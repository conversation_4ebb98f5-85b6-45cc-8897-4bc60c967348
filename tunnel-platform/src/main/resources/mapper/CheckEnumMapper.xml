<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckEnumMapper">


    <sql id="selectCheckEnumVo">
        select id, check_type, check_project, check_content, question_type,score,fixed_time,type
         , create_time, update_time from sc_check_enum
    </sql>

    <select id="selectCheckEnumList" parameterType="CheckEnum" resultType="CheckEnum">
        <include refid="selectCheckEnumVo"/>
        <where>  
            <if test="checkType != null  and checkType != ''"> and check_type = #{checkType}</if>
            <if test="checkProject != null  and checkProject != ''"> and check_project = #{checkProject}</if>
            <if test="checkContent != null  and checkContent != ''"> and check_content = #{checkContent}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="type != null"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectCheckEnumById" parameterType="Long" resultType="CheckEnum">
        <include refid="selectCheckEnumVo"/>
        where id = #{id}
    </select>
    <select id="distinctListByType" resultType="java.lang.String">
        select distinct  ${column},min(id) b
        from sc_check_enum
        <where>
            <if test="checkType != null and checkType != ''">and check_type=#{checkType}</if>
            <if test="checkProject != null and checkProject != ''">and check_project=#{checkProject}</if>
            <if test="checkContent != null and checkContent != ''">and check_content=#{checkContent}</if>
            <if test="questionType != null and questionType != ''">and question_type=#{questionType}</if>
            <if test="type != null"> and type = #{type}</if>
        </where>
        group by ${column}
        order by b asc
    </select>
    <select id="selectListByIds" resultType="com.tunnel.domain.CheckEnum">
        select * from sc_check_enum
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectCheckEnumListByList" resultType="com.tunnel.domain.CheckEnum">
        select * from sc_check_enum
        where (check_type,check_project,check_content,question_type) in
        <foreach item="item" collection="checkEnumList" open="(" separator="," close=")">
            (#{item.checkType},#{item.checkProject},#{item.checkContent},#{item.questionType})
        </foreach>
    </select>
    <select id="queryByCheckTypeAndCheckProject" resultType="com.tunnel.domain.CheckEnum">
        select * from sc_check_enum
        where check_type=#{checkType}  and check_project=#{checkProject}
        limit 1
    </select>
    <select id="queryDefaultScoreList" resultType="java.math.BigDecimal">
        select distinct score from sc_check_enum
        where check_type=#{checkType}  and check_project=#{checkProject}
        and check_content=#{checkContent} and question_type=#{questionType}
        limit 1
    </select>
    <select id="selectCheckEnumByIdList" resultType="com.tunnel.domain.CheckEnum">
        select * from sc_check_enum
        where id in
        <foreach item="item" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listDistinctCheckContent" resultType="com.tunnel.domain.CheckEnum">
        select distinct check_type,check_project,check_content
        from sc_check_enum
    </select>

    <select id="selectGroupByParams" resultType="com.tunnel.domain.CheckEnum">
        select check_type,check_project,check_content,sum(score) score
        from sc_check_enum
        where id in
        <foreach item="item" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by check_type,check_project,check_content
    </select>
    <select id="queryDefaultEnvScoreList" resultType="java.math.BigDecimal">
        SELECT DISTINCT score from sc_check_enum
        WHERE id in
        (
            SELECT env_item_id from sc_check_enum_relation
            WHERE road_item_id in
                  (select id from sc_check_enum
                   where check_type=#{checkType}  and check_project=#{checkProject}
                     and check_content=#{checkContent} and question_type=#{questionType})
        )
        limit 1
    </select>
    <select id="distinctListScoreAll" resultType="java.math.BigDecimal">
        select distinct score
        from sc_check_enum
        order by score asc
    </select>

    <insert id="insertCheckEnum" parameterType="CheckEnum" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_enum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">check_type,</if>
            <if test="checkProject != null and checkProject != ''">check_project,</if>
            <if test="checkContent != null and checkContent != ''">check_content,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="fixedTime != null">fixed_time,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">#{checkType},</if>
            <if test="checkProject != null and checkProject != ''">#{checkProject},</if>
            <if test="checkContent != null and checkContent != ''">#{checkContent},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="fixedTime != null">#{fixedTime},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCheckEnum" parameterType="CheckEnum">
        update sc_check_enum
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">check_type = #{checkType},</if>
            <if test="checkProject != null and checkProject != ''">check_project = #{checkProject},</if>
            <if test="checkContent != null and checkContent != ''">check_content = #{checkContent},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="fixedTime != null">fixed_time = #{fixedTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckEnumById" parameterType="Long">
        delete from sc_check_enum where id = #{id}
    </delete>

    <delete id="deleteCheckEnumByIds" parameterType="String">
        delete from sc_check_enum where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>