<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadMapper">

    <sql id="selectScRoadVo">
        select id,code, road_code, road_name, company_name,company, start_code,
               end_code, mileage, remark, create_time, update_time, creator, modifier,
               is_available, is_deleted, version_no, team_no,match_company_code,match_company_name,notify_phone,notify_user,
               wuhan, important
        from sc_road
    </sql>

    <select id="selectScRoadList" parameterType="com.tunnel.domain.Road" resultType="com.tunnel.domain.Road">
        select t.* from sc_road t
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        <where>
            <if test="code != null  and code != ''">and t.code = #{code}</if>
            <if test="roadCode != null  and roadCode != ''">and t.road_code = #{roadCode}</if>
            <if test="roadName != null  and roadName != ''">and t.road_name like concat('%', #{roadName}, '%')</if>
            <if test="companyName != null  and companyName != ''">and t.company_name like concat('%', #{companyName},
                '%')
            </if>
            <if test="startCode != null  and startCode != ''">and t.start_code = #{startCode}</if>
            <if test="endCode != null  and endCode != ''">and t.end_code = #{endCode}</if>
            <if test="mileage != null ">and t.mileage = #{mileage}</if>
            <if test="teamNo != null  and teamNo != ''">and t.team_no = #{teamNo}</if>
            <if test="wuhan != null">and t.wuhan = #{wuhan}</if>
            <if test="important != null">and t.important = #{important}</if>
            ${params.dataScope}
        </where>
        ORDER BY FIELD(t.company_name,
        '湖北交投鄂东高速公路运营管理有限公司',
        '湖北交投鄂西高速公路运营管理有限公司',
        '湖北交投鄂西北高速公路运营管理有限公司',
        '湖北交投江汉高速公路运营管理有限公司',
        '湖北交投襄阳高速公路运营管理有限公司',
        '湖北交投京珠高速公路运营管理有限公司',
        '湖北交投随岳高速公路运营管理有限公司',
        '湖北交投武黄高速公路运营管理有限公司',
        '湖北交投宜昌高速公路运营管理有限公司',
        '湖北交投实业发展有限公司',
        '湖北楚天智能交通股份有限公司',
        '湖北联合交通投资开发有限公司',
        '中交投资（湖北）运营管理有限公司',
        '中交资产管理有限公司湖北区域管理总部',
        '湖北武荆高速公路发展有限公司',
        '武汉交投高速公路运营管理有限公司',
        '湖北武麻高速公路有限公司',
        '越秀（湖北）高速公路有限公司',
        '湖北随岳南高速公路有限公司',
        '葛洲坝湖北襄荆高速公路有限公司',
        '湖北荆宜高速公路有限公司',
        '荆州市路桥投资开发有限公司',
        '湖北荆东高速公路建设开发有限公司',
        '湖北鄂东长江公路大桥有限公司',
        '湖北汉洪东荆河桥梁建设管理有限公司',
        '湖北汉孝高速公路建设经营有限公司',
        '湖北樊魏高速公路有限公司',
        '武汉华益路桥管理有限公司',
        '湖北老谷高速公路开发有限公司',
        '武汉青山长江大桥建设有限公司',
        '宜昌长江大桥建设营运集团有限公司',
        '湖北黄石武阳高速公路发展有限公司',
        '武汉市武阳高速公路投资管理有限公司',
        '汉江国投湖北高速公路发展有限公司'
        )
    </select>
    
    <select id="selectScRoadById" parameterType="Long" resultType="com.tunnel.domain.Road">
        <include refid="selectScRoadVo"/>
        where id = #{id}
    </select>
    <select id="selectAll" resultType="com.tunnel.domain.Road">
        select * from sc_road
        where is_deleted=0
    </select>
    <select id="selectStatInfo" resultType="com.tunnel.domain.stat.RoadStat">
        select count(DISTINCT code) totalCount,sum(mileage) totalMileage,count(distinct company_name)companyCount
        from sc_road
        where is_deleted=0
    </select>
    <select id="listAllCompany" resultType="com.tunnel.domain.Road">
        SELECT DISTINCT t.company_name, t.company
        FROM sc_road t
        LEFT JOIN sys_user us ON t.user_id = us.user_id
        LEFT JOIN sys_dept de ON t.dept_id = de.dept_id
        <where>
            t.is_deleted = 0
            ${params.dataScope}
        </where>
        ORDER BY FIELD(t.company_name,
        '湖北交投鄂东高速公路运营管理有限公司',
        '湖北交投鄂西高速公路运营管理有限公司',
        '湖北交投鄂西北高速公路运营管理有限公司',
        '湖北交投江汉高速公路运营管理有限公司',
        '湖北交投襄阳高速公路运营管理有限公司',
        '湖北交投京珠高速公路运营管理有限公司',
        '湖北交投随岳高速公路运营管理有限公司',
        '湖北交投武黄高速公路运营管理有限公司',
        '湖北交投宜昌高速公路运营管理有限公司',
        '湖北交投实业发展有限公司',
        '湖北楚天智能交通股份有限公司',
        '湖北联合交通投资开发有限公司',
        '中交投资（湖北）运营管理有限公司',
        '中交资产管理有限公司湖北区域管理总部',
        '湖北武荆高速公路发展有限公司',
        '武汉交投高速公路运营管理有限公司',
        '湖北武麻高速公路有限公司',
        '越秀（湖北）高速公路有限公司',
        '湖北随岳南高速公路有限公司',
        '葛洲坝湖北襄荆高速公路有限公司',
        '湖北荆宜高速公路有限公司',
        '荆州市路桥投资开发有限公司',
        '湖北荆东高速公路建设开发有限公司',
        '湖北鄂东长江公路大桥有限公司',
        '湖北汉洪东荆河桥梁建设管理有限公司',
        '湖北汉孝高速公路建设经营有限公司',
        '湖北樊魏高速公路有限公司',
        '武汉华益路桥管理有限公司',
        '湖北老谷高速公路开发有限公司',
        '武汉青山长江大桥建设有限公司',
        '宜昌长江大桥建设营运集团有限公司',
        '湖北黄石武阳高速公路发展有限公司',
        '武汉市武阳高速公路投资管理有限公司',
        '汉江国投湖北高速公路发展有限公司'
        )
    </select>
    <select id="selectByRoadIdList" resultType="com.tunnel.domain.Road">
        select * from sc_road where id in
        <foreach collection="roadIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="insertScRoad" parameterType="com.tunnel.domain.Road" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="roadCode != null and roadCode != ''">road_code,</if>
            <if test="roadName != null and roadName != ''">road_name,</if>
            <if test="companyName != null">company_name,</if>
            <if test="company != null">company,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="mileage != null">mileage,</if>
            <if test="wuhan != null">wuhan,</if>
            <if test="important != null">important,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="teamNo != null">team_no,</if>
            <if test="matchCompanyCode != null">match_company_code,</if>
            <if test="matchCompanyName != null">match_company_name,</if>
            <if test="notifyPhone != null">notify_phone,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="roadCode != null and roadCode != ''">#{roadCode},</if>
            <if test="roadName != null and roadName != ''">#{roadName},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="company != null">#{company},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="wuhan != null">#{wuhan},</if>
            <if test="important != null">#{important},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="teamNo != null">#{teamNo},</if>
            <if test="matchCompanyCode != null">#{matchCompanyCode},</if>
            <if test="matchCompanyName != null">#{matchCompanyName},</if>
            <if test="notifyPhone != null">#{notifyPhone},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>
    <insert id="batchAdd">
        insert into sc_road
        (code,road_code,road_name,company_name,company,start_code,end_code,mileage,remark,creator,team_no,match_company_code,match_company_name,notify_phone,notify_user,wuhan,important,user_id,dept_id
        )
        values
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.code},#{item.roadCode},#{item.roadName},#{item.companyName},#{item.company},#{item.startCode}
            ,#{item.endCode},#{item.mileage},#{item.remark},#{item.creator},#{item.teamNo},#{item.matchCompanyCode},#{item.matchCompanyName},#{item.notifyPhone},#{item.notifyUser},#{item.wuhan},#{item.important},#{item.userId},#{item.deptId}
            )
        </foreach>
    </insert>

    <update id="updateScRoad" parameterType="com.tunnel.domain.Road">
        update sc_road
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="roadCode != null and roadCode != ''">road_code = #{roadCode},</if>
            <if test="roadName != null and roadName != ''">road_name = #{roadName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="company != null">company = #{company},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="wuhan != null">wuhan = #{wuhan},</if>
            <if test="important != null">important = #{important},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="teamNo != null">team_no = #{teamNo},</if>
            <if test="matchCompanyCode != null">match_company_code = #{matchCompanyCode},</if>
            <if test="matchCompanyName != null">match_company_name = #{matchCompanyName},</if>
            <if test="notifyPhone != null">notify_phone = #{notifyPhone},</if>
            <if test="notifyUser != null">notify_user = #{notifyUser},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScRoadById" parameterType="Long">
        delete from sc_road where id = #{id}
    </delete>

    <delete id="deleteScRoadByIds" parameterType="String">
        delete from sc_road where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryNoticeUser"  parameterType="com.tunnel.domain.dto.ScRoadQueryDTO" resultType="com.tunnel.domain.Road">
        select distinct company_name, road_name, notify_user, notify_phone from sc_road where is_deleted = 0 and is_available = 1
        and (company_name, road_name) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.companyName}, #{item.roadName})
        </foreach>
    </select>
    <select id="selectByCompanyNameList" resultType="com.tunnel.domain.Road">
        select company,company_name from sc_road
         where company_name in
        <foreach item="item" collection="companyList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectDistinctCompanyNameList" resultType="com.tunnel.domain.Road">
        SELECT DISTINCT company, company_name
        FROM sc_road
        WHERE company_name IS NOT NULL
        ORDER BY FIELD(company_name,'湖北交投鄂东高速公路运营管理有限公司','湖北交投鄂西高速公路运营管理有限公司','湖北交投鄂西北高速公路运营管理有限公司','湖北交投江汉高速公路运营管理有限公司','湖北交投襄阳高速公路运营管理有限公司','湖北交投京珠高速公路运营管理有限公司','湖北交投随岳高速公路运营管理有限公司','湖北交投武黄高速公路运营管理有限公司','湖北交投宜昌高速公路运营管理有限公司','湖北交投实业发展有限公司','湖北楚天智能交通股份有限公司','湖北联合交通投资开发有限公司','中交投资（湖北）运营管理有限公司','中交资产管理有限公司湖北区域管理总部','湖北武荆高速公路发展有限公司','武汉交投高速公路运营管理有限公司','湖北武麻高速公路有限公司','越秀（湖北）高速公路有限公司','湖北随岳南高速公路有限公司','葛洲坝湖北襄荆高速公路有限公司','湖北荆宜高速公路有限公司','荆州市路桥投资开发有限公司','湖北荆东高速公路建设开发有限公司','湖北鄂东长江公路大桥有限公司','湖北汉洪东荆河桥梁建设管理有限公司','湖北汉孝高速公路建设经营有限公司','湖北樊魏高速公路有限公司','武汉华益路桥管理有限公司','湖北老谷高速公路开发有限公司','武汉青山长江大桥建设有限公司','宜昌长江大桥建设营运集团有限公司','湖北黄石武阳高速公路发展有限公司','武汉市武阳高速公路投资管理有限公司','汉江国投湖北高速公路发展有限公司')

    </select>

</mapper>