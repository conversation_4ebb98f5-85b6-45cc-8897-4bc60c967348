<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskNormalMapper">

    <insert id="batchAdd">
        INSERT INTO sc_task_normal
        (
        task_date,
        company_name,
        road_name,
        check_type,
        check_project,
        check_content,
        question_type,
        is_fixed,
        creator,
        user_id,
        dept_id
        )
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.taskDate},
            #{item.companyName},
            #{item.roadName},
            #{item.checkType},
            #{item.checkProject},
            #{item.checkContent},
            #{item.questionType},
            #{item.isFixed},
            #{item.creator},
            #{item.userId},
            #{item.deptId}
            )
        </foreach>
    </insert>

    <select id="selectByTaskDateOrCompany" resultType="com.tunnel.domain.ScTaskNormal">
        select * from sc_task_normal n
        left join sys_user us on n.user_id = us.user_id
        left join sys_dept de on n.dept_id = de.dept_id
                 where n.is_deleted = 0 and n.is_available = 1
        <if test="companyName != null and companyName != ''">
            and n.company_name = #{companyName}
        </if>
        <if test="taskDate != null">
            and n.task_date = #{taskDate}
        </if>
        ${params.dataScope}
        order by n.create_time desc
    </select>

    <select id="queryByIdList" resultType="com.tunnel.domain.ScTaskNormal">
        select * from sc_task_normal
                 where is_deleted = 0 and is_available = 1 and id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryCountByTaskDate" resultType="java.lang.Integer">
        select count(1) from sc_task_normal
            where is_deleted = 0 and is_available = 1 and task_date = #{taskDate}
    </select>

    <delete id="deleteByTaskDate">
    delete from sc_task_normal
        where is_deleted = 0 and is_available = 1 and task_date = #{taskDate}
    </delete>
</mapper>