<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskDetailFixedMapper">
    
    <sql id="selectTaskDetailFixedVo">
        select id, task_detail_id, after_fix_pic, check_project, fix_desc,
        fix_people, fix_time, is_confirm, confirm_pinion, confirm_people, confirm_time,
        remark, create_time, update_time, creator, modifier, is_available, is_deleted, version_no
        from sc_task_detail_fixed
    </sql>

    <select id="selectTaskDetailFixedList" parameterType="com.tunnel.domain.TaskDetailFixed" resultType="com.tunnel.domain.TaskDetailFixed">
        <include refid="selectTaskDetailFixedVo"/>
        <where>
            is_deleted = 0
            <if test="taskDetailId != null">
                AND task_detail_id = #{taskDetailId}
            </if>
            <if test="checkProject != null and checkProject != ''">
                AND check_project = #{checkProject}
            </if>
            <if test="isConfirm != null">
                AND is_confirm = #{isConfirm}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTaskDetailFixedByDetailId" parameterType="Long" resultType="com.tunnel.domain.TaskDetailFixed">
        <include refid="selectTaskDetailFixedVo"/>
        where task_detail_id = #{detailId} and is_deleted = 0
    </select>

    <insert id="insertTaskDetailFixed" parameterType="com.tunnel.domain.TaskDetailFixed" useGeneratedKeys="true" keyProperty="id">
        insert into sc_task_detail_fixed (
            task_detail_id, after_fix_pic, check_project, fix_desc,
            fix_people, fix_time, is_confirm, confirm_pinion, confirm_people, confirm_time,
            remark, creator, modifier
        ) values (
            #{taskDetailId}, #{afterFixPic}, #{checkProject}, #{fixDesc},
            #{fixPeople}, #{fixTime}, #{isConfirm}, #{confirmPinion}, #{confirmPeople}, #{confirmTime},
            #{remark}, #{creator}, #{modifier}
        )
    </insert>

    <update id="updateTaskDetailFixed" parameterType="com.tunnel.domain.TaskDetailFixed">
        update sc_task_detail_fixed
        <set>
            <if test="afterFixPic != null">after_fix_pic = #{afterFixPic},</if>
            <if test="checkProject != null">check_project = #{checkProject},</if>
            <if test="fixDesc != null">fix_desc = #{fixDesc},</if>
            <if test="fixPeople != null">fix_people = #{fixPeople},</if>
            <if test="fixTime != null">fix_time = #{fixTime},</if>
            <if test="isConfirm != null">is_confirm = #{isConfirm},</if>
            <if test="confirmPinion != null">confirm_pinion = #{confirmPinion},</if>
            <if test="confirmPeople != null">confirm_people = #{confirmPeople},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            version_no = version_no + 1
        </set>
        where id = #{id} and is_deleted = 0
    </update>

    <update id="deleteTaskDetailFixedByIds">
        update sc_task_detail_fixed set is_deleted = 1
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </update>

    <select id="selectByIdList" resultType="com.tunnel.domain.TaskDetailFixed">
        select * from sc_task_detail_fixed where is_deleted = 0 and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateTaskDetailFixed" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update sc_task_detail_fixed
            <set>
                <if test="item.afterFixPic != null">after_fix_pic = #{item.afterFixPic},</if>
                <if test="item.checkProject != null">check_project = #{item.checkProject},</if>
                <if test="item.fixDesc != null">fix_desc = #{item.fixDesc},</if>
                <if test="item.fixPeople != null">fix_people = #{item.fixPeople},</if>
                <if test="item.fixTime != null">fix_time = #{item.fixTime},</if>
                <if test="item.isConfirm != null">is_confirm = #{item.isConfirm},</if>
                <if test="item.confirmPinion != null">confirm_pinion = #{item.confirmPinion},</if>
                <if test="item.confirmPeople != null">confirm_people = #{item.confirmPeople},</if>
                <if test="item.confirmTime != null">confirm_time = #{item.confirmTime},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.modifier != null">modifier = #{item.modifier},</if>
                version_no = version_no + 1
            </set>
            where id = #{item.id} and is_deleted = 0
        </foreach>
    </update>
</mapper>