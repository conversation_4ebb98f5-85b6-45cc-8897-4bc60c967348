<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckEnumScoreMapper">
    
    <resultMap type="CheckEnumScore" id="CheckEnumScoreResult">
        <result property="id"    column="id"    />
        <result property="checkType"    column="check_type"    />
        <result property="checkProject"    column="check_project"    />
        <result property="checkContent"    column="check_content"    />
        <result property="score"    column="score"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCheckEnumScoreVo">
        select id, check_type, check_project, check_content, score, create_time, update_time from sc_check_enum_score
    </sql>

    <select id="selectCheckEnumScoreList" parameterType="CheckEnumScore" resultMap="CheckEnumScoreResult">
        <include refid="selectCheckEnumScoreVo"/>
        <where>  
            <if test="checkType != null  and checkType != ''"> and check_type = #{checkType}</if>
            <if test="checkProject != null  and checkProject != ''"> and check_project = #{checkProject}</if>
            <if test="checkContent != null  and checkContent != ''"> and check_content = #{checkContent}</if>
        </where>
    </select>
    
    <select id="selectCheckEnumScoreById" parameterType="Long" resultMap="CheckEnumScoreResult">
        <include refid="selectCheckEnumScoreVo"/>
        where id = #{id}
    </select>
    <select id="selectByCheckEnumScore" resultType="com.tunnel.domain.CheckEnumScore">
        select * from sc_check_enum_score
        where
        (check_type,check_project,check_content) in
        <foreach item="item" collection="detailList" open="(" separator="," close=")">
              (#{item.checkType},#{item.checkProject},#{item.checkContent})
        </foreach>
    </select>
    <select id="selectAllList" resultType="com.tunnel.domain.CheckEnum">
        select * from sc_check_enum_score
        order by id asc
    </select>

    <insert id="insertCheckEnumScore" parameterType="CheckEnumScore" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_enum_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">check_type,</if>
            <if test="checkProject != null and checkProject != ''">check_project,</if>
            <if test="checkContent != null and checkContent != ''">check_content,</if>
            <if test="score != null">score,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">#{checkType},</if>
            <if test="checkProject != null and checkProject != ''">#{checkProject},</if>
            <if test="checkContent != null and checkContent != ''">#{checkContent},</if>
            <if test="score != null">#{score},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCheckEnumScore" parameterType="CheckEnumScore">
        update sc_check_enum_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkType != null and checkType != ''">check_type = #{checkType},</if>
            <if test="checkProject != null and checkProject != ''">check_project = #{checkProject},</if>
            <if test="checkContent != null and checkContent != ''">check_content = #{checkContent},</if>
            <if test="score != null">score = #{score},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckEnumScoreById" parameterType="Long">
        delete from sc_check_enum_score where id = #{id}
    </delete>

    <delete id="deleteCheckEnumScoreByIds" parameterType="String">
        delete from sc_check_enum_score where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>