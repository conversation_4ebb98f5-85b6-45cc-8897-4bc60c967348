<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskDetailMapper">

    <sql id="selectScTaskDetailVo">
        select id,
               task_id,
               road_id,
               item_id,
               env_detail_id,
               source_type,
               check_content,
               question_type,
               check_code,
               name,
               city,
               direction,
               start_check_code,
               end_check_code,
               check_num,
               check_num_unit,
               score,
               is_fcous,
               work_pic,
               center_pic,
               side_pic,
               scene_pic1,
               scene_pic2,
               scene_pic3,
               longitude,
               latitude,
               address,
               is_fixed,
               remark,
               create_time,
               update_time,
               creator,
               modifier,
               is_available,
               is_deleted,
               version_no
        from sc_task_detail
    </sql>

    <select id="selectScTaskDetailList" parameterType="com.tunnel.domain.TaskDetail"
            resultType="com.tunnel.domain.TaskDetail">
        select d.*,t.code,t.road_code,t.road_name,t.company_name,t.start_code,t.end_code,t.mileage,t.task_date
        from sc_task_detail d
        inner join sc_task t on t.id = d.task_id
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        <where>
            <if test="roadCodeOrName != null and roadCodeOrName != ''">
                and (t.road_code = #{roadCodeOrName} or t.road_name like concat('%', #{roadCodeOrName}, '%') )
            </if>
            <if test="roadCode != null  and roadCode != ''">and t.road_code = #{roadCode}</if>
            <if test="roadName != null  and roadName != ''">and t.road_name = #{roadName}</if>
            <if test="type != null">and d.type = #{type}</if>
            <if test="code != null and code != ''">and t.code = #{code}</if>
            <if test="id != null ">and d.id = #{id}</if>
            <if test="taskId != null ">and d.task_id = #{taskId}</if>
            <if test="itemId != null  and itemId != ''">and d.item_id = #{itemId}</if>
            <if test="name != null  and name != ''">and d.name like concat('%', #{name}, '%')</if>
            <if test="direction != null ">and d.direction = #{direction}</if>
            <if test="startCheckCode != null  and startCheckCode != ''">and d.start_check_code = #{startCheckCode}</if>
            <if test="endCheckCode != null  and endCheckCode != ''">and d.end_check_code = #{endCheckCode}</if>
            <if test="checkNumUnit != null  and checkNumUnit != ''">and d.check_num_unit = #{checkNumUnit}</if>
            <if test="isFcous != null ">and d.is_fcous = #{isFcous}</if>
            <if test="creator != null ">and d.creator = #{creator}</if>
            <if test="modifier != null ">and d.modifier = #{modifier}</if>
            <if test="sourceType != null ">and d.source_type = #{sourceType}</if>
            <if test="taskDate != null ">and t.task_date like concat(left(#{taskDate},7),'%') </if>
            <if test="companyName != null ">and t.company_name = #{companyName}</if>
            <if test="checkType != null and checkType !=''">and d.check_type = #{checkType}</if>
            <if test="checkProject != null and checkProject !=''">and d.check_project = #{checkProject}</if>
            <if test="isFixed != null">and d.is_fixed = #{isFixed}</if>
            <if test="searchStartTime != null and searchEndTime != null">and d.fixed_end_date &gt;= #{searchStartTime}
                and d.fixed_end_date &lt;= #{searchEndTime}
            </if>
            <if test="taskIdList != null and taskIdList.size() > 0">
                and d.task_id in
                <foreach item="taskId" collection="taskIdList" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
            and d.check_content != '无问题'
            <if test="type == null">
                and d.check_type != '路域环境检查'
            </if>
            ${params.dataScope}
        </where>
        order by d.id desc
    </select>

    <select id="selectScTaskDetailById" parameterType="Long" resultType="com.tunnel.domain.TaskDetail">
        select *
        from sc_task_detail
        where id = #{id}
    </select>
    <select id="selectScTaskDetailByIdList" parameterType="Long" resultType="com.tunnel.domain.TaskDetail">
        select * from sc_task_detail
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByTaskId" resultType="com.tunnel.domain.TaskDetail">
        select * from sc_task_detail d
        where d.task_id=#{taskId}
        <if test="type != null ">and d.type = #{type}</if>
        <if test="direction != null ">and d.direction = #{direction}</if>
        <if test="city != null ">and d.city = #{city}</if>
        <if test="startCheckCode != null  and startCheckCode != ''">and d.start_check_code = #{startCheckCode}</if>
        <if test="endCheckCode != null  and endCheckCode != ''">and d.end_check_code = #{endCheckCode}</if>
        <if test="creator != null ">and d.creator = #{creator}</if>
    </select>
    <select id="listBaseInfoByTaskIdList" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        SELECT GROUP_CONCAT(DISTINCT name SEPARATOR ',')
        feeArea,task_id,check_project,direction,min(start_check_code)start_check_code,max(end_check_code)end_check_code
        from sc_task_detail
        WHERE check_project in ('服务区','隧道出入口','互通区及收费站')
        and type=#{type}
        and (task_id,direction,start_check_code,end_check_code) in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            (#{item.taskId},#{item.direction},#{item.startCheckCode},#{item.endCheckCode})
        </foreach>
        GROUP BY check_project,task_id,direction;
    </select>
    <select id="sumCheckMileage" resultType="java.math.BigDecimal">
        select sum(end_check_code - start_check_code)
        from sc_task_detail d
        inner join sc_task t on d.task_id=t.id
        where left(t.task_date, 7)=#{taskDate}
        <if test="type != null">
            and d.type = #{type}
        </if>
    </select>
    <select id="selectGroupByTaskDate" resultType="com.tunnel.domain.TaskDetail">
        SELECT check_project checkContent,count(d.id)score
        from sc_task_detail d
        INNER JOIN sc_task t on t.id=d.task_id
        inner join sc_road r on r.id=d.road_id
        where d.check_content!='无问题'
        and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
        '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
        <if test="createTime !=null">
            and d.create_time >= #{createTime}
        </if>
        <if test="startDate != null and endDate!=null">
            and left(t.task_date,7) between #{startDate} and #{endDate}
        </if>
        <if test="checkType != null">
            and d.type = #{checkType}
        </if>
        <if test="taskDate!=null">
            and left(t.task_date, 7)=#{taskDate}
        </if>
        <if test="type != null">
            and d.type = #{type}
        </if>
        <if test="roadId != null">
            and d.road_id = #{roadId}
        </if>
        <if test="company != null">
            and r.company = #{company}
        </if>
        <if test="wuhan != null">
            and r.wuhan = #{wuhan}
        </if>
        <if test="important != null">
            and r.important = #{important}
        </if>
        <if test="checkProjectType != null">
            <choose>
                <when test="checkProjectType == 1">
                    and d.check_project in ('服务区')
                </when>
                <otherwise>
                    and d.check_project not in ('服务区')
                </otherwise>
            </choose>
        </if>
        GROUP BY check_project
        order by score desc
    </select>
    <select id="selectByTaskDetail" resultType="java.lang.Integer">
        select count(1)
        from sc_task_detail
        where task_id = #{taskId}
          and check_type = #{checkType}
          and check_project = #{checkProject}
          and check_content = #{checkContent}
          and question_type = #{questionType}
          and check_code = #{checkCode}
    </select>
    <select id="selectCountByTaskDate" resultType="com.tunnel.domain.stat.QuestionStat">
        select count(1) as totalCount,
        sum(case when is_fixed=1 then 1 else 0 end) as checkedCount,
        sum(case when is_fixed=0 then 1 else 0 end) as needCheckCount
        from sc_task_detail d
        inner join sc_road r on r.id=d.road_id
        inner join sc_task t on t.id=d.task_id
        <where>
            and d.source_type=0
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
            '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            and d.check_content!='无问题'
            <if test="createTime !=null">
                and d.create_time >= #{createTime}
            </if>
            <if test="startDate != null and endDate!=null">
                and left(t.task_date,7) between #{startDate} and #{endDate}
            </if>
            <if test="checkType != null">
                and d.type = #{checkType}
            </if>
            <if test="roadId != null">
                and d.road_id = #{roadId}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
            <if test="checkProjectType != null">
                <choose>
                    <when test="checkProjectType == 1">
                        and d.check_project in ('服务区')
                    </when>
                    <otherwise>
                        and d.check_project not in ('服务区')
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="selectQuestionGroupByTaskDate" resultType="com.tunnel.domain.stat.ZhuStat">
        SELECT check_content name,count(d.id) questionCount
        from sc_task_detail d
        INNER JOIN sc_road r on r.id=d.road_id
        inner join sc_task t on t.id=d.task_id
        <where>
            <if test="createTime !=null">
                and d.create_time >= #{createTime}
            </if>
            <if test="startDate != null and endDate!=null">
                and left(t.task_date,7) between #{startDate} and #{endDate}
            </if>
            and d.source_type=0
            and d.check_content!='无问题'
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
            '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            <if test="checkType != null">
                and d.type = #{checkType}
            </if>
            <if test="roadId != null">
                and d.road_id = #{roadId}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
            <if test="checkProjectType != null">
                <choose>
                    <when test="checkProjectType == 1">
                        and d.check_project in ('服务区')
                    </when>
                    <otherwise>
                        and d.check_project not in ('服务区')
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY check_content
        order by questionCount desc
    </select>
    <select id="selectCountByCompanyName" resultType="com.tunnel.domain.TaskDetail">
        select r.company_name,r.company,count(*)num,sum(case when is_fixed=0 then 1 else 0 end)needFixCount
        from sc_task_detail d
        inner join sc_road r on d.road_id=r.id
        inner join sc_task t on t.id=d.task_id
        <where>
            <if test="createTime !=null">
                and d.create_time >= #{createTime}
            </if>
            <if test="startDate != null and endDate!=null">
                and left(t.task_date,7) between #{startDate} and #{endDate}
            </if>
            and d.source_type=0
            and d.check_content!='无问题'
            <if test="checkType != null">
                and d.type = #{checkType}
            </if>
            <if test="roadId != null">
                and d.road_id = #{roadId}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
            <if test="checkProjectType != null">
                <choose>
                    <when test="checkProjectType == 1">
                        and d.check_project in ('服务区')
                    </when>
                    <otherwise>
                        and d.check_project not in ('服务区')
                    </otherwise>
                </choose>
            </if>
        </where>
        group by r.company_name,r.company
        order by num desc
    </select>
    <select id="selectCountByRoad" resultType="com.tunnel.domain.TaskDetail">
        select r.company_name, r.company, d.road_id, r.road_code, r.road_name, count(*)num
        from sc_task_detail d
        inner join sc_road r on d.road_id=r.id
        inner join sc_task t on t.id=d.task_id
        <where>
            t.is_deleted = 0
            and t.is_available = 1
            and d.is_deleted = 0
            and d.is_available = 1
            and d.check_type != '路域环境检查'
            <if test="createTime !=null">
                and d.create_time >= #{createTime}
            </if>
            <if test="startDate != null and endDate!=null">
                and left(t.task_date,7) between #{startDate} and #{endDate}
            </if>
            and d.check_content!='无问题'
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
            '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            <if test="checkType != null">
                and d.type = #{checkType}
            </if>
            <if test="roadId != null">
                and d.road_id = #{roadId}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
            <if test="checkProjectType != null">
                <choose>
                    <when test="checkProjectType == 1">
                        and d.check_project in ('服务区')
                    </when>
                    <otherwise>
                        and d.check_project not in ('服务区')
                    </otherwise>
                </choose>
            </if>
        </where>
        group by r.company_name,d.road_id
        order by num desc
    </select>

    <insert id="insertScTaskDetail" parameterType="com.tunnel.domain.TaskDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into sc_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="roadId != null">road_id,</if>
            <if test="itemId != null and itemId != ''">item_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="type != null">type,</if>
            <if test="checkType != null">check_type,</if>
            <if test="checkProject != null">check_project,</if>
            <if test="checkContent != null">check_content,</if>
            <if test="questionType != null">question_type,</if>
            <if test="checkCode != null">check_code,</if>
            <if test="name != null">name,</if>
            <if test="city != null">city,</if>
            <if test="direction != null">direction,</if>
            <if test="startCheckCode != null">start_check_code,</if>
            <if test="endCheckCode != null">end_check_code,</if>
            <if test="checkNum != null">check_num,</if>
            <if test="checkNumUnit != null and checkNumUnit != ''">check_num_unit,</if>
            <if test="score != null">score,</if>
            <if test="isFcous != null">is_fcous,</if>
            <if test="isRoadCheck != null">is_road_check,</if>
            <if test="roadCheckScore != null">road_check_score,</if>
            <if test="workPic != null and workPic != ''">work_pic,</if>
            <if test="centerPic != null and centerPic != ''">center_pic,</if>
            <if test="sidePic != null and sidePic != ''">side_pic,</if>
            <if test="scenePic1 != null and scenePic1 != ''">scene_pic1,</if>
            <if test="scenePic2 != null and scenePic2 != ''">scene_pic2,</if>
            <if test="scenePic3 != null and scenePic3 != ''">scene_pic3,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="isFixed != null and isFixed !=''">is_fixed,</if>
            <if test="fixedEndDate != null">fixed_end_date,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="roadId != null">#{roadId},</if>
            <if test="itemId != null and itemId != ''">#{itemId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="type != null">#{type},</if>
            <if test="checkType != null">#{checkType},</if>
            <if test="checkProject != null">#{checkProject},</if>
            <if test="checkContent != null">#{checkContent},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="checkCode != null">#{checkCode},</if>
            <if test="name != null">#{name},</if>
            <if test="city != null">#{city},</if>
            <if test="direction != null">#{direction},</if>
            <if test="startCheckCode != null">#{startCheckCode},</if>
            <if test="endCheckCode != null">#{endCheckCode},</if>
            <if test="checkNum != null">#{checkNum},</if>
            <if test="checkNumUnit != null and checkNumUnit != ''">#{checkNumUnit},</if>
            <if test="score != null">#{score},</if>
            <if test="isFcous != null">#{isFcous},</if>
            <if test="isRoadCheck != null">#{isRoadCheck},</if>
            <if test="roadCheckScore != null">#{roadCheckScore},</if>
            <if test="workPic != null and workPic != ''">#{workPic},</if>
            <if test="centerPic != null and centerPic != ''">#{centerPic},</if>
            <if test="sidePic != null and sidePic != ''">#{sidePic},</if>
            <if test="scenePic1 != null and scenePic1 != ''">#{scenePic1},</if>
            <if test="scenePic2 != null and scenePic2 != ''">#{scenePic2},</if>
            <if test="scenePic3 != null and scenePic3 != ''">#{scenePic3},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="isFixed != null and isFixed !=''">#{isFixed},</if>
            <if test="fixedEndDate != null">#{fixedEndDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
        </trim>
    </insert>

    <update id="updateScTaskDetail" parameterType="com.tunnel.domain.TaskDetail">
        update sc_task_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="itemId != null and itemId != ''">item_id = #{itemId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="type != null">type = #{type},</if>
            check_type = #{checkType},
            check_project = #{checkProject},
            check_content = #{checkContent},
            question_type = #{questionType},
            check_code = #{checkCode},
            name = #{name},
            <if test="city != null">city = #{city},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="startCheckCode != null and startCheckCode != ''">start_check_code = #{startCheckCode},</if>
            <if test="endCheckCode != null and endCheckCode != ''">end_check_code = #{endCheckCode},</if>
            <if test="checkNum != null">check_num = #{checkNum},</if>
            <if test="checkNumUnit != null and checkNumUnit != ''">check_num_unit = #{checkNumUnit},</if>
            <if test="score != null">score = #{score},</if>
            <if test="isFcous != null">is_fcous = #{isFcous},</if>
            <if test="isRoadCheck != null">is_road_check = #{isRoadCheck},</if>
            <if test="roadCheckScore != null">road_check_score = #{roadCheckScore},</if>
            work_pic = #{workPic},
            center_pic = #{centerPic},
            side_pic = #{sidePic},
            scene_pic1 = #{scenePic1},
            scene_pic2 = #{scenePic2},
            scene_pic3 = #{scenePic3},
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="isFixed != null and isFixed !=''">is_fixed = #{isFixed},</if>
            <if test="fixedEndDate != null">fixed_end_date = #{fixedEndDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateByTask">
        update sc_task_detail
        set direction=#{direction},
            start_check_code = #{startCheckCode},
            end_check_code = #{endCheckCode}
        where task_id = #{id}
    </update>
    <update id="updateEnvId">
        update sc_task_detail
        set env_detail_id=#{envId}
        where id = #{id}
    </update>
    <update id="updateRoadCheckScoreByEnvId">
        update sc_task_detail
        set score=#{score}
        where id = #{id}
    </update>

    <delete id="deleteScTaskDetailById" parameterType="Long">
        delete
        from sc_task_detail
        where id = #{id}
    </delete>

    <delete id="deleteScTaskDetailByIds" parameterType="String">
        delete from sc_task_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectScTaskDetailByTaskIds" resultType="com.tunnel.domain.TaskDetail">
        select d.* from sc_task_detail d
        where d.check_content != '无问题' and d.check_type != '路域环境检查'
        and d.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="selectScTaskDetailByTaskIdsWithStatus" resultType="com.tunnel.domain.TaskDetail">
        select d.* from sc_task_detail d
        inner join sc_task_evaluate e on d.task_id = e.task_id and e.status = 2
        where d.check_content != '无问题' and d.check_type != '路域环境检查'
        and d.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="selectScTaskDetailWithTaskByTaskIds" resultType="com.tunnel.domain.TaskDetail">
        select distinct d.*, t.road_name, t.company_name
        from sc_task_detail d
        inner join sc_task t on t.id = d.task_id
        where d.is_deleted = 0 and d.is_available = 1
        and t.is_deleted = 0 and t.is_available = 1
        and task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <update id="updateScTaskDetailIsFixed" parameterType="com.tunnel.domain.TaskDetail">
        update sc_task_detail
        set is_fixed = #{isFixed}
        where id = #{id}
    </update>

    <select id="selectNoticeTaskDetail" resultType="com.tunnel.domain.dto.NoticeTaskDetailDTO">
        SELECT DISTINCT sr.company_name,
                        sr.road_name,
                        sr.notify_phone,
                        std.fixed_end_date
        FROM sc_task_detail std
                 INNER JOIN sc_task st
                            ON std.task_id = st.id
                 LEFT JOIN sc_road sr
                           ON st.company_name = sr.company_name
                               AND st.road_name = sr.road_name
        WHERE std.is_deleted = 0
          AND std.is_available = 1
          AND st.is_deleted = 0
          AND st.is_available = 1
          AND sr.is_deleted = 0
          AND sr.is_available = 1
          AND std.is_fixed = 0
          AND std.fixed_end_date IS NOT NULL
          AND sr.notify_user IS NOT NULL
          AND sr.notify_phone IS NOT NULL
          and std.check_content!='无问题'
          AND std.fixed_end_date = DATE_ADD(CURDATE(), INTERVAL 3 DAY)

    </select>


    <select id="selectByMapInfo" resultType="com.tunnel.domain.TaskDetail">
        select d.id, d.longitude,d.latitude,d.address,d.check_project,d.name
        from sc_task_detail d
        left join sc_road r on r.id = d.road_id
        <where>
            longitude is not null and latitude is not null
            and d.check_project in ('服务区','互通区及收费站')
            and d.create_time >= '2025-04-21 00:00:00'
            and d.check_type != '路域环境检查'
            <if test="startDate != null  and endDate != null ">
                and d.create_time between concat(#{startDate}, ' 00:00:00') and concat(#{endDate}, ' 23:59:59')
            </if>
            <if test="checkType != null">
                and d.type = #{checkType}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <choose>
                <when test="type == 1">
                    and d.check_project = '服务区'
                </when>
                <when test="type == 2">
                    and d.check_project = '互通区及收费站'
                </when>
                <otherwise>
                    and d.check_project in ('服务区','互通区及收费站')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectFixedDetailByTaskIds" resultType="com.tunnel.domain.TaskDetail">
        select d.*,f.id as fixedId from sc_task_detail d
        inner join sc_task_detail_fixed f on d.id = f.task_detail_id
        where d.is_fixed = 1 and f.is_confirm = 0 and d.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="selectScTaskDetailListByTaskId" resultType="com.tunnel.domain.dto.TaskDetailQueryDTO">
        select std.id             taskDetailId,
               std.task_id        taskId,
               std.check_code     checkCode,
               std.question_type  questionType,
               std.check_content  checkContent,
               std.scene_pic1     scenePic1,
               std.scene_pic2     scenePic2,
               std.scene_pic3     scenePic3,
               st.road_code       roadCode,
               st.road_name       roadName,
               stdf.after_fix_pic afterFixPic,
               stdf.fix_desc      fixDesc,
               stdf.fix_people    fixPeople,
               stdf.fix_time      fixTime,
               stdf.id             fixedId,
               std.is_fixed isFixed,
               std.check_project checkProject,
               std.create_time checkTime
        from sc_task_detail std
                 left join sc_task st on std.task_id = st.id
                 left join sc_task_detail_fixed stdf on std.id = stdf.task_detail_id
        where std.task_id = #{id}
    </select>
    <select id="selectTaskDetailIsAllFixed" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from sc_task_detail std
        where std.task_id = #{id} and std.is_fixed = 0
    </select>

    <select id="selectStartAndEndCheckCode" resultType="java.lang.String">
        select distinct concat(d.start_check_code, '-', d.end_check_code)
        from sc_task_detail d
        inner join sc_task t on t.id = d.task_id
        where d.is_deleted = 0 and d.is_available = 1 and d.type = 1
        and t.is_deleted = 0 and t.is_available = 1
        and left(t.task_date,7) = #{taskDate}
        and t.road_id = #{roadId}
        order by d.start_check_code
    </select>
</mapper>