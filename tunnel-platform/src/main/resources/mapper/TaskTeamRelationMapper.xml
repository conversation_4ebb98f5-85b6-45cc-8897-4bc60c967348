<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskTeamRelationMapper">
    
    <resultMap type="TaskTeamRelation" id="TaskTeamRelationResult">
        <result property="id"    column="id"    />
        <result property="roadId"    column="road_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <result property="isAvailable"    column="is_available"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTaskTeamRelationVo">
        select id, road_id, team_id, remark, create_time, update_time, creator, modifier, is_available, is_deleted, version_no from sc_task_team_relation
    </sql>

    <select id="selectTaskTeamRelationList" parameterType="TaskTeamRelation" resultMap="TaskTeamRelationResult">
        <include refid="selectTaskTeamRelationVo"/>
        <where>  
            <if test="roadId != null ">
                and FIND_IN_SET(#{roadId}, road_id)
             </if>
            <if test="teamId != null  and teamId != ''">
                and FIND_IN_SET(#{teamId}, team_id)
             </if>
        </where>
    </select>
    
    <select id="selectTaskTeamRelationById" parameterType="Long" resultMap="TaskTeamRelationResult">
        <include refid="selectTaskTeamRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTaskTeamRelation" parameterType="TaskTeamRelation" useGeneratedKeys="true" keyProperty="id">
        insert into sc_task_team_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="teamId != null and teamId != ''">team_id,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="teamId != null and teamId != ''">#{teamId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTaskTeamRelation" parameterType="TaskTeamRelation">
        update sc_task_team_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="teamId != null and teamId != ''">team_id = #{teamId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskTeamRelationById" parameterType="Long">
        delete from sc_task_team_relation where id = #{id}
    </delete>

    <delete id="deleteTaskTeamRelationByIds" parameterType="String">
        delete from sc_task_team_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>