<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskMapper">
    

    <sql id="selectScTaskVo">
        select id, road_id, task_date, status, finish_time,work_pic,center_pic,side_pic, remark, create_time, update_time, creator, modifier, is_available, is_deleted, version_no, team_no
        ,code, road_code, road_name, company_name,company, start_code, end_code, mileage, road_remark, user_id, dept_id
        from sc_task
    </sql>

    <select id="selectScTaskList" parameterType="com.tunnel.domain.Task" resultType="com.tunnel.domain.Task">
        select t.*
        from sc_task t
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        <where>
            <if test="roadCodeOrName != null and roadCodeOrName != ''">
               and (t.road_code = #{roadCodeOrName} or t.road_name like concat('%', #{roadCodeOrName}, '%') or t.code = #{roadCodeOrName} )
            </if>
            <if test="code != null and code != ''"> and t.code = #{code}</if>
            <if test="taskDate != null "> and t.task_date = #{taskDate}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="finishTime != null "> and t.finish_time = #{finishTime}</if>
            <if test="teamNo != null and teamNo != ''"> and t.team_no = #{teamNo}</if>
            <if test="roadIdList != null">
                and t.road_id in
                <foreach collection="roadIdList" item="roadId" open="(" separator="," close=")">
                    #{roadId}
                </foreach>
            </if>
            <if test="isAllFixed != null">
                and t.is_all_fixed = #{isAllFixed}
            </if>
            ${params.dataScope}
        </where>
        order by t.task_date desc ,t.code
    </select>
    
    <select id="selectScTaskById" parameterType="Long" resultType="com.tunnel.domain.Task">
        select t.*
        from sc_task t
        where t.id = #{id}
    </select>
    <select id="selectByTaskDate" resultType="java.lang.Long">
        select road_id from sc_task
        where task_date=#{taskDate}
    </select>
    <select id="listAllMonth" resultType="java.lang.String">
        select distinct task_date
        from sc_task where is_deleted = 0
        <if test="filterDate != null">and left(task_date, 7) &lt;= #{filterDate}</if>
        order by task_date desc
    </select>
    <select id="selectIdListByStatusParams" resultType="java.lang.Long">
        select id from sc_task WHERE id not in
         (SELECT task_id from sc_task_evaluate e
              INNER JOIN sc_task t on t.id=e.task_id WHERE e.type=#{type} and t.task_date=#{taskDate})
     and `status`=#{status} and task_date=#{taskDate}
    </select>
    <select id="listAllMonthDTO" resultType="com.tunnel.domain.Task">
        select distinct task_date
        from sc_task order by task_date desc
    </select>

    <insert id="insertScTask" parameterType="com.tunnel.domain.Task" useGeneratedKeys="true" keyProperty="id">
        insert into sc_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="taskDate != null">task_date,</if>
            <if test="status != null">status,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="teamNo != null">team_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="taskDate != null">#{taskDate},</if>
            <if test="status != null">#{status},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="teamNo != null">#{teamNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>
    <insert id="batchAdd">
        insert into sc_task
            (road_id,task_date,remark,code, road_code, road_name, company_name,company, start_code, end_code, mileage, road_remark, team_no, user_id, dept_id)
        values
        <foreach collection="roadList" item="item" separator="," >
            (#{item.id},#{item.taskDate},'系统自动生成',#{item.code}, #{item.roadCode}, #{item.roadName}, #{item.companyName}
            ,#{item.company}, #{item.startCode}, #{item.endCode}, #{item.mileage}, #{item.roadRemark}, #{item.teamNo}, #{item.userId}, #{item.deptId})
        </foreach>
    </insert>

    <update id="updateScTask" parameterType="com.tunnel.domain.Task">
        update sc_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskDate != null">task_date = #{taskDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="teamNo != null">team_no = #{teamNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            is_all_fixed = #{isAllFixed},
            work_pic = #{workPic},
            center_pic = #{centerPic},
            side_pic = #{sidePic},
        </trim>
        where id = #{id}
    </update>
    <update id="taskComplete">
        update sc_task set status=1,finish_time=now()
        where id=#{id}
    </update>

    <delete id="deleteScTaskById" parameterType="Long">
        delete from sc_task where id = #{id}
    </delete>

    <delete id="deleteScTaskByIds" parameterType="String">
        delete from sc_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectScTaskForCount" resultType="com.tunnel.domain.Task">
        select distinct t.*
        from sc_task t inner join sc_task_detail d on t.id = d.task_id
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        <where>
            t.is_deleted = 0
            and t.is_available = 1
            and d.is_deleted = 0
            and d.is_available = 1
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
                                    '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            and d.check_content != '无问题'
            <if test="companyName != null and companyName != ''">
                and t.company_name like CONCAT('%', #{companyName}, '%')
            </if>
            <if test="roadName != null and roadName != ''">
                and t.road_name like CONCAT('%', #{roadName}, '%')
            </if>
            <if test="roadCode != null and roadCode != ''">
                and t.road_code = #{roadCode}
            </if>
            <if test="taskDate != null">
                and left(t.task_date, 7) = #{taskDate}
            </if>
            <if test="isSpecial">
                and left(t.task_date, 7) &gt; '2025-03'
            </if>
<!--            <if test="isFilter != null and isFilter">-->
<!--                and d.check_project = '服务区'-->
<!--            </if>-->
<!--            <if test="isFilter != null and !isFilter">-->
<!--                and d.check_project not in ('服务区')-->
<!--            </if>-->
            <if test="exportList != null and exportList.size() > 0">
                and (t.company_name, t.road_name, left(t.task_date, 7)) in
                <foreach collection="exportList" item="item" open="(" close=")" separator=",">
                    (#{item.companyName}, #{item.roadName}, #{item.taskDate})
                </foreach>
            </if>
            ${params.dataScope}
        </where>
        order by t.task_date desc
    </select>

    <select id="selectTaskByCompany" resultType="com.tunnel.domain.Task">
    select distinct t.*
        from sc_task t left join sc_task_detail d on t.id = d.task_id and d.is_deleted = 0 and d.is_available = 1  and d.check_content != '无问题'
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        where
            t.is_deleted = 0
            and t.is_available = 1
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
            '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            <if test="companyName != null and companyName != ''">
                and t.company_name = #{companyName}
            </if>
            <if test="taskDate != null">
                and left(t.task_date, 7) = #{taskDate}
            </if>
            <if test="isSpecial">
                and left(t.task_date, 7) &gt; '2025-03'
            </if>
        ${params.dataScope}
        order by t.create_time desc
    </select>

    <select id="selectTaskByRoad" resultType="com.tunnel.domain.Task">
        select distinct t.*
        from sc_task t left join sc_task_detail d on t.id = d.task_id and d.is_deleted = 0 and d.is_available = 1  and d.check_content != '无问题'
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        <where>
            t.is_deleted = 0
            and t.is_available = 1
            and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
            '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
            <if test="taskIdList != null and taskIdList.size() > 0">
                and t.id in
                <foreach collection="taskIdList" item="taskId" open="(" close=")" separator=",">
                    ( #{taskId} )
                </foreach>
            </if>
            <if test="roadName != null and roadName != ''">
                and t.road_name = #{roadName}
            </if>
            <if test="roadCode != null and roadCode != ''">
                and t.road_code = #{roadCode}
            </if>
            <if test="taskDate != null">
                and left(t.task_date, 7) = #{taskDate}
            </if>
            <if test="isSpecial">
                and left(t.task_date, 7) &gt; '2025-03'
            </if>
            ${params.dataScope}
        </where>
    </select>
    <select id="selectScFixedTaskList" resultType="com.tunnel.domain.Task">
        select t.*
        from sc_task t
        left join sc_task_evaluate te on t.id = te.task_id
        left join sys_user us on t.user_id = us.user_id
        left join sys_dept de on t.dept_id = de.dept_id
        where te.status = 2 and te.type= 1
        <if test="roadCodeOrName != null and roadCodeOrName != ''">
            and (t.road_code = #{roadCodeOrName} or t.road_name like concat('%', #{roadCodeOrName}, '%') )
        </if>
        <if test="code != null and code != ''">and t.code = #{code}</if>
        <if test="taskDate != null ">and t.task_date = #{taskDate}</if>
        <if test="status != null ">and t.status = #{status}</if>
        <if test="finishTime != null ">and t.finish_time = #{finishTime}</if>
        <if test="teamNo != null and teamNo != ''">and t.team_no = #{teamNo}</if>
        <if test="roadIdList != null">
            and t.road_id in
            <foreach collection="roadIdList" item="roadId" open="(" separator="," close=")">
                #{roadId}
            </foreach>
        </if>
        <if test="isAllFixed != null">
            and t.is_all_fixed = #{isAllFixed}
        </if>
        ${params.dataScope}
        order by t.task_date desc ,t.code
    </select>
</mapper>