<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckEnumRelationMapper">
    
    <resultMap type="CheckEnumRelation" id="CheckEnumRelationResult">
        <result property="id"    column="id"    />
        <result property="roadItemId"    column="road_item_id"    />
        <result property="envItemId"    column="env_item_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCheckEnumRelationVo">
        select id, road_item_id, env_item_id, create_time, update_time from sc_check_enum_relation
    </sql>

    <select id="selectCheckEnumRelationList" parameterType="CheckEnumRelation" resultMap="CheckEnumRelationResult">
        <include refid="selectCheckEnumRelationVo"/>
        <where>
            <if test="roadItemId != null">and road_item_id=#{roadItemId}</if>
            <if test="envItemId != null">and env_item_id=#{envItemId}</if>
        </where>
    </select>
    
    <select id="selectCheckEnumRelationById" parameterType="Long" resultMap="CheckEnumRelationResult">
        <include refid="selectCheckEnumRelationVo"/>
        where id = #{id}
    </select>
    <select id="selectByRoadItemIdList" resultType="com.tunnel.domain.CheckEnumRelation">
        select * from sc_check_enum_relation
        where road_item_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertCheckEnumRelation" parameterType="CheckEnumRelation" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_enum_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadItemId != null">road_item_id,</if>
            <if test="envItemId != null">env_item_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadItemId != null">#{roadItemId},</if>
            <if test="envItemId != null">#{envItemId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCheckEnumRelation" parameterType="CheckEnumRelation">
        update sc_check_enum_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadItemId != null">road_item_id = #{roadItemId},</if>
            <if test="envItemId != null">env_item_id = #{envItemId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckEnumRelationById" parameterType="Long">
        delete from sc_check_enum_relation where id = #{id}
    </delete>

    <delete id="deleteCheckEnumRelationByIds" parameterType="String">
        delete from sc_check_enum_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>