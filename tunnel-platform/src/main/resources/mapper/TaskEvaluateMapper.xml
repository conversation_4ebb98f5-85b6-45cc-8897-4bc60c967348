<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskEvaluateMapper">
    
    <sql id="selectTaskEvaluateVo">
        select id, road_id, task_id, score,building_score, status,type, remark, create_time, update_time, creator, modifier, is_deleted from sc_task_evaluate
    </sql>

    <select id="selectTaskEvaluateList" parameterType="TaskEvaluate" resultType="TaskEvaluate">
        select e.*,t.task_date,t.road_name,t.road_code,t.code,t.start_code,t.end_code,t.company_name,t.company
        from sc_task_evaluate e
        left join sc_task t on e.task_id=t.id
        <where>
            <if test="type != null "> and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        order by t.code
    </select>
    
    <select id="selectTaskEvaluateById" parameterType="Long" resultType="TaskEvaluate">
        <include refid="selectTaskEvaluateVo"/>
        where id = #{id}
    </select>
    <select id="selectByTaskId" resultType="com.tunnel.domain.TaskEvaluate">
        select * from sc_task_evaluate
         where task_id=#{taskId} and type=#{type}
    </select>
    <select id="selectByParams" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,d.task_id,min(d.start_check_code)start_check_code,max(d.end_check_code)end_check_code,d.direction,t.task_date
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            <if test="startDate != null  and endDate != null ">
                and d.create_time between concat(#{startDate}, ' 00:00:00')  and concat(#{endDate}, ' 23:59:59')
            </if>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
        <include refid="common_query"></include>
        </where>
        group by t.id,d.task_id,d.direction
        order by t.code asc
    </select>

    <sql id="common_query">
        <if test="ids!=null and ids.size()>0">
            and e.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>


    <select id="selectEnvByParams" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,GROUP_CONCAT(scene_pic1 SEPARATOR ',') scene_pic1,
               d.check_code,d.check_project,d.start_check_code,d.end_check_code,d.direction,d.check_project,d.question_type,d.task_id
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
           and d.check_project in ('公路设施维护','公路沿线保洁','隧道设施管理','公路沿线绿化','标志标线管理')
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>

            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and t.task_date = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and t.task_date in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        GROUP BY t.id,d.check_code,d.check_project,d.start_check_code,d.end_check_code,d.direction,d.check_project,d.question_type,d.task_id
        order by t.code asc
    </select>

    <select id="selectQuestionListByParams" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,d.start_check_code,d.end_check_code,d.direction,d.task_id,d.check_code,d.scene_pic1,d.check_project,d.question_type,d.create_time
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            and d.check_project in ('公路设施维护','公路沿线保洁','隧道设施管理','公路沿线绿化','标志标线管理')
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>

            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        order by t.code asc,FIELD(d.check_project,'公路设施维护','公路沿线保洁','隧道设施管理','公路沿线绿化','标志标线管理')
    </select>


    <select id="selectFocusPicEnv" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,d.check_code
             ,d.question_type,d.check_project,d.name,scene_pic1,d.remark,d.direction
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            <if test="isFocus != null "> and d.is_fcous=#{isFocus}</if>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="checkType != null "> and d.check_type = #{checkType}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <if test="roadIdList != null and roadIdList.size() > 0">
                and e.road_id in
                <foreach collection="roadIdList" item="roadId" open="(" close=")" separator=",">
                    #{roadId}
                </foreach>
            </if>
            <if test="checkProject != null">
                and d.check_project=#{checkProject}
            </if>
            <include refid="common_query"></include>
        </where>
        order by t.code asc,t.company_name
    </select>


    <select id="selectWorkPic" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select distinct t.code, t.road_code, t.road_name,d.work_pic
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            and d.work_pic is not null and d.work_pic != 'null'
            <if test="isFocus != null "> and d.is_fcous=#{isFocus}</if>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="checkType != null "> and d.check_type = #{checkType}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        order by t.code asc,t.company_name
    </select>


    <select id="selectByCheckRoadScore" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,d.task_id,t.task_date
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>

            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        group by t.id,d.task_id
    </select>
    <select id="selectPicListByParams" resultType="com.tunnel.domain.TaskDetail">
        select t.code,t.road_code,t.road_name,t.company_name,t.start_code,t.end_code,t.task_date,d.scene_pic1
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        order by t.code
    </select>
    <select id="selectByCheckCity" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,d.task_id,t.task_date,GROUP_CONCAT(DISTINCT d.city SEPARATOR ',') city
             ,min(d.start_check_code)start_check_code,max(d.end_check_code)end_check_code,d.direction
        from sc_task_evaluate e
        inner join sc_task t on e.task_id=t.id
        inner join sc_task_detail d on e.task_id=d.task_id
        <where>
            <if test="type != null "> and d.type = #{type} and e.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and e.task_id = #{taskId}</if>
            <if test="status != null "> and e.status = #{status}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        group by t.id,d.task_id,d.direction
    </select>
    <select id="selectGroupByTaskDate" resultType="com.tunnel.domain.TaskEvaluate">
        SELECT ROUND(AVG(e.score), 2) AS score,e.type,LEFT(t.task_date, 7) AS month
        FROM sc_task_evaluate e
        INNER JOIN sc_task t ON t.id = e.task_id
        GROUP BY month, e.type;
    </select>
    <select id="selectCircleMapList" resultType="PieStat">
        SELECT
            SUM(CASE WHEN score > 90 THEN 1 ELSE 0 END) AS 'above90',
            SUM(CASE WHEN score BETWEEN 80 AND 90 THEN 1 ELSE 0 END) AS 'bet80and90',
            SUM(CASE WHEN score BETWEEN 70 AND 80 THEN 1 ELSE 0 END) AS 'bet70and80',
            SUM(CASE WHEN score BETWEEN 60 AND 70 THEN 1 ELSE 0 END) AS 'bet60and70',
            SUM(CASE WHEN score &lt; 60 THEN 1 ELSE 0 END) AS 'below60',
            count(e.id) AS 'total'
        from sc_task_evaluate e
        INNER JOIN sc_task t on t.id=e.task_id
        WHERE left(t.task_date, 7)=#{taskDate}
        <if test="type != null">
            and e.type = #{type}
        </if>
    </select>
    <select id="selectGroupByCompanyName" resultType="com.tunnel.domain.stat.RankStat">
        SELECT r.company_name,r.company,ROUND(AVG(e.score),2) score
        from sc_task_evaluate e
        INNER JOIN sc_task t on t.id=e.task_id
        INNER JOIN sc_road r on r.id=t.road_id
        <where>
            <if test="createTime !=null">
                and  e.create_time >= #{createTime}
            </if>
            <if test="taskDate != null">
            and t.task_date=#{taskDate}
            </if>
            <if test="startDate != null and endDate!=null">
                and left(t.task_date,7) between #{startDate} and #{endDate}
            </if>
            <if test="type != null">
                and e.type = #{type}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
        </where>
        GROUP BY r.company_name
        ORDER BY score DESC
    </select>


    <select id="selectGroupByCompanyNameStat" resultType="com.tunnel.domain.stat.RankStat">
        SELECT r.company_name,r.company,ROUND(AVG(e.score),2) score
        from sc_task_evaluate e
        INNER JOIN sc_task t on t.id=e.task_id
        INNER JOIN sc_road r on r.id=t.road_id
        <where>
            <if test="taskDate != null">
                and left(t.task_date, 7)=#{taskDate}
            </if>
            <if test="type != null">
                and e.type = #{type}
            </if>
            <if test="company != null">
                and r.company = #{company}
            </if>
            <if test="wuhan != null">
                and r.wuhan = #{wuhan}
            </if>
            <if test="important != null">
                and r.important = #{important}
            </if>
        </where>
        GROUP BY r.company_name
        ORDER BY score DESC
    </select>


    <select id="selectTunnelNameByParams" resultType="java.lang.String">
        select distinct name from sc_task_detail d
        where d.start_check_code=#{startCheckCode} and d.end_check_code=#{endCheckCode}
        and task_id=#{taskId} and direction=#{direction}
        and name like '%隧道%'
    </select>
    <select id="countGroupByTaskDate" resultType="com.tunnel.domain.TaskEvaluate">
        SELECT
            COUNT(1) AS count,d.type,LEFT(t.task_date, 7) AS taskDate
        FROM sc_task_detail d
        INNER JOIN sc_task t ON t.id = d.task_id
        inner join sc_road r on r.id=d.road_id
        WHERE d.check_content != '无问题'
        and t.task_date not in ('2025-04-1', '2025-04-2', '2025-04-3','2025-04-4','2025-04-6',
        '2025-05-1', '2025-05-2', '2025-05-3','2025-05-4','2025-05-6')
        <if test="checkType != null">
            and d.type = #{checkType}
        </if>
        <if test="roadId != null">
            and d.road_id = #{roadId}
        </if>
        <if test="company != null">
            and r.company = #{company}
        </if>
        <if test="wuhan != null">
            and r.wuhan = #{wuhan}
        </if>
        <if test="important != null">
            and r.important = #{important}
        </if>
        <if test="checkProjectType != null">
            <choose>
                <when test="checkProjectType == 1">
                    and d.check_project in ('服务区')
                </when>
                <otherwise>
                    and d.check_project not in ('服务区')
                </otherwise>
            </choose>
        </if>
        GROUP BY taskDate, d.type
    </select>
    <select id="selectTaskDetailCountList" resultType="com.tunnel.domain.TaskEvaluate">
        select count(1) totalCount,d.task_id
        from sc_task_detail d
        left join sc_task t on d.task_id=t.id
        <where>
           and d.check_content!='无问题'
            <if test="type != null "> and d.type = #{type}</if>
            <if test="code != null "> and t.code = #{code}</if>
            <if test="taskId != null "> and d.task_id = #{taskId}</if>
            <if test="taskDate != null "> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null "> and t.company = #{company}</if>
            <if test="companyName != null "> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            <include refid="common_query"></include>
        </where>
        GROUP BY d.task_id
    </select>
    <select id="countGroupByCompanyName" resultType="com.tunnel.domain.stat.RankStat">
        SELECT company_name companyName, company,count(1) totalCount
        from sc_task_detail d
        INNER JOIN sc_task t on t.id=d.task_id
        WHERE
            d.check_content!='无问题'
        <if test="taskDate!=null">
            and  left(t.task_date, 7)=#{taskDate}
        </if>
        <if test="type != null">
            and d.type = #{type}
        </if>
        <if test="startDate != null  and endDate != null ">
            and d.create_time between concat(#{startDate}, ' 00:00:00')  and concat(#{endDate}, ' 23:59:59')
        </if>
        GROUP BY company_name
        ORDER BY score DESC
    </select>
    <select id="selectSpecialCompanyList" resultType="com.tunnel.domain.Road">
        select distinct r.id,r.road_name,r.road_code,r.company_name,r.company,match_company_code,match_company_name
        from sc_road r
        left join sc_task_evaluate e on r.id=e.road_id
        <where>
            and r.match_company_name is not null and r.match_company_name != ''
            <include refid="common_query"></include>
        </where>
    </select>
    <select id="selectTaskEvaluateByRoadIdList" resultType="com.tunnel.domain.TaskEvaluate">
        select e.road_id,ROUND(AVG(e.score), 2) AS score
        from sc_task_evaluate e
        inner join sc_road  r on e.road_id=r.id
        inner join sc_task t on t.id=e.task_id
        <where>
            <if test="task.createTime !=null">
                and e.create_time >= #{task.createTime}
            </if>
            <if test="task.startDate != null and task.endDate!=null">
                and left(t.task_date,7) between #{task.startDate} and #{task.endDate}
            </if>
             <if test="roadIdList!=null and roadIdList.size()>0">
                 and t.road_id in
                 <foreach collection="roadIdList" item="roadId" open="(" close=")" separator=",">
                     #{roadId}
                 </foreach>
             </if>
            <if test="task.company != null">
                and r.company = #{task.company}
            </if>
            <if test="task.wuhan != null">
                and r.wuhan = #{task.wuhan}
            </if>
            <if test="task.important != null">
                and r.important = #{task.important}
            </if>
        </where>
        group by t.road_id
    </select>

    <insert id="insertTaskEvaluate" parameterType="TaskEvaluate" useGeneratedKeys="true" keyProperty="id">
        insert into sc_task_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="type != null">type,</if>
            <if test="score != null">score,</if>
            <if test="buildingScore != null">building_score,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="type != null">#{type},</if>
            <if test="score != null">#{score},</if>
            <if test="buildingScore != null">#{buildingScore},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
         </trim>
    </insert>

    <update id="updateTaskEvaluate" parameterType="TaskEvaluate">
        update sc_task_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="score != null">score = #{score},</if>
            <if test="buildingScore != null">building_score = #{buildingScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskEvaluateById" parameterType="Long">
        delete from sc_task_evaluate where id = #{id}
    </delete>

    <delete id="deleteTaskEvaluateByIds" parameterType="String">
        delete from sc_task_evaluate where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByTaskIdList" resultType="com.tunnel.domain.TaskEvaluate">
        select * from sc_task_evaluate
        where type = 1 and is_deleted = 0 and status = 2 and task_id in
        <foreach item="taskId" collection="taskIdList" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>


    <select id="selectAllTasksByParams" resultType="com.tunnel.domain.template.EvaluateRoadSheetOne">
        select t.code, t.road_code, t.road_name, t.company_name, t.start_code, t.end_code, t.mileage,
            t.id as task_id, t.task_date,
            COALESCE(d.direction, 1) as direction,
            COALESCE(MIN(d.start_check_code), t.start_code) as start_check_code,
            COALESCE(MAX(d.end_check_code), t.end_code) as end_check_code
        from sc_task t
        left join sc_task_evaluate e on e.task_id = t.id
        left join sc_task_detail d on e.task_id = d.task_id
        <where>
            <if test="startDate != null and endDate != null">
                and t.create_time between concat(#{startDate}, ' 00:00:00') and concat(#{endDate}, ' 23:59:59')
            </if>
            <if test="type != null"> and (d.type = #{type} or d.type is null) and (e.type = #{type} or e.type is null)</if>
            <if test="code != null"> and t.code = #{code}</if>
            <if test="taskId != null"> and t.id = #{taskId}</if>
            <if test="status != null"> and (e.status = #{status} or e.status is null)</if>
            <if test="taskDate != null"> and left(t.task_date, 7) = #{taskDate}</if>
            <if test="company != null"> and t.company = #{company}</if>
            <if test="companyName != null"> and t.company_name = #{companyName}</if>
            <if test="taskDateList != null and taskDateList.size() > 0">
                and left(t.task_date, 7) in
                <foreach collection="taskDateList" item="taskDate" open="(" close=")" separator=",">
                    #{taskDate}
                </foreach>
            </if>
            and t.is_deleted = 0 and t.is_available = 1
            <include refid="common_query"></include>
        </where>
        group by t.id, COALESCE(d.direction, 1)
        order by t.code asc
    </select>
    
</mapper>