<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MaintenanceResourceMapper">

    <sql id="selectMaintenanceResourceVo">
        select id, company_name, company, personnel, vehicles, funds,
               main_road, fixed_service_area, fixed_toll_station, fixed_interchange_area,
               add_time, remark, create_time, update_time, creator, modifier,
               is_available, is_deleted, version_no, user_id, dept_id
        from sc_maintenance_resource
    </sql>

    <select id="selectMaintenanceResourceList" parameterType="com.tunnel.domain.MaintenanceResource" resultType="com.tunnel.domain.MaintenanceResource">
        select smr.* from sc_maintenance_resource smr
        left join sys_user us on smr.user_id = us.user_id
        left join sys_dept de on smr.dept_id = de.dept_id
        <where>
            smr.is_deleted = 0
            <if test="companyName != null and companyName != ''">
                AND smr.company_name like concat('%', #{companyName}, '%')
            </if>
            <if test="company != null and company != ''">
                AND smr.company = #{company}
            </if>
            <if test="addTime != null">
                AND smr.add_time = #{addTime}
            </if>
            ${params.dataScope}
        </where>
        order by smr.create_time desc
    </select>

    <select id="selectMaintenanceResourceById" parameterType="Long" resultType="com.tunnel.domain.MaintenanceResource">
        <include refid="selectMaintenanceResourceVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <insert id="insertMaintenanceResource" parameterType="com.tunnel.domain.MaintenanceResource" useGeneratedKeys="true" keyProperty="id">
        insert into sc_maintenance_resource (
            company_name, company, personnel, vehicles, funds,
            main_road, fixed_service_area, fixed_toll_station, fixed_interchange_area,
            add_time, remark, creator, modifier, user_id, dept_id
        ) values (
                     #{companyName}, #{company}, #{personnel}, #{vehicles}, #{funds},
                     #{mainRoad}, #{fixedServiceArea}, #{fixedTollStation}, #{fixedInterchangeArea},
                     #{addTime}, #{remark}, #{creator}, #{modifier}, #{userId}, #{deptId}
                 )
    </insert>

    <update id="updateMaintenanceResource" parameterType="com.tunnel.domain.MaintenanceResource">
        update sc_maintenance_resource
        <set>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="company != null">company = #{company},</if>
            <if test="personnel != null">personnel = #{personnel},</if>
            <if test="vehicles != null">vehicles = #{vehicles},</if>
            <if test="funds != null">funds = #{funds},</if>
            <if test="mainRoad != null">main_road = #{mainRoad},</if>
            <if test="fixedServiceArea != null">fixed_service_area = #{fixedServiceArea},</if>
            <if test="fixedTollStation != null">fixed_toll_station = #{fixedTollStation},</if>
            <if test="fixedInterchangeArea != null">fixed_interchange_area = #{fixedInterchangeArea},</if>
            <if test="addTime != null">add_time = #{addTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            version_no = version_no + 1
        </set>
        where id = #{id} and is_deleted = 0
    </update>

    <update id="deleteMaintenanceResourceByIds">
        update sc_maintenance_resource set is_deleted = 1
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </update>

    <select id="selectMaintenanceResourceByCompanyList" resultType="com.tunnel.domain.MaintenanceResource">
        select * from sc_maintenance_resource
        where is_deleted = 0 and add_time like concat(#{addTimePrefix}, '%')
        and company_name in
        <foreach collection="list" item="companyName" open="(" separator="," close=")">
            #{companyName}
        </foreach>
    </select>

    <!-- 批量插入管养资源记录 -->
    <insert id="batchInsertResources" parameterType="java.util.List">
        insert into sc_maintenance_resource (
        company_name,
        company,
        add_time,
        remark,
        main_road,
        fixed_service_area,
        fixed_toll_station,
        fixed_interchange_area,
        user_id,
        dept_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyName},
            #{item.company},
            #{item.addTime},
            #{item.remark},
            #{item.mainRoad},
            #{item.fixedServiceArea},
            #{item.fixedTollStation},
            #{item.fixedInterchangeArea},
            #{item.userId},
            #{item.deptId}
            )
        </foreach>
    </insert>

    <select id="queryDataBoardCount" resultType="com.tunnel.domain.vo.MaintenanceResourceCountVO">
        select sum(personnel) as personnelCount,
               sum(vehicles) as vehiclesCount,
               sum(funds) as fundsCount,
               sum(main_road) as mainRoadCount
        from sc_maintenance_resource where is_deleted = 0
    </select>

</mapper>
