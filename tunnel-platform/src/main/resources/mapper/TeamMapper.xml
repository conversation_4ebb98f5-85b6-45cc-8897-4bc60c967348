<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TeamMapper">
    
    <resultMap type="Team" id="TeamResult">
        <result property="id"    column="id"    />
        <result property="teamName"    column="team_name"    />
        <result property="userIds"    column="user_ids"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <result property="isAvailable"    column="is_available"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTeamVo">
        select id, team_name, user_ids, remark, create_time, update_time, creator, modifier, is_available, is_deleted, version_no from sc_team
    </sql>

    <select id="selectTeamList" parameterType="Team" resultMap="TeamResult">
        <include refid="selectTeamVo"/>
        <where>  
            <if test="teamName != null "> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="userIds != null"> and FIND_IN_SET(#{userIds}, user_ids)</if>
        </where>
    </select>
    
    <select id="selectTeamById" parameterType="Long" resultMap="TeamResult">
        <include refid="selectTeamVo"/>
        where id = #{id}
    </select>
    <select id="listAll" resultType="com.tunnel.domain.Team">
        select * from sc_team
    </select>
    <select id="selectByTeamIdList" resultType="com.tunnel.domain.Team">
        select * from sc_team where id in
        <foreach item="id" collection="teamIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTeam" parameterType="Team" useGeneratedKeys="true" keyProperty="id">
        insert into sc_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null">team_name,</if>
            <if test="userIds != null and userIds != ''">user_ids,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null">#{teamName},</if>
            <if test="userIds != null and userIds != ''">#{userIds},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTeam" parameterType="Team">
        update sc_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="userIds != null and userIds != ''">user_ids = #{userIds},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeamById" parameterType="Long">
        delete from sc_team where id = #{id}
    </delete>

    <delete id="deleteTeamByIds" parameterType="String">
        delete from sc_team where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>