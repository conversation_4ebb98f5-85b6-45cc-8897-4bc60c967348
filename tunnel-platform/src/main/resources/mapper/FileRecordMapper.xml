<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.FileRecordMapper">
    
    <sql id="selectFileRecordVo">
        select id, road_code, road_name, company_name, file_name, file_url, file_type, year, remark,
        create_time, update_time, creator, modifier, is_available, is_deleted, version_no
        from sc_file_record
    </sql>

    <!-- 分页查询 -->
    <select id="selectFileRecordList" parameterType="com.tunnel.domain.FileRecord" resultType="com.tunnel.domain.FileRecord">
        select sf.* from sc_file_record sf
        left join sys_user us on sf.user_id = us.user_id
        left join sys_dept de on sf.dept_id = de.dept_id
        <where>
            sf.is_deleted = 0
            <if test="roadCode != null and roadCode != ''">
                AND sf.road_code = #{roadCode}
            </if>
            <if test="roadName != null and roadName != ''">
                AND sf.road_name like concat('%', #{roadName}, '%')
            </if>
            <if test="companyName != null and companyName != ''">
                AND sf.company_name like concat('%', #{companyName}, '%')
            </if>
            <if test="fileType != null and fileType != ''">
                AND sf.file_type = #{fileType}
            </if>
            <if test="year != null and year != ''">
                AND sf.`year` = #{year}
            </if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <!-- 新增 -->
    <insert id="insertFileRecord" parameterType="com.tunnel.domain.FileRecord" useGeneratedKeys="true" keyProperty="id">
        insert into sc_file_record (
            road_code, road_name, company_name, file_name, file_url, file_type, year, remark,
            creator, modifier, user_id, dept_id
        ) values (
            #{roadCode}, #{roadName}, #{companyName}, #{fileName}, #{fileUrl}, #{fileType}, #{year}, #{remark},
            #{creator}, #{modifier}, #{userId}, #{deptId}
        )
    </insert>

    <!-- 修改 -->
    <update id="updateFileRecord" parameterType="com.tunnel.domain.FileRecord">
        update sc_file_record
        <set>
            <if test="roadCode != null">road_code = #{roadCode},</if>
            <if test="roadName != null">road_name = #{roadName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            version_no = version_no + 1
        </set>
        where id = #{id} and is_deleted = 0
    </update>

    <!-- 批量删除（逻辑删除） -->
    <update id="deleteFileRecordByIds">
        update sc_file_record set is_deleted = 1
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </update>
</mapper>