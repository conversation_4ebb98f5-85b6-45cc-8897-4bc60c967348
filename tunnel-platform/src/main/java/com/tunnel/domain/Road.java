package com.tunnel.domain;

import java.math.BigDecimal;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tunnel.common.annotation.Excel;

/**
 * 基础数据对象 sc_road
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Road extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    @Excel(name = "序号")
    private String code;

    /**
     * 路线编号
     */
    @Excel(name = "路线编号")
    private String roadCode;

    /**
     * 路线名称
     */
    @Excel(name = "路线名称")
    private String roadName;

    @Excel(name = "线路分组")
    private Integer teamNo;

    /**
     * 管养单位
     */
    @Excel(name = "管养单位")
    private String companyName;

    @Excel(name = "单位简称")
    private String company;

    /**
     * 起点桩号
     */
    @Excel(name = "起点桩号")
    private String startCode;

    /**
     * 迄点桩号
     */
    @Excel(name = "讫点桩号")
    private String endCode;

    /**
     * 里程(km)
     */
    @Excel(name = "里程(km)")
    private BigDecimal mileage;

    /**
     * 是否武汉市周边:0.否,1.是
     */
    @Excel(name = "是否武汉市周边", readConverterExp = "0=否,1=是")
    private Integer wuhan;

    /**
     * 是否重点路段:0.否,1.是
     */
    @Excel(name = "是否重点路段", readConverterExp = "0=否,1=是")
    private Integer important;

    /**
     * 是否可用：0-否，1-是
     */
    private Integer isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Long versionNo;

    private String taskDate;

    private String roadRemark;

    /**
     * 对应交投的公司code
     */
    @Excel(name = "对应交投的公司code")
    private String matchCompanyCode;

    /**
     * 对应交投的公司名称
     */
    @Excel(name = "对应交投的公司名称")
    private String matchCompanyName;

    /**
     * 通知手机号
     */
    private String notifyPhone ;


    private String notifyUser;

    private Long userId;

    private Long deptId;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("roadCode", getRoadCode())
                .append("roadName", getRoadName())
                .append("companyName", getCompanyName())
                .append("startCode", getStartCode())
                .append("endCode", getEndCode())
                .append("mileage", getMileage())
                .append("wuhan", getWuhan())
                .append("important", getImportant())
                .append("matchCompanyCode", getMatchCompanyCode())
                .append("matchCompanyName", getMatchCompanyName())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("creator", getCreator())
                .append("modifier", getModifier())
                .append("isAvailable", getIsAvailable())
                .append("isDeleted", getIsDeleted())
                .append("versionNo", getVersionNo())
                .toString();
    }
}
