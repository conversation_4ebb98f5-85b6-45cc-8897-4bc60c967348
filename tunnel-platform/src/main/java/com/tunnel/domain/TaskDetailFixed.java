package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 整改信息对象 sc_task_detail_fixed
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskDetailFixed extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 检测详情id */
    private Long taskDetailId;

    /** 整改后照片 */
    private String afterFixPic;

    /** 检测项目 */
    private String checkProject;

    /** 整改情况 */
    private String fixDesc;

    /** 整改人 */
    private String fixPeople;

    /** 整改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date fixTime;

    /** 是否确认（0-未确认 1-已确认） */
    private Integer isConfirm;

    /** 确认意见 */
    private String confirmPinion;

    /** 确认人 */
    private String confirmPeople;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date confirmTime;

    /** 备注信息 */
    private String remark;

    /** 是否可用：0-否，1-是 */
    private Integer isAvailable;

    /** 是否逻辑删除：0-否，1-是 */
    private Integer isDeleted;

    /** 版本号 */
    private Integer versionNo;
}