package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tunnel.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CheckEnum extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    @Excel(name = "检测类别")
    private String checkType;

    @Excel(name = "检测项目")
    private String checkProject;

    @Excel(name = "检测内容")
    private String checkContent;

    @Excel(name = "问题类型")
    private String questionType;

    /**
     * 整改天数
     */
    private Integer fixedTime;

    @Excel(name = "扣分值")
    private BigDecimal score;

    @Excel(name = "分值上限")
    private BigDecimal limitScore;

    @Excel(name = "1.公路用地等..,2.路域环境")
    private Integer type;

    //建筑用地-城市字段
    private String city;

//    @Excel(name = "")
    //病害数量统计
    private Integer count=0;

    @Excel(name = "未限制扣分值")
    private BigDecimal unLimitScore;


    /**
     * 查询具体的字段
     */
    private String column;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("checkType", getCheckType())
            .append("checkProject", getCheckProject())
            .append("checkContent", getCheckContent())
            .append("questionType", getQuestionType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }

    /**
     * 当前得分
     * @return
     */
    public BigDecimal getRealScore(){
        if(Objects.isNull(this.limitScore) || Objects.isNull(this.score)){
            return null;
        }
        return BigDecimal.ZERO.max(this.limitScore.subtract(this.score));
    }
}
