package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CheckEnumRes extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @Excel(name = "公路用地默认值")
    private BigDecimal roadScore;

    @Excel(name = "路域环境默认值")
    private BigDecimal envScore;


}
