package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 日常检测数据实体类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScTaskNormal extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;


    /**
     * 任务月份，xxxx-xx
     */
    @Excel(name = "提交时间", dateFormat="yyyy-MM")
    private String taskDate;

    /**
     * 管养单位
     */
    @Excel(name = "请选择数据:治理责任单位")
    private String companyName;

    /**
     * 高速公路名称
     */
    @Excel(name = "请选择数据:高速公路名称")
    private String roadName;


    /**
     * 检测类别
     */
    @Excel(name = "考评类别")
    private String checkType;

    /**
     * 检测项目
     */
    @Excel(name = "具体位置")
    private String checkProject;

    /**
     * 检测内容
     */
    @Excel(name = "相关问题:问题")
    private String checkContent;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 是否整改0-未整改 1-已整改
     */
    @Excel(name = "整治状态")
    private String fixedString;

    /**
     * 是否整改0-未整改 1-已整改
     */
    private Integer isFixed;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否可用：0-否，1-是
     */
    private Boolean isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Boolean isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;
}
