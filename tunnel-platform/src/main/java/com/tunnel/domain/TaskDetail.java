package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 检测列表对象 sc_task_detail
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class TaskDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /** 任务id */
    @Excel(name = "任务id", sort = 11)
    @NotNull(message = "任务不能为空")
    private Long taskId;

    private List<Long> taskIdList;

    /** 线路id */
    @Excel(name = "线路id", sort = 12)
    private Long roadId;

    /** 检测项目id(多个使用逗号隔开) */
    @Excel(name = "检测项目id(多个使用逗号隔开)", sort = 13)
    private String itemId;

    /**
     * 对应路域环境监测明细ID
     */
    private Long envDetailId;

    /**
     * 是否通过路域环境项自动生成:0.否,1.是
     */
    private Integer sourceType;

    /**
     * 检测类型: 1.专项监测, 2.路域环境
     */
    private Integer type;

    @Excel(name = "监测点编号", sort = 3, scale = 3)
//    @NotBlank(message = "桩号不能为空")
    private BigDecimal checkCode;

    /** 检测名称 */
    @Excel(name = "检测名称", sort = 4)
//    @NotBlank(message = "检测名称不能为空")
    private String name;

//    @Excel(name = "城市")
    private String city;

    /** 方向：1-上行，2-下行 */
    @Excel(name = "方向：1-左幅，2-右幅", sort = 2, readConverterExp = "1=左幅,2=右幅")
//    @NotNull(message = "方向不能为空")
    private Integer direction;

    /** 检测起点桩号 */
    @Excel(name = "检测起点桩号", sort = 14)
//    @NotBlank(message = "检测起点桩号不能为空")
    private BigDecimal startCheckCode;

    /** 检测迄点桩号 */
    @Excel(name = "检测迄点桩号", sort = 15)
//    @NotBlank(message = "检测迄点桩号不能为空")
    private BigDecimal endCheckCode;

    /**
     * 监测数量
     */
//    @Excel(name = "监测数量")
//    @NotNull(message = "监测数量不能为空")
    private BigDecimal checkNum;

    /**
     * 监测数量单位
     */
//    @Excel(name = "监测数量单位")
//    @NotBlank(message = "监测数量单位不能为空")
    private String checkNumUnit;

    /**
     * 是否整改0-未整改 1-已整改
     */
    @Excel(name = "是否整改", sort = 9, readConverterExp = "0=未整改,1=已整改")
    private Integer isFixed;

    @Excel(name = "整改截止日期", sort = 10)
    @JsonFormat(pattern = "yyyy-MM-dd hh:MM:ss")
    private Date fixedEndDate;

    private Integer fixTimeRange;

    private Date searchStartTime;

    private Date searchEndTime;

//    @Excel(name = "备注")
    private String remark;

    /** 监测扣分值选择 */
    @Excel(name = "监测扣分值选择", sort = 16)
    @NotNull(message = "监测扣分值不能为空")
    private BigDecimal score;

    /** 是否重点关注病害：0-否，1-是 */
    @Excel(name = "是否重点关注病害：0-否，1-是", sort = 17)
    @NotNull(message = "是否重点关注病害不能为空")
    private Integer isFcous;

    @Excel(name = "是否路域环境病害：0-否，1-是", sort = 18)
    @NotNull(message = "是否路域环境病害不能为空")
    private Integer isRoadCheck;

    @Excel(name = "路域环境对应扣分值", sort = 19)
    @NotNull(message = "路域环境对应扣分值不能为空")
    private BigDecimal roadCheckScore;

    /** 工作照 */
    @Excel(name = "工作照", sort = 20)
//    @NotBlank(message = "工作照不能为空")
    private String workPic;

    /** 中央隔离带照片 */
    @Excel(name = "中央隔离带照片", sort = 21)
//    @NotBlank(message = "中央隔离带照片不能为空")
    private String centerPic;

    /** 路侧景观照片 */
    @Excel(name = "路侧景观照片", sort = 22)
//    @NotBlank(message = "路侧景观照片不能为空")
    private String sidePic;

    /** 现场照片1 */
    @Excel(name = "现场照片1", sort = 23)
//    @NotBlank(message = "现场照片1不能为空")
    private String scenePic1;

    /** 现场照片2 */
    @Excel(name = "现场照片2", sort = 24)
//    @NotBlank(message = "现场照片2不能为空")
    private String scenePic2;

    /** 现场照片3 */
    @Excel(name = "现场照片3", sort = 25)
//    @NotBlank(message = "现场照片3不能为空")
    private String scenePic3;

    /** 经度 */
    @Excel(name = "经度", sort = 26)
//    @NotBlank(message = "经度不能为空")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度", sort = 27)
//    @NotBlank(message = "纬度不能为空")
    private String latitude;

    /** 地址信息 */
    @Excel(name = "地址信息", sort = 28)
//    @NotBlank(message = "地址信息不能为空")
    private String address;

    /**
     * 是否可用：0-否，1-是
     */
    private Integer isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Long versionNo;

    /** 序号 */
    @Excel(name = "序号", sort = 0)
    private String code;
    /** 路线编号 */
    @Excel(name = "路线编号", sort = 29)
    private String roadCode;

    /** 路线名称 */
    @Excel(name = "路线名称", sort = 1)
    private String roadName;

    /** 管养单位 */
    @Excel(name = "管养单位", sort = 30)
    private String companyName;

    /**
     * 管养单位简称
     */
    private String company;

    /**
     * 对应管养单位
     */
    private String matchCompanyName;

    /** 起点桩号 */
    @Excel(name = "起点桩号", sort = 31)
    private String startCode;

    /** 迄点桩号 */
    @Excel(name = "迄点桩号", sort = 32)
    private String endCode;

    /** 里程(km) */
    @Excel(name = "里程(km)", sort = 33)
    private BigDecimal mileage;

    /** 检测类别 */
    @Excel(name = "检测类别", sort = 5)
    @NotBlank(message = "检测类别不能为空")
    private String checkType;

    /** 检测项目 */
    @Excel(name = "检测项目", sort = 6)
    @NotBlank(message = "检测项目不能为空")
    private String checkProject;

    /** 检测内容 */
    @Excel(name = "检测内容", sort = 7)
    @NotBlank(message = "检测内容不能为空")
    private String checkContent;

    /** 问题类型 多个使用逗号隔开*/
    @Excel(name = "问题类型", sort = 8)
    @NotBlank(message = "问题类型不能为空")
    private String questionType;
    /**
     * 线路名称或编码
     */
    private String roadCodeOrName;

    private String enumScoreKey;


    private BigDecimal maxScore;

    /**
     * 任务日期
     */
    private String taskDate;

    private Integer num;

    /**
     * 需要整改数量
     */
    private Integer needFixCount;

    private Boolean isFilter;

    private Long fixedId;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskId", getTaskId())
                .append("roadId", getRoadId())
                .append("itemId", getItemId())
                .append("name", getName())
                .append("direction", getDirection())
                .append("startCheckCode", getStartCheckCode())
                .append("endCheckCode", getEndCheckCode())
                .append("checkNum", getCheckNum())
                .append("checkNumUnit", getCheckNumUnit())
                .append("score", getScore())
                .append("isFcous", getIsFcous())
                .append("workPic", getWorkPic())
                .append("centerPic", getCenterPic())
                .append("sidePic", getSidePic())
                .append("scenePic1", getScenePic1())
                .append("scenePic2", getScenePic2())
                .append("scenePic3", getScenePic3())
                .append("longitude", getLongitude())
                .append("latitude", getLatitude())
                .append("address", getAddress())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("creator", getCreator())
                .append("modifier", getModifier())
                .append("isAvailable", getIsAvailable())
                .append("isDeleted", getIsDeleted())
                .append("versionNo", getVersionNo())
                .toString();
    }


}
