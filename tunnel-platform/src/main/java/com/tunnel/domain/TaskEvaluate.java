package com.tunnel.domain;

import java.math.BigDecimal;
import java.util.List;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tunnel.common.annotation.Excel;

/**
 * 检测任务评定对象 sc_task_evaluate
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Data
public class TaskEvaluate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 线路id
     */
    private Long roadId;
    @Excel(name = "线路名称")
    private String roadName;
    @Excel(name = "线路序号")
    private String code;
    @Excel(name = "线路编码")
    private String roadCode;
    @Excel(name = "任务月份")
    private String taskDate;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 评定分数
     */
    @Excel(name = "评定分数")
    private BigDecimal score;

    //    @Excel(name = "病害数量")
    private Integer count = 0;

    @Excel(name = "建筑用地分数")
    private BigDecimal buildingScore;

    //    @Excel(name = "建筑用地数量")
    private Integer buildingCount = 0;

    /**
     * 检测类型: 1.专项监测,2.路域环境
     */
    @Excel(name = "检测类型", readConverterExp = "1=专项监测,2=路域环境")
    private Integer type;

    /**
     * 评定状态：0.评定失败,1.评定成功
     */
    @Excel(name = "评定状态", readConverterExp = "0=评定失败,1=评定成功")
    private Integer status;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    private Long userId;

    private String checkType;
    /**
     * 是否生成文件
     */
    private boolean initFile;
    /**
     * 文件生成父路径
     */
    private String sourcePath;

    /**
     * 城市
     */
    private String city;

    /**
     * 任务检测id集合
     */
    private List<Long> ids;


    private String startCode;

    private String endCode;

    private String companyName;

    private String company;

    private Integer isFocus;

    private List<String> taskDateList;

    /**
     * 下载任务唯一标识
     */
    private String downloadTaskId;
    /**
     * 检测项目
     */
    private String checkProject;

    private List<Long> roadIdList;


    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;


    private Integer totalCount;

    /**
     * 已整改数量
     */
    private Integer fixedCount;

    /**
     * 待整改数量
     */
    private Integer pendingCount;

    /**
     * 整改率
     */
    private Double fixedRate;

}
