package com.tunnel.domain;

import java.math.BigDecimal;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tunnel.common.annotation.Excel;

/**
 * 检测类别问题项-最大分值对象 sc_check_enum_score
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class CheckEnumScore extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 检测类别 */
    @Excel(name = "检测类别")
    private String checkType;

    /** 检测项目 */
    @Excel(name = "检测项目")
    private String checkProject;

    /** 检测内容 */
    @Excel(name = "检测内容")
    private String checkContent;

    /** 最大扣分值 */
    @Excel(name = "最大扣分值")
    private BigDecimal score;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("checkType", getCheckType())
            .append("checkProject", getCheckProject())
            .append("checkContent", getCheckContent())
            .append("score", getScore())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
