package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class CountAndScore extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    //分数统计
    private BigDecimal score;
//    @Excel(name = "")
    //病害数量统计
    private Integer count;

    public CountAndScore(BigDecimal score, Integer count) {
        this.score = score;
        this.count = count;
    }
}
