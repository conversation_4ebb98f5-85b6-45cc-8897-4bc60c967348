package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件资源对象 sc_file_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 路线编号 */
    private String roadCode;

    /** 路线名称 */
    private String roadName;

    /** 管养单位 */
    private String companyName;

    /** 文件名称 */
    private String fileName;

    /** 文件地址 */
    private String fileUrl;

    /** 文件分类*/
    private String fileType;

    /** 年份 **/
    private String year;

    /** 备注信息 */
    private String remark;

    /** 是否可用：0-否，1-是 */
    private Integer isAvailable;

    /** 是否逻辑删除：0-否，1-是 */
    private Integer isDeleted;

    /** 版本号 */
    private Integer versionNo;

    private Long userId;

    private Long deptId;
}