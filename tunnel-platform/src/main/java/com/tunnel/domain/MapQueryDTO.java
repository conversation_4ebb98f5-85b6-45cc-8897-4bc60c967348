package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 任务列表对象 sc_task
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class MapQueryDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     *  类型:1.服务区,2.收费站
     */
    private Integer type;

    /**
     *  1.专项检测  2.  日常监测
     */
    private Integer checkType;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    private String company;

}
