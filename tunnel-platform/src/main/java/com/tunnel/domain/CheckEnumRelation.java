package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 公路用地-路域环境对应关系对象 sc_check_enum_relation
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
public class CheckEnumRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 公路用地检测项ID
     */
    @Excel(name = "公路用地检测项ID")
    private Long roadItemId;

    /**
     * 路域环境检测项ID
     */
    @Excel(name = "路域环境检测项ID")
    private Long envItemId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("roadItemId", getRoadItemId())
                .append("envItemId", getEnvItemId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}