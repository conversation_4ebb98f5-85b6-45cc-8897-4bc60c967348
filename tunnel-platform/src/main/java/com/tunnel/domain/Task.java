package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务列表对象 sc_task
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class Task extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 线路id
     */
    private Long roadId;

    /**
     * 任务日期
     */
    @Excel(name = "任务日期", width = 30, dateFormat = "yyyy-MM-dd")
    private String taskDate;

    //    @Excel(name = "检测状态")
    private Integer status;

    @Excel(name = "检测状态")
    private String statusName;

    /**
     * 检测完成时间
     */
    @Excel(name = "检测完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String finishTime;

    @Excel(name = "线路序号")
    private String code;
    /**
     * 路线编号
     */
    @Excel(name = "路线编号")
    private String roadCode;

    /**
     * 路线名称
     */
    @Excel(name = "路线名称")
    private String roadName;


    @Excel(name = "线路分组")
    private Integer teamNo;

    @Excel(name = "任务名称")
    private String taskName;

    /**
     * 管养单位
     */
    @Excel(name = "管养单位")
    private String companyName;

    @Excel(name = "单位简称")
    private String company;

    /**
     * 起点桩号
     */
    @Excel(name = "起点桩号")
    private String startCode;

    /**
     * 迄点桩号
     */
    @Excel(name = "迄点桩号")
    private String endCode;

    /**
     * 里程(km)
     */
    @Excel(name = "里程(km)")
    private BigDecimal mileage;

    /**
     * 方向：1-上行，2-下行
     */
    private Integer direction;

    /**
     * 检测起点桩号
     */
    private String startCheckCode;

    /**
     * 检测迄点桩号
     */
    private String endCheckCode;

    //    @Excel(name = "工作照")
    private String workPic;

    /**
     * 中央隔离带照片
     */
//    @Excel(name = "中央隔离带照片")
    private String centerPic;

    /**
     * 路侧景观照片
     */
//    @Excel(name = "路侧景观照片")
    private String sidePic;

    /**
     * 线路名称或编码
     */
    private String roadCodeOrName;

    /**
     * 是否可用：0-否，1-是
     */
    private Integer isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Long versionNo;

    private Integer type;

    /**
     * 统计类型:1.数量,2分数
     */
    private Integer countType;


    private List<TaskDetail> taskDetailList;

    private List<Long> roadIdList;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;


    /**
     *  1.专项检测  2.  日常监测
     */
    private Integer checkType;


    private Long userId;

    private Long deptId;

    /**
     * 检测项目类型:1.服务区,2.服务区之外的
     */
    private Integer checkProjectType;

    /**
     * 是否武汉市周边:0.否,1.是
     */
    private Integer wuhan;

    /**
     * 是否重点路段:0.否,1.是
     */
    private Integer important;

    /**
     * 是否完成整改 1-是 0-否
     */
    private Integer isAllFixed;

    //出参字段，新增加
    /**
     * 上上上个月检测区间
     */
    private String oneMonthCheckCode;

    /**
     * 上上个月检测区间
     */
    private String twoMonthCheckCode;

    /**
     * 上个月检测区间
     */
    private String threeMonthCheckCode;

}
