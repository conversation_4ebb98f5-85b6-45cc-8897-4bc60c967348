package com.tunnel.domain.template;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 基础数据对象 sc_road
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class EvaluateRoadSheetThree extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "管养单位")
    private String companyName;

    @Excel(name = "平均得分")
    private BigDecimal score = BigDecimal.ZERO;

    @Excel(name = "病害总数")
    private Integer count = 0;

    @Excel(name = "管辖路段数量")
    private Integer manageCount = 0;

    @Excel(name = "检测路段数量")
    private Integer checkRoadCount = 0;

}
