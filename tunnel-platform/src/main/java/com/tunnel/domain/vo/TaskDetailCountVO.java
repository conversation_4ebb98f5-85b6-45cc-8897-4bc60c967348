package com.tunnel.domain.vo;

import com.tunnel.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @author: muke
 * @date: 2025年04月13日 12:35
 */
@Data
public class TaskDetailCountVO {

    private Long id;

    private List<Long> idList;

//    @Excel(name = "序号")
//    private String code;

    @Excel(name = "任务日期")
    private String taskDate;

    @Excel(name = "管养单位")
    private String companyName;

//    @Excel(name = "路线名称")
    private String roadName;

//    @Excel(name = "路线编号")
    private String roadCode;

//    @Excel(name = "起点桩号")
    private String startCode;

//    @Excel(name = "迄点桩号")
    private String endCode;

    @Excel(name = "问题总数")
    private Integer totalCount;

    @Excel(name = "评分")
    private BigDecimal score;

    // 在Service层计算得出的统计值

    @Excel(name = "已整改数量")
    private Integer fixedCount;

    @Excel(name = "待整改数量")
    private Integer pendingCount;

    @Excel(name = "整改率")
    private Double fixedRate;

    private Boolean isFilter;
}
