package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 管养资源投入记录对象 sc_maintenance_resource
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaintenanceResource extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 管养单位 */
    private String companyName;

    /** 公司简称 */
    private String company;

    /** 投入人员数量 */
    private Integer personnel;

    /** 投入车辆数量 */
    private Integer vehicles;

    /** 已完成整修的主线（单位：公里） */
    private BigDecimal mainRoad;

    /** 已完成整改的服务区 */
    private String fixedServiceArea;

    /** 已完成整改的收费站 */
    private String fixedTollStation;

    /** 已完成整改的互通区 */
    private String fixedInterchangeArea;

    /** 投入资金（单位：元） */
    private BigDecimal funds;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date addTime;

    /** 备注信息 */
    private String remark;

    /** 是否可用：0-否，1-是 */
    private Integer isAvailable;

    /** 是否逻辑删除：0-否，1-是 */
    private Integer isDeleted;

    /** 版本号 */
    private Integer versionNo;

    private Long userId;

    private Long deptId;
}
