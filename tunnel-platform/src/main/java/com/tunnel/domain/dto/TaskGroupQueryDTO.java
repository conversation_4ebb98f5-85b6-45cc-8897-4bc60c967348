package com.tunnel.domain.dto;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskGroupQueryDTO extends BaseEntity {

    private String companyName;

    private String roadName;

    private String roadCode;

    private Long roadId;

    private String taskDate;

    List<TaskGroupQueryDTO> exportList;

    List<Long> taskIdList;

    /**
     * 内部使用，无需传参
     */
    private String endDate;

    private Boolean isSpecial;

}
