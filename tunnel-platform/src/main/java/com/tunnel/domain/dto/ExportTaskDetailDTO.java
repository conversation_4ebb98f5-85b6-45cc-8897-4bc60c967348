package com.tunnel.domain.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExportTaskDetailDTO {

    @Excel(name = "序号", width = 10)
    private String index;

    @Excel(name = "单位名称", width = 30)
    private String company;

    @Excel(name = "问题路段", width = 30)
    private String questionRoad;

    @Excel(name = "主要存在问题", width = 30)
    private String mainQuestion;

    @Excel(name = "整改前照片", type = 2, width = 40, height = 40, imageType = 2)
    private byte[ ] afterImg;

    @Excel(name = "监测机构", width = 10)
    private String jcCompany;

}
