package com.tunnel.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class TaskDetailQueryDTO {

    private Long taskId;

    private Long fixedId;

    private Long taskDetailId;

    private String checkCode;

    private String questionType;

    private String checkContent;

    private String roadName;

    private String checkProject;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;

    // 整改前照片
    private String scenePic1;
    private String scenePic2;
    private String scenePic3;

    // 整改后照片
    private String afterFixPic;

    private String fixDesc;

    private String fixPeople;

    private String fixTime;

    private Integer isFixed;

}
