package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import com.tunnel.domain.TaskDetail;
import lombok.Data;

import java.util.List;

/**
 * 公路检测类别问题项对象 sc_check_enum
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class ResultStatNew extends BaseEntity {
    /**
     * 问题统计
     */
    private QuestionStat questionStat;

    /**
     * 近六个月的巡查情况
     */
    private MonthScore monthScore;

    /**
     * 评分占比-环图
     */
    private List<CircleStat> circleList;

    /**
     * 问题类型占比-饼图
     */
    private List<CircleStat> pieList;

    /**
     * 问题类型统计-柱状图-横向
     */
    private List<ZhuStat> zhuList;

    /**
     * 路段总分排名
     */
    private List<TaskDetail> roadRankList;

    /**
     * 管养单位平均分排名
     */
    private List<RankStat> companyRankList;
}
