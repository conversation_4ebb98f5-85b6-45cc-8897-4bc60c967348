package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.TaskEvaluate;
import lombok.Data;

import java.util.List;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class ResultStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 总数统计
     */
    private RoadStat roadStat;

    /**
     * 路段月度均分
     */
    private MonthScore monthScore;

    /**
     * 评分占比-环图
     */
    private List<CircleStat> circleList;

    /**
     * 病害类型统计-饼图
     */
    private List<CircleStat> pieList;

    /**
     * 路段总分排名
     */
    private List<TaskEvaluate> roadRankList;

    /**
     * 管养单位平均分排名
     */
    private List<RankStat> companyRankList;
}
