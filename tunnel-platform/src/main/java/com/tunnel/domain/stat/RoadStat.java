package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 路段信息汇总
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class RoadStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**
     * 路段总数
     */
    private Integer totalCount;
    /**
     * 合计里程
     */
    private BigDecimal totalMileage;
    /**
     * 合计里程
     */
    private Integer companyCount;

    /**
     * 检测总里程
     */
    private BigDecimal checkMileage;
}
