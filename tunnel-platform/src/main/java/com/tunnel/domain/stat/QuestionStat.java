package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 问题数统计
 */
@Data
public class QuestionStat extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 问题总数
     */
    private Integer totalCount;
    /**
     * 已整改
     */
    private Integer checkedCount;
    /**
     * 待整改
     */
    private Integer needCheckCount;

}
