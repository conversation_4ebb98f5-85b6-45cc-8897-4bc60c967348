package com.tunnel.domain.stat;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class RankStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**
     * 序号
     */
    private Integer index;

    /**
     * 管养单位
     */
    private String company;

    private String companyName;

    private Integer manageCount;

    /**
     * 需要整改数量
     */
    private Integer needFixCount;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 分数
     */
    private BigDecimal score;
}
