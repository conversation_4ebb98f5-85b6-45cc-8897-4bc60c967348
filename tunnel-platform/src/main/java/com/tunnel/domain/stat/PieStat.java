package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 路段信息汇总
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class PieStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**
     * 90分以上
     */
    private Integer above90=0;
    /**
     * 80到90分
     */
    private Integer bet80and90=0;
    /**
     * 70到80分
     */
    private Integer bet70and80=0;

    /**
     * 60到70分
     */
    private Integer bet60and70=0;
    /**
     * 69分以下
     */
    private Integer below60=0;

    private Integer total;

    public Integer getTotal(){
        if(Objects.isNull(total) || total==0){
            return 1;
        }
        return total;
    }
}
