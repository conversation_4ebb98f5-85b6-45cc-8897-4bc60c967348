package com.tunnel.domain.stat;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 公路检测类别问题项对象 sc_check_enum
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
public class MonthScore extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**
     * 月份列表
     */
    private List<String> monthList;
    /**
     * 公路用地分数
     */
    private List<BigDecimal> roadScoreList;
    /**
     * 路域环境分数
     */
    private List<BigDecimal> envScoreList;
}
