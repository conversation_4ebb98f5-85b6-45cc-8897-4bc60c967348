package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 任务和检测组关系对象 sc_task_team_relation
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Data
public class TaskTeamRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 任务ID
     */
    @Excel(name = "线路ID")
    private String roadId;

    private String roadNameList;

    /**
     * 检测组ID
     */
    @Excel(name = "检测组ID")
    private String teamId;

    private List<Long> teamIdList;

    private String teamNameList;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 是否可用：0-否，1-是
     */
    private Integer isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Long versionNo;
}
