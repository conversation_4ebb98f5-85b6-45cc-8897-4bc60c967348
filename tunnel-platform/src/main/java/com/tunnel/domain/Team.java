package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 检测组和用户关系对象 sc_team
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Data
public class Team extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 小组名称
     */
    @Excel(name = "小组名称")
    private String teamName;

    /**
     * 用户集合(多个使用逗号拼接)
     */
    @Excel(name = "用户集合(多个使用逗号拼接)")
    private String userIds;

    private List<Long> userIdList;

    private String userNickName;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 是否可用：0-否，1-是
     */
    private Integer isAvailable;

    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 版本号:默认0,每次更新+1
     */
    private Long versionNo;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teamName", getTeamName())
                .append("userIds", getUserIds())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("creator", getCreator())
                .append("modifier", getModifier())
                .append("isAvailable", getIsAvailable())
                .append("isDeleted", getIsDeleted())
                .append("versionNo", getVersionNo())
                .toString();
    }
}
