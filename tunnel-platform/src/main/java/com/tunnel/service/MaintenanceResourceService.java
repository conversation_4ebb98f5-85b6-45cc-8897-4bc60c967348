package com.tunnel.service;

import com.tunnel.domain.MaintenanceResource;
import com.tunnel.domain.vo.MaintenanceResourceCountVO;

import java.util.List;

public interface MaintenanceResourceService {
    
    /**
     * 查询管养资源投入记录列表
     */
    List<MaintenanceResource> selectMaintenanceResourceList(MaintenanceResource maintenanceResource);

    /**
     * 根据ID查询管养资源投入记录
     */
    MaintenanceResource selectMaintenanceResourceById(Long id);

    /**
     * 新增管养资源投入记录
     */
    int insertMaintenanceResource(MaintenanceResource maintenanceResource);

    /**
     * 修改管养资源投入记录
     */
    int updateMaintenanceResource(MaintenanceResource maintenanceResource);

    /**
     * 批量删除管养资源投入记录
     */
    int deleteMaintenanceResourceByIds(List<Long> ids);

    List<MaintenanceResource> selectMaintenanceResourceByCompanyList(String addTimePrefix, List<String> list);

    int batchInsertResources(List<MaintenanceResource> list);

    MaintenanceResourceCountVO queryDataBoardCount();
}