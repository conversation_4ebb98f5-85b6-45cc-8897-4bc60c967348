package com.tunnel.service.impl;

import java.util.List;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.CheckEnumRelation;
import com.tunnel.domain.CheckEnumScore;
import com.tunnel.mapper.CheckEnumRelationMapper;
import com.tunnel.service.CheckEnumRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公路用地-路域环境对应关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class CheckEnumRelationServiceImpl implements CheckEnumRelationService
{
    @Autowired
    private CheckEnumRelationMapper checkEnumRelationMapper;

    /**
     * 查询公路用地-路域环境对应关系
     * 
     * @param id 公路用地-路域环境对应关系主键
     * @return 公路用地-路域环境对应关系
     */
    @Override
    public CheckEnumRelation selectCheckEnumRelationById(Long id)
    {
        return checkEnumRelationMapper.selectCheckEnumRelationById(id);
    }

    /**
     * 查询公路用地-路域环境对应关系列表
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 公路用地-路域环境对应关系
     */
    @Override
    public List<CheckEnumRelation> selectCheckEnumRelationList(CheckEnumRelation CheckEnumRelation)
    {
        return checkEnumRelationMapper.selectCheckEnumRelationList(CheckEnumRelation);
    }

    /**
     * 新增公路用地-路域环境对应关系
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 结果
     */
    @Override
    public int insertCheckEnumRelation(CheckEnumRelation CheckEnumRelation)
    {
        CheckEnumRelation.setCreateTime(DateUtils.getNowDate());
        return checkEnumRelationMapper.insertCheckEnumRelation(CheckEnumRelation);
    }

    /**
     * 修改公路用地-路域环境对应关系
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 结果
     */
    @Override
    public int updateCheckEnumRelation(CheckEnumRelation CheckEnumRelation)
    {
        CheckEnumRelation.setUpdateTime(DateUtils.getNowDate());
        return checkEnumRelationMapper.updateCheckEnumRelation(CheckEnumRelation);
    }

    /**
     * 批量删除公路用地-路域环境对应关系
     * 
     * @param ids 需要删除的公路用地-路域环境对应关系主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumRelationByIds(Long[] ids)
    {
        return checkEnumRelationMapper.deleteCheckEnumRelationByIds(ids);
    }

    /**
     * 删除公路用地-路域环境对应关系信息
     * 
     * @param id 公路用地-路域环境对应关系主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumRelationById(Long id)
    {
        return checkEnumRelationMapper.deleteCheckEnumRelationById(id);
    }
}
