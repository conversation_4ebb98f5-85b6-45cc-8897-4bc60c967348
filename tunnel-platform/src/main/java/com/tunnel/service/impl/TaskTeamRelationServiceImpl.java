package com.tunnel.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.Road;
import com.tunnel.domain.TaskTeamRelation;
import com.tunnel.domain.Team;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.mapper.TaskTeamRelationMapper;
import com.tunnel.mapper.TeamMapper;
import com.tunnel.service.TaskTeamRelationService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 任务和检测组关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Service
public class TaskTeamRelationServiceImpl implements TaskTeamRelationService {
    @Autowired
    private TaskTeamRelationMapper taskTeamRelationMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private RoadMapper roadMapper;

    /**
     * 查询任务和检测组关系
     *
     * @param id 任务和检测组关系主键
     * @return 任务和检测组关系
     */
    @Override
    public TaskTeamRelation selectTaskTeamRelationById(Long id) {
        return taskTeamRelationMapper.selectTaskTeamRelationById(id);
    }

    /**
     * 查询任务和检测组关系列表
     *
     * @param taskTeamRelation 任务和检测组关系
     * @return 任务和检测组关系
     */
    @Override
    public List<TaskTeamRelation> selectTaskTeamRelationList(TaskTeamRelation taskTeamRelation) {
        List<TaskTeamRelation> list = taskTeamRelationMapper.selectTaskTeamRelationList(taskTeamRelation);
        for (TaskTeamRelation temp : list) {
            String arrays[]= temp.getTeamId().split(",");
            List<Long> teamIds = Lists.newArrayList();
            for (String array : arrays) {
                teamIds.add(Long.valueOf(array));
            }
            //转换成List
            List<Team> sysUsers=teamMapper.selectByTeamIdList(teamIds);
            List<String> teamNameList = sysUsers.stream().map(Team::getTeamName).collect(Collectors.toList());
            temp.setTeamNameList(String.join(",", teamNameList));

            String roadIds[]= temp.getRoadId().split(",");
            List<Long> roadList = Lists.newArrayList();
            for (String array : roadIds) {
                roadList.add(Long.valueOf(array));
            }
            //转换成List
            List<Road> roadIdList=roadMapper.selectByRoadIdList(roadList);
            List<String> roadNameList = roadIdList.stream().map(Road::getRoadName).collect(Collectors.toList());
            temp.setRoadNameList(String.join(",", roadNameList));
        }
        return list;
    }

    /**
     * 新增任务和检测组关系
     *
     * @param taskTeamRelation 任务和检测组关系
     * @return 结果
     */
    @Override
    public int insertTaskTeamRelation(TaskTeamRelation taskTeamRelation) {
        taskTeamRelation.setCreateTime(DateUtils.getNowDate());
        return taskTeamRelationMapper.insertTaskTeamRelation(taskTeamRelation);
    }

    /**
     * 修改任务和检测组关系
     *
     * @param taskTeamRelation 任务和检测组关系
     * @return 结果
     */
    @Override
    public int updateTaskTeamRelation(TaskTeamRelation taskTeamRelation) {
        taskTeamRelation.setUpdateTime(DateUtils.getNowDate());
        return taskTeamRelationMapper.updateTaskTeamRelation(taskTeamRelation);
    }

    /**
     * 批量删除任务和检测组关系
     *
     * @param ids 需要删除的任务和检测组关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskTeamRelationByIds(Long[] ids) {
        return taskTeamRelationMapper.deleteTaskTeamRelationByIds(ids);
    }

    /**
     * 删除任务和检测组关系信息
     *
     * @param id 任务和检测组关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskTeamRelationById(Long id) {
        return taskTeamRelationMapper.deleteTaskTeamRelationById(id);
    }
}
