package com.tunnel.service.impl;

import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.FileRecord;
import com.tunnel.mapper.FileRecordMapper;
import com.tunnel.service.FileRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @author: 专业bug开发（kk）
 * @date: 2025年04月18日 14:16
 */
@Slf4j
@Service
public class FileRecordServiceImpl implements FileRecordService {

    @Resource
    private FileRecordMapper fileRecordMapper;

    @DataScope(deptAlias = "de")
    @Override
    public List<FileRecord> selectFileRecordList(FileRecord fileRecord) {
        return fileRecordMapper.selectFileRecordList(fileRecord);
    }

    @Override
    public int insertFileRecord(FileRecord fileRecord) {
        fileRecord.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        fileRecord.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        return fileRecordMapper.insertFileRecord(fileRecord);
    }

    @Override
    public int updateFileRecord(FileRecord fileRecord) {
        return fileRecordMapper.updateFileRecord(fileRecord);
    }

    @Override
    public int deleteFileRecordByIds(List<Long> ids) {
        return fileRecordMapper.deleteFileRecordByIds(ids);
    }
}
