package com.tunnel.service.impl;

import com.tunnel.CommonBaseEntityUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.core.domain.entity.SysDept;
import com.tunnel.common.core.domain.model.LoginUser;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.Road;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.RoadService;
import com.tunnel.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Slf4j
@Service
public class RoadServiceImpl implements RoadService {
    @Autowired
    private RoadMapper roadMapper;

    @Resource
    private ISysDeptService iSysDeptService;

    /**
     * 查询基础数据
     *
     * @param id 基础数据主键
     * @return 基础数据
     */
    @Override
    public Road selectScRoadById(Long id) {
        return roadMapper.selectScRoadById(id);
    }

    /**
     * 查询基础数据列表
     *
     * @param road 基础数据
     * @return 基础数据
     */
    @DataScope(deptAlias = "de")
    @Override
    public List<Road> selectScRoadList(Road road) {
        List<Road> list = roadMapper.selectScRoadList(road);
        CommonBaseEntityUtil.initUserNameByList(list);
        return list;
    }

    /**
     * 新增基础数据
     *
     * @param road 基础数据
     * @return 结果
     */
    @Override
    public int insertScRoad(Road road) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        road.setCreator(loginUser.getUserId());
        road.setCreateTime(DateUtils.getNowDate());
        //根据管养单位设置depId
        getDeptId(road);
        return roadMapper.insertScRoad(road);
    }

    /**
     * 修改基础数据
     *
     * @param road 基础数据
     * @return 结果
     */
    @Override
    public int updateScRoad(Road road) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        road.setModifier(loginUser.getUserId());
        road.setUpdateTime(DateUtils.getNowDate());
        getDeptId(road);
        return roadMapper.updateScRoad(road);
    }

    private void getDeptId(Road road) {
        try {
            List<SysDept> deptList = iSysDeptService.selectAllDeptList();
            Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
            road.setDeptId(deptMap.get(road.getCompanyName()));
            road.setUserId(0L);
        } catch (Exception e) {
            log.error("根据管养单位设置depId错误：{}", e.getMessage());
        }
    }

    /**
     * 批量删除基础数据
     *
     * @param ids 需要删除的基础数据主键
     * @return 结果
     */
    @Override
    public int deleteScRoadByIds(Long[] ids) {
        return roadMapper.deleteScRoadByIds(ids);
    }

    /**
     * 删除基础数据信息
     *
     * @param id 基础数据主键
     * @return 结果
     */
    @Override
    public int deleteScRoadById(Long id) {
        return roadMapper.deleteScRoadById(id);
    }

    @Override
    public BatchAddResponse batchAdd(MultipartFile file) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            //生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
            ExcelUtil excelUtil = new ExcelUtil(Road.class);
            List<Road> dataList = excelUtil.importExcel(bufferedInputStream, 0);
            List<SysDept> deptList = iSysDeptService.selectAllDeptList();
            Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
            if(!CollectionUtils.isEmpty(dataList)){
                for (Road road : dataList) {
                    road.setTeamNo(1);
                    CommonBaseEntityUtil.setBaseEntityInfo(road);
                    road.setDeptId(deptMap.get(road.getCompanyName()));
                    road.setUserId(0L);
                }
                roadMapper.batchAdd(dataList);
                addResponse.setMsg("success");
                addResponse.setStatus(0);
            }else {
                addResponse.setMsg("导入数据不符合模板要求！");
                addResponse.setStatus(1);
            }
        }catch (IOException e){
            e.printStackTrace();
            addResponse.setMsg("文件解析失败:"+e.getMessage());
            addResponse.setStatus(1);
        }catch (Exception e){
            e.printStackTrace();
            addResponse.setMsg("导入数据失败:"+e.getMessage());
            addResponse.setStatus(1);
        }finally {
            try {
                if(fileStream != null){
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    @DataScope(deptAlias = "de")
    @Override
    public List<Road> listAllCompany(Road road) {
        return roadMapper.listAllCompany(road);
    }



}
