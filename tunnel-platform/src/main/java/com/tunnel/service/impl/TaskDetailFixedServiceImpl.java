package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.TaskDetailFixed;
import com.tunnel.domain.dto.NoticeTaskDetailDTO;
import com.tunnel.mapper.TaskDetailFixedMapper;
import com.tunnel.mapper.TaskDetailMapper;
import com.tunnel.service.TaskDetailFixedService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class TaskDetailFixedServiceImpl implements TaskDetailFixedService {

    @Resource
    private TaskDetailFixedMapper taskDetailFixedMapper;

    @Resource
    private TaskDetailMapper taskDetailMapper;

    @Override
    public List<TaskDetailFixed> selectTaskDetailFixedList(TaskDetailFixed taskDetailFixed) {
        return taskDetailFixedMapper.selectTaskDetailFixedList(taskDetailFixed);
    }

    @Override
    public TaskDetailFixed selectTaskDetailFixedByDetailId(Long detailId) {
        return taskDetailFixedMapper.selectTaskDetailFixedByDetailId(detailId);
    }

    @Override
    @Transactional
    public int insertTaskDetailFixed(TaskDetailFixed taskDetailFixed) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        taskDetailFixed.setCreator(userId);
        taskDetailFixed.setCreateBy(userName);
        TaskDetail taskDetail = new TaskDetail();
        taskDetail.setId(taskDetailFixed.getTaskDetailId());
        taskDetail.setIsFixed(1);
        taskDetailMapper.updateScTaskDetailIsFixed(taskDetail);
        if (taskDetailFixed.getFixTime() == null) {
            taskDetailFixed.setFixTime(new Date());
        }
        taskDetailFixed.setIsConfirm(0);
        return taskDetailFixedMapper.insertTaskDetailFixed(taskDetailFixed);
    }

    @Override
    public int updateTaskDetailFixed(TaskDetailFixed taskDetailFixed) {
        if (taskDetailFixed.getConfirmTime() == null) {
            taskDetailFixed.setConfirmTime(new Date());
        }
        return taskDetailFixedMapper.updateTaskDetailFixed(taskDetailFixed);
    }

    @Override
    public int deleteTaskDetailFixedByIds(List<Long> ids) {
        return taskDetailFixedMapper.deleteTaskDetailFixedByIds(ids);
    }
}