package com.tunnel.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.Team;
import com.tunnel.mapper.TeamMapper;
import com.tunnel.service.TeamService;
import com.tunnel.system.mapper.SysUserMapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 检测组和用户关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Service
public class TeamServiceImpl implements TeamService {
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询检测组和用户关系
     *
     * @param id 检测组和用户关系主键
     * @return 检测组和用户关系
     */
    @Override
    public Team selectTeamById(Long id) {
        return teamMapper.selectTeamById(id);
    }

    /**
     * 查询检测组和用户关系列表
     *
     * @param team 检测组和用户关系
     * @return 检测组和用户关系
     */
    @Override
    public List<Team> selectTeamList(Team team) {
        List<Team> list = teamMapper.selectTeamList(team);
        for (Team temp : list) {
            String arrays[]= temp.getUserIds().split(",");
            List<Long> userIds = Lists.newArrayList();
            for (String array : arrays) {
                userIds.add(Long.valueOf(array));
            }
            //转换成List
            List<SysUser> sysUsers=sysUserMapper.selectByUserIdList(userIds);
            List<String> nickNameList = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
            temp.setUserIds(String.join(",", nickNameList));
        }
        return list;
    }

    /**
     * 新增检测组和用户关系
     *
     * @param team 检测组和用户关系
     * @return 结果
     */
    @Override
    public int insertTeam(Team team) {
        team.setCreateTime(DateUtils.getNowDate());
        return teamMapper.insertTeam(team);
    }

    /**
     * 修改检测组和用户关系
     *
     * @param team 检测组和用户关系
     * @return 结果
     */
    @Override
    public int updateTeam(Team team) {
        team.setUpdateTime(DateUtils.getNowDate());
        return teamMapper.updateTeam(team);
    }

    /**
     * 批量删除检测组和用户关系
     *
     * @param ids 需要删除的检测组和用户关系主键
     * @return 结果
     */
    @Override
    public int deleteTeamByIds(Long[] ids) {
        return teamMapper.deleteTeamByIds(ids);
    }

    /**
     * 删除检测组和用户关系信息
     *
     * @param id 检测组和用户关系主键
     * @return 结果
     */
    @Override
    public int deleteTeamById(Long id) {
        return teamMapper.deleteTeamById(id);
    }

    @Override
    public List<Team> listAll() {
        return teamMapper.listAll();
    }
}
