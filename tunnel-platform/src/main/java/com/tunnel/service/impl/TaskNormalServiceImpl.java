package com.tunnel.service.impl;

import com.tunnel.CommonBaseEntityUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.core.domain.entity.SysDept;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.ScTaskNormal;
import com.tunnel.domain.dto.TaskDetailCountRes;
import com.tunnel.domain.dto.TaskNormalQueryDTO;
import com.tunnel.domain.vo.TaskDetailCountVO;
import com.tunnel.mapper.TaskNormalMapper;
import com.tunnel.service.TaskNormalService;
import com.tunnel.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskNormalServiceImpl implements TaskNormalService {

    @Resource
    private TaskNormalMapper taskNormalMapper;

    @Resource
    private ISysDeptService iSysDeptService;



    /**
     * 批量新增/导入
     *
     * @param file 文件
     * @return 结果
     */
    @Override
    public BatchAddResponse batchAdd(MultipartFile file) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = Files.newInputStream(Paths.get(localFile));
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
            ExcelUtil<ScTaskNormal> excelUtil = new ExcelUtil<>(ScTaskNormal.class);
            List<ScTaskNormal> dataList = excelUtil.importExcel(bufferedInputStream, 0);
            if(!CollectionUtils.isEmpty(dataList)){
                //日期去重
                List<String> distinctTaskDateList = dataList.stream().map(ScTaskNormal::getTaskDate).distinct().collect(Collectors.toList());
                if (distinctTaskDateList.size() > 1) {
                    throw new ServiceException("请按月份导入数据，当前数据包含多个月份");
                }
                //校验本月是否有数据
                int haveNum = taskNormalMapper.queryCountByTaskDate(distinctTaskDateList.get(0));
                if (haveNum > 0) {
                    throw new ServiceException("当月已导入数据，如需再次导入请先删除当月数据！");
                }
                //设置deptId
                List<SysDept> deptList = iSysDeptService.selectAllDeptList();
                Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
                for (ScTaskNormal taskNormal : dataList) {
                    CommonBaseEntityUtil.setBaseEntityInfo(taskNormal);
                    taskNormal.setDeptId(deptMap.getOrDefault(taskNormal.getCompanyName(), 0L));
                    taskNormal.setUserId(0L);
                    //转换整治状态
                    taskNormal.setIsFixed("已整改".equals(taskNormal.getFixedString()) ? 1 : 0);
                }
                taskNormalMapper.batchAdd(dataList);
                addResponse.setMsg("success");
                addResponse.setStatus(0);
            }else {
                addResponse.setMsg("导入数据不符合模板要求！");
                addResponse.setStatus(1);
            }
        }catch (IOException e){
            log.error("文件解析失败:{}", e.getMessage());
            addResponse.setMsg("文件解析失败:" + e.getMessage());
            addResponse.setStatus(1);
        }catch (Exception e){
            log.error("导入数据失败:{}", e.getMessage());
            addResponse.setMsg("导入数据失败:"+e.getMessage());
            addResponse.setStatus(1);
        }finally {
            try {
                if(fileStream != null){
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e.getMessage());
            }
        }
        return addResponse;
    }

    /**
     * 按公司汇总后的列表
     *
     */
    @DataScope(deptAlias = "de")
    @Override
    public TaskDetailCountRes listByCompanyPage(TaskNormalQueryDTO queryDTO) {
        TaskDetailCountRes res = new TaskDetailCountRes();
        //根据日期和公司获取数据
        List<ScTaskNormal> taskNormalList = taskNormalMapper.selectByTaskDateOrCompany(queryDTO);
        if (CollectionUtils.isEmpty(taskNormalList)) {
            res.setTotal(0);
            res.setResult(new ArrayList<>());
            return res;
        }
        //根据公司分组
        Map<String, List<ScTaskNormal>> groupByCompanyMap = taskNormalList.stream().collect(Collectors.groupingBy(ScTaskNormal::getCompanyName));
        List<TaskDetailCountVO> voList = new ArrayList<>();
        groupByCompanyMap.forEach((key, list) -> {
            TaskDetailCountVO vo = new TaskDetailCountVO();
            vo.setCompanyName(key);
            vo.setTaskDate(queryDTO.getTaskDate());
            List<Long> idList = list.stream().map(ScTaskNormal::getId).collect(Collectors.toList());
            vo.setIdList(idList);
            int totalCount = list.size();
            vo.setTotalCount(totalCount);
            long fixedCount = list.stream().filter(dto -> Objects.equals(1, dto.getIsFixed())).count();
            double fixedRate;
            double rate = (fixedCount * 100.0) / totalCount;
            fixedRate = BigDecimal.valueOf(rate)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue();
            vo.setFixedCount((int) fixedCount);
            vo.setPendingCount(vo.getTotalCount() - vo.getFixedCount());
            vo.setFixedRate(fixedRate);
            voList.add(vo);
        });
        res.setTotal(voList.size());
        res.setResult(voList);
        return res;
    }

    /**
     * 根据id批量查询
     *
     * @param idList
     * @return
     */
    @Override
    public List<ScTaskNormal> queryByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return taskNormalMapper.queryByIdList(idList);
    }

    /**
     * 根据taskDate删除
     *
     * @param taskDate
     */
    @Override
    public Integer deleteByTaskDate(String taskDate) {
        return taskNormalMapper.deleteByTaskDate(taskDate);
    }
}
