package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.tunnel.common.core.domain.entity.SysRole;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.*;
import com.tunnel.domain.stat.*;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.mapper.TaskDetailMapper;
import com.tunnel.mapper.TaskEvaluateMapper;
import com.tunnel.service.StatService;
import com.tunnel.service.TaskEvaluateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基础数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Slf4j
@Service
public class StatServiceImpl implements StatService {

    @Resource
    private RoadMapper roadMapper;

    @Resource
    private TaskDetailMapper taskDetailMapper;

    @Resource
    private TaskEvaluateMapper taskEvaluateMapper;

    @Resource
    private TaskEvaluateService taskEvaluateService;

    @Override
    public ResultStat queryStatInfo(Task task) {
        if(StringUtils.isEmpty(task.getTaskDate())){
            throw new ServiceException("任务月份不能为空");
        }
        ResultStat resultStat=new ResultStat();
        //问题统计
        RoadStat roadStat=roadMapper.selectStatInfo();
        //检测里程统计
        BigDecimal checkMileage=taskDetailMapper.sumCheckMileage(task);
        roadStat.setCheckMileage(checkMileage);
        resultStat.setRoadStat(roadStat);
        //近六个月的巡查情况
        MonthScore monthScore = this.initMonthScore();
        resultStat.setMonthScore(monthScore);
        //评分占比-环图
        List<CircleStat> circleList=this.initCircle(task);
        resultStat.setCircleList(circleList);
        //病害类型统计-饼图
        List<CircleStat> pieList=this.initPie(task);
        resultStat.setPieList(pieList);
        //路段问题统计
        TaskEvaluate taskEvaluate = new TaskEvaluate();
        taskEvaluate.setTaskDate(task.getTaskDate());
        taskEvaluate.setType(task.getType());
        List<TaskEvaluate> roadRankList=taskEvaluateMapper.selectTaskEvaluateList(taskEvaluate);
        roadRankList=roadRankList.stream().sorted(Comparator.comparing(TaskEvaluate::getScore).reversed()).collect(Collectors.toList());
        resultStat.setRoadRankList(roadRankList);
        //管养单位平均分排名
        List<RankStat> companyRankList=taskEvaluateMapper.selectGroupByCompanyName(task);
        List<RankStat> rankStatList=taskEvaluateService.matchCompanyList(taskEvaluate);
        companyRankList.addAll(rankStatList);
        Map<String,RankStat> map = companyRankList.stream().collect(Collectors.toMap(RankStat::getCompanyName, Function.identity(), (key1, key2) -> key1));
        List<String> companylist=Lists.newArrayList("湖北交投鄂东高速公路运营管理有限公司","湖北交投鄂西高速公路运营管理有限公司","湖北交投鄂西北高速公路运营管理有限公司","湖北交投江汉高速公路运营管理有限公司","湖北交投襄阳高速公路运营管理有限公司","湖北交投京珠高速公路运营管理有限公司","湖北交投随岳高速公路运营管理有限公司","湖北交投武黄高速公路运营管理有限公司","湖北交投宜昌高速公路运营管理有限公司","湖北楚天智能交通股份有限公司","湖北联合交通投资开发有限公司","中交投资（湖北）运营管理有限公司","中交资产管理有限公司湖北区域管理总部","湖北武荆高速公路发展有限公司","武汉交投高速公路运营管理有限公司","湖北武麻高速公路有限公司","越秀（湖北）高速公路有限公司","湖北随岳南高速公路有限公司","葛洲坝湖北襄荆高速公路有限公司","湖北荆宜高速公路有限公司","荆州市路桥投资开发有限公司","湖北荆东高速公路建设开发有限公司","湖北鄂东长江公路大桥有限公司","湖北汉洪东荆河桥梁建设管理有限公司","湖北汉孝高速公路建设经营有限公司","湖北樊魏高速公路有限公司","华益路桥管理有限公司","湖北老谷高速公路开发有限公司","武汉青山长江大桥建设有限公司","宜昌长江大桥建设营运集团有限公司","湖北黄石武阳高速公路发展有限公司","武汉市武阳高速公路投资管理有限公司","湖北交投实业发展有限公司");
        List<Integer> manageCountList=Lists.newArrayList(12,8,11,10,10,10,6,8,6,5,9,3,3,1,8,2,3,1,1,2,3,1,2,1,3,1,1,1,1,1,1,1,10);
        List<RankStat> resultList=Lists.newArrayList();
        for (int i = 0; i < companylist.size(); i++) {
            String company=companylist.get(i);
            RankStat rankStat=new RankStat();
            if(map.containsKey(company)){
                rankStat.setCompany(map.get(company).getCompany());
                rankStat.setManageCount(manageCountList.get(i));
                rankStat.setScore(map.get(company).getScore());
            }else{
                rankStat.setCompany(company);
                rankStat.setManageCount(0);
                rankStat.setScore(BigDecimal.ZERO);
            }
            resultList.add(rankStat);
        }
        resultStat.setCompanyRankList(resultList);
        return resultStat;
    }



    @Override
    public ResultStat queryStatInfoCount(Task task) {
        if(StringUtils.isEmpty(task.getTaskDate())){
            throw new ServiceException("任务月份不能为空");
        }
        ResultStat resultStat=new ResultStat();
        //路段基本信息统计
        RoadStat roadStat=roadMapper.selectStatInfo();
        //检测里程统计
        BigDecimal checkMileage=taskDetailMapper.sumCheckMileage(task);
        roadStat.setCheckMileage(checkMileage);
        resultStat.setRoadStat(roadStat);
        //路段月度均分
        MonthScore monthScore = this.initMonthCount(task);
        resultStat.setMonthScore(monthScore);
        //评分占比-环图
        List<CircleStat> circleList=this.initCircle(task);
        resultStat.setCircleList(circleList);
        //病害类型统计-饼图
        List<CircleStat> pieList=this.initPie(task);
        resultStat.setPieList(pieList);
        //路段总分排名
        TaskEvaluate taskEvaluate = new TaskEvaluate();
        taskEvaluate.setTaskDate(task.getTaskDate());
        taskEvaluate.setType(task.getType());
        List<TaskEvaluate> roadRankList=taskEvaluateMapper.selectTaskDetailCountList(taskEvaluate);
        Map<Long,Integer> roadRankMap=roadRankList.stream().collect(Collectors.toMap(TaskEvaluate::getTaskId, TaskEvaluate::getTotalCount, (o1, o2) -> o1));

        List<Long> taskIds = roadRankList.stream().map(TaskEvaluate::getTaskId).collect(Collectors.toList());
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        log.info("当前用户角色：{}", roleList);
        //特殊处理
        List<TaskDetail> taskDetailList = new ArrayList<>();
        if (DateUtils.isBefore(task.getTaskDate(), "2025-06") || roleList.contains("JCDW")) {
            log.info("特殊查询，不过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIds(taskIds));
        } else {
            log.info("非特殊查询，过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIdsWithStatus(taskIds));
        }
        Map<Long, List<TaskDetail>> taskDetailMap = taskDetailList.stream().collect(Collectors.groupingBy(TaskDetail::getTaskId));

        List<TaskEvaluate> roadScoreList=taskEvaluateMapper.selectTaskEvaluateList(taskEvaluate);
        roadScoreList=roadScoreList.stream().sorted(Comparator.comparing(TaskEvaluate::getScore).reversed()).collect(Collectors.toList());
        for (TaskEvaluate evaluate : roadScoreList) {
            if(roadRankMap.containsKey(evaluate.getTaskId())){
                evaluate.setCount(roadRankMap.get(evaluate.getTaskId()));
            }
            if(taskDetailMap.containsKey(evaluate.getTaskId())){
                List<TaskDetail> allDetailList = taskDetailMap.get(evaluate.getTaskId());
                int totalCount = allDetailList.size();
                evaluate.setTotalCount(totalCount);
                long fixedCount = allDetailList.stream()
                        .filter(detail -> Objects.equals(1, detail.getIsFixed())).count();
                evaluate.setFixedCount((int) fixedCount);
                evaluate.setPendingCount(evaluate.getTotalCount() - evaluate.getFixedCount());
                double fixedRate;
                double rate = (fixedCount * 100.0) / totalCount;
                fixedRate = BigDecimal.valueOf(rate)
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue();
                evaluate.setFixedRate(fixedRate);
            } else {
                evaluate.setTotalCount(0);
                evaluate.setFixedCount(0);
                evaluate.setPendingCount(0);
                evaluate.setFixedRate(0.00);
            }
        }
        resultStat.setRoadRankList(roadScoreList);
        //管养单位平均分排名
        List<RankStat> companyRankList=taskEvaluateMapper.countGroupByCompanyName(task);
        List<RankStat> rankStatList=taskEvaluateService.matchCompanyList(taskEvaluate);
        List<RankStat> companyRankScoreList=taskEvaluateMapper.selectGroupByCompanyNameStat(task);
        Map<String,BigDecimal> companyRankMap=companyRankScoreList.stream().collect(Collectors.toMap(RankStat::getCompanyName, RankStat::getScore, (key1, key2) -> key1));
        companyRankList.addAll(rankStatList);
        Map<String,RankStat> map = companyRankList.stream().collect(Collectors.toMap(RankStat::getCompanyName, Function.identity(), (key1, key2) -> key1));

        List<Road> roadList=roadMapper.selectDistinctCompanyNameList();
        List<RankStat> resultList=Lists.newArrayList();
        for (int i = 0; i < roadList.size(); i++) {
            Road road = roadList.get(i);
            String company=road.getCompanyName();
            RankStat rankStat=new RankStat();
            if(map.containsKey(company)){
                rankStat.setCompany(map.get(company).getCompany());
                rankStat.setTotalCount(map.get(company).getTotalCount());
            }else{
                rankStat.setCompany(company);
                rankStat.setTotalCount(0);
            }
            if(companyRankMap.containsKey(company)){
                rankStat.setScore(companyRankMap.get(company));
            }else{
                rankStat.setScore(new BigDecimal(100));
            }
            resultList.add(rankStat);
        }
        //把resultList使用score分数倒排序
        resultList.sort(Comparator.comparing(RankStat::getScore).reversed());
        resultStat.setCompanyRankList(resultList);
        return resultStat;
    }

    @Override
    public ResultStatNew queryStatInfoCountNew(Task task) {
        task.setCreateTime(DateUtil.parse("2025-04-21 00:00:00"));
        ResultStatNew resultStat=new ResultStatNew();
        //路段基本信息统计
        QuestionStat questionStat=taskDetailMapper.selectCountByTaskDate(task);
        resultStat.setQuestionStat(questionStat);

        //近六个月巡查情况
        MonthScore monthScore = this.initMonthCountNew(task);
        resultStat.setMonthScore(monthScore);

        //问题类型统计-柱状图
        List<ZhuStat> zhuList = this.initZhu(task);
        resultStat.setZhuList(zhuList);
        //病害类型统计-饼图
        List<CircleStat> pieList=this.initPie(task);
        resultStat.setPieList(pieList);
        //路段总分排名
        TaskEvaluate taskEvaluate = new TaskEvaluate();
        taskEvaluate.setTaskDate(task.getTaskDate());
        taskEvaluate.setType(task.getType());
        List<TaskDetail> roadRankList=taskDetailMapper.selectCountByRoad(task);

        List<Long> roadIdList=roadRankList.stream().map(TaskDetail::getRoadId).distinct().collect(Collectors.toList());
        //查询各个路段的分数的平均值,时间范围内和公司范围内的
        List<TaskEvaluate> roadRankScoreList=taskEvaluateMapper.selectTaskEvaluateByRoadIdList(roadIdList,task);
        Map<Long,TaskEvaluate> roadMap = roadRankScoreList.stream().collect(Collectors.toMap(TaskEvaluate::getRoadId, Function.identity(), (key1, key2) -> key1));
        for (TaskDetail taskDetail : roadRankList) {
            if(roadMap.containsKey(taskDetail.getRoadId())){
                taskDetail.setScore(roadMap.get(taskDetail.getRoadId()).getScore());
            }else{
                taskDetail.setScore(BigDecimal.ZERO);
            }
        }
        //roadRankList按照score进行倒排
        roadRankList=roadRankList.stream().sorted(Comparator.comparing(TaskDetail::getScore).reversed()).collect(Collectors.toList());
        resultStat.setRoadRankList(roadRankList);
        //管养单位平均分排名
        List<TaskDetail> companyRankList=taskDetailMapper.selectCountByCompanyName(task);
        List<RankStat> companyRankScoreList=taskEvaluateMapper.selectGroupByCompanyName(task);

//        List<RankStat> rankStatList=taskEvaluateService.matchCompanyList(taskEvaluate);
//        for (RankStat rankStat : rankStatList) {
//            TaskDetail taskDetail = new TaskDetail();
//            taskDetail.setCompany(rankStat.getCompanyName());
//            taskDetail.setScore(rankStat.getScore());
//            taskDetail.setCompanyName(rankStat.getCompanyName());
//            companyRankList.add(taskDetail);
//        }

        Map<String,RankStat> companyMap = companyRankScoreList.stream().collect(Collectors.toMap(RankStat::getCompanyName, Function.identity(), (key1, key2) -> key1));

        Map<String,TaskDetail> map = companyRankList.stream().collect(Collectors.toMap(TaskDetail::getCompanyName, Function.identity(), (key1, key2) -> key1));
        List<Road> roadList=roadMapper.selectDistinctCompanyNameList();
        Map<String,String> companyRoadMap = roadList.stream().collect(Collectors.toMap(Road::getCompanyName, Road::getCompany, (key1, key2) -> key1));
//        List<Integer> manageCountList=Lists.newArrayList(12,8,11,10,10,10,6,8,6,5,9,3,3,1,8,2,3,1,1,2,3,1,2,1,3,1,1,1,1,1,1,1,10);
        List<RankStat> resultList=Lists.newArrayList();
        for (int i = 0; i < roadList.size(); i++) {
            Road road=roadList.get(i);
            String company=road.getCompanyName();
            RankStat rankStat=new RankStat();
            if(map.containsKey(company)){
                rankStat.setCompany(map.get(company).getCompany());
                rankStat.setCompanyName(company);
//                rankStat.setManageCount(manageCountList.get(i));
                rankStat.setTotalCount(map.get(company).getNum());
                rankStat.setNeedFixCount(map.get(company).getNeedFixCount());
            }else{
                if(companyRoadMap.containsKey(company)){
                    rankStat.setCompany(companyRoadMap.get(company));
                }else{
                    rankStat.setCompany(company);
                }
                rankStat.setCompanyName(company);
                rankStat.setManageCount(0);
            }
            if(companyMap.containsKey(company)){
                rankStat.setScore(companyMap.get(company).getScore());
            }else{
                rankStat.setScore(new BigDecimal(100));
            }
            resultList.add(rankStat);
        }
        //把resultList使用score分数倒排序
//        resultList.sort(Comparator.comparing(RankStat::getScore).reversed());
        resultStat.setCompanyRankList(resultList);
        return resultStat;
    }

    private List<ZhuStat> initZhu(Task task) {
        List<ZhuStat> taskDetailList=taskDetailMapper.selectQuestionGroupByTaskDate(task);
        return taskDetailList;
    }

    @Override
    public ResultStatNew queryStatInfoNew(Task task) {
        return null;
    }

    @Override
    public List<TaskDetail> queryMapInfo(MapQueryDTO mapInfo) {
        return taskDetailMapper.selectByMapInfo(mapInfo);
    }

    private List<CircleStat> initPie(Task task) {
        List<CircleStat> circleList= Lists.newArrayList();
        List<TaskDetail> taskDetailList=taskDetailMapper.selectGroupByTaskDate(task);
        List<TaskDetail> top6List=taskDetailList;
        if(!CollectionUtils.isEmpty(taskDetailList) && taskDetailList.size()>6){
            top6List=taskDetailList.subList(0,6);
            List<TaskDetail> otherList=taskDetailList.subList(6,taskDetailList.size());
            if(otherList.size()>0){
                TaskDetail other=new TaskDetail();
                other.setCheckContent("其他");
                other.setScore(otherList.stream().map(TaskDetail::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                top6List.add(other);
            }
        }
        BigDecimal total=top6List.stream().map(TaskDetail::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (TaskDetail taskDetail : top6List) {
            CircleStat circleStat = new CircleStat();
            circleStat.setName((taskDetail.getScore().multiply(new BigDecimal(100))).divide(total,2, RoundingMode.HALF_UP)+"%"+taskDetail.getCheckContent());
            if(circleStat.getName().length()>15){
                circleStat.setName(circleStat.getName().substring(0,15)+"...");
            }
            circleStat.setValue(taskDetail.getScore());
            circleStat.setIndexName(taskDetail.getCheckContent());
            circleList.add(circleStat);
        }
        return circleList;
    }


    /**
     * 评分占比-环图
     * @param task
     */
    private List<CircleStat> initCircle(Task task){
        List<CircleStat> circleList= Lists.newArrayList();
        PieStat pieStat=taskEvaluateMapper.selectCircleMapList(task);
        CircleStat above90 = new CircleStat();
        DecimalFormat df = new DecimalFormat("0.00");
        above90.setName("90分以上"+pieStat.getAbove90()+"个,占比"+df.format(Double.valueOf(pieStat.getAbove90())/Double.valueOf(pieStat.getTotal())*100)+"%");
        above90.setValue(divide(pieStat.getAbove90(), pieStat.getTotal()));
        circleList.add(above90);

        CircleStat bet80and90 = new CircleStat();
        bet80and90.setName("80到90分"+pieStat.getBet80and90()+"个,占比"+df.format(Double.valueOf(pieStat.getBet80and90())/Double.valueOf(pieStat.getTotal())*100)+"%");
        bet80and90.setValue(divide(pieStat.getBet80and90(), pieStat.getTotal()));
        circleList.add(bet80and90);

        CircleStat bet70and80 = new CircleStat();
        bet70and80.setName("70到80分"+pieStat.getBet70and80()+"个,占比"+df.format(Double.valueOf(pieStat.getBet70and80())/Double.valueOf(pieStat.getTotal())*100)+"%");
        bet70and80.setValue(divide(pieStat.getBet70and80(), pieStat.getTotal()));
        circleList.add(bet70and80);

        CircleStat bet60and70 = new CircleStat();
        bet60and70.setName("70到80分"+pieStat.getBet60and70()+"个,占比"+df.format(Double.valueOf(pieStat.getBet60and70())/Double.valueOf(pieStat.getTotal())*100)+"%");
        bet60and70.setValue(divide(pieStat.getBet60and70(), pieStat.getTotal()));
        circleList.add(bet60and70);

        CircleStat below60 = new CircleStat();
        below60.setName("60分以下"+pieStat.getBelow60()+"个,占比"+pieStat.getBelow60()/pieStat.getTotal()*100+"%");
        below60.setValue(divide(pieStat.getBelow60(), pieStat.getTotal()));
        circleList.add(below60);
        return circleList;
    }




    private BigDecimal divide(int a, int b) {
        if(b<=0){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(a).divide(new BigDecimal(b), 2, BigDecimal.ROUND_HALF_UP);
    }


    private MonthScore initMonthScore(){
        MonthScore monthScore = new MonthScore();
        List<TaskEvaluate> taskEvaluateList=taskEvaluateMapper.selectGroupByTaskDate();
        List<String> monthList=Lists.newArrayList();
        List<String> monthListTemp=taskEvaluateList.stream().map(TaskEvaluate::getTaskDate).distinct().collect(Collectors.toList());
        monthList.addAll(monthListTemp);
        monthScore.setMonthList(monthList);
        List<BigDecimal> roadScoreList=Lists.newArrayList();
        List<BigDecimal> roadScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),1)).map(TaskEvaluate::getScore).collect(Collectors.toList());
        roadScoreList.addAll(roadScoreListTemp);
        monthScore.setRoadScoreList(roadScoreList);
        List<BigDecimal> envScoreList=Lists.newArrayList();
        List<BigDecimal> envScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),2)).map(TaskEvaluate::getScore).collect(Collectors.toList());
        envScoreList.addAll(envScoreListTemp);
        monthScore.setEnvScoreList(envScoreList);
        return monthScore;
    }

    private MonthScore initMonthCount(Task task){
        MonthScore monthScore = new MonthScore();
        List<TaskEvaluate> taskEvaluateList=taskEvaluateMapper.countGroupByTaskDate(task);
        List<String> monthList=Lists.newArrayList();
        List<String> monthListTemp=taskEvaluateList.stream().map(TaskEvaluate::getTaskDate).distinct().collect(Collectors.toList());
        monthList.addAll(monthListTemp);
        monthScore.setMonthList(monthList);
        List<Integer> roadScoreList=Lists.newArrayList();
        List<Integer> roadScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),1)).map(TaskEvaluate::getCount).collect(Collectors.toList());
        roadScoreList.addAll(roadScoreListTemp);
        List<BigDecimal> scoreList=Lists.newArrayList();
        for (Integer count : roadScoreList) {
            scoreList.add(BigDecimal.valueOf(count));
        }
        monthScore.setRoadScoreList(scoreList);
        List<Integer> envScoreList=Lists.newArrayList();
        List<Integer> envScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),2)).map(TaskEvaluate::getCount).collect(Collectors.toList());
        envScoreList.addAll(envScoreListTemp);
        List<BigDecimal> envScore=Lists.newArrayList();
        for (Integer count : envScoreList) {
            envScore.add(BigDecimal.valueOf(count));
        }
        monthScore.setEnvScoreList(envScore);
        return monthScore;
    }



    private MonthScore initMonthCountNew(Task task){
        MonthScore monthScore = new MonthScore();
        List<TaskEvaluate> taskEvaluateList=taskEvaluateMapper.countGroupByTaskDate(task);
        List<String> monthList=Lists.newArrayList();
        List<String> monthListTemp=taskEvaluateList.stream().map(TaskEvaluate::getTaskDate).distinct().collect(Collectors.toList());
        monthList.addAll(monthListTemp);
        monthScore.setMonthList(monthList);
        List<Integer> roadScoreList=Lists.newArrayList();
        List<Integer> roadScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),1)).map(TaskEvaluate::getCount).collect(Collectors.toList());
        roadScoreList.addAll(roadScoreListTemp);
        List<BigDecimal> scoreList=Lists.newArrayList();
        for (Integer count : roadScoreList) {
            scoreList.add(BigDecimal.valueOf(count));
        }
        monthScore.setRoadScoreList(scoreList);
        List<Integer> envScoreList=Lists.newArrayList();
        List<Integer> envScoreListTemp=taskEvaluateList.stream().filter(v-> Objects.equals(v.getType(),2)).map(TaskEvaluate::getCount).collect(Collectors.toList());
        envScoreList.addAll(envScoreListTemp);
        List<BigDecimal> envScore=Lists.newArrayList();
        for (Integer count : envScoreList) {
            envScore.add(BigDecimal.valueOf(count));
        }
        monthScore.setEnvScoreList(envScore);
        return monthScore;
    }

}
