package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.CheckEnumRes;
import com.tunnel.mapper.CheckEnumMapper;
import com.tunnel.service.CheckEnumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 公路检测类别问题项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class CheckEnumServiceImpl implements CheckEnumService
{
    @Autowired
    private CheckEnumMapper checkEnumMapper;

    /**
     * 查询公路检测类别问题项
     * 
     * @param id 公路检测类别问题项主键
     * @return 公路检测类别问题项
     */
    @Override
    public CheckEnum selectCheckEnumById(Long id)
    {
        return checkEnumMapper.selectCheckEnumById(id);
    }

    /**
     * 查询公路检测类别问题项列表
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 公路检测类别问题项
     */
    @Override
    public List<CheckEnum> selectCheckEnumList(CheckEnum checkEnum)
    {
        return checkEnumMapper.selectCheckEnumList(checkEnum);
    }

    /**
     * 新增公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    @Override
    public int insertCheckEnum(CheckEnum checkEnum)
    {
        checkEnum.setCreateTime(DateUtils.getNowDate());
        return checkEnumMapper.insertCheckEnum(checkEnum);
    }

    /**
     * 修改公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    @Override
    public int updateCheckEnum(CheckEnum checkEnum)
    {
        checkEnum.setUpdateTime(DateUtils.getNowDate());
        return checkEnumMapper.updateCheckEnum(checkEnum);
    }

    /**
     * 批量删除公路检测类别问题项
     * 
     * @param ids 需要删除的公路检测类别问题项主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumByIds(Long[] ids)
    {
        return checkEnumMapper.deleteCheckEnumByIds(ids);
    }

    /**
     * 删除公路检测类别问题项信息
     * 
     * @param id 公路检测类别问题项主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumById(Long id)
    {
        return checkEnumMapper.deleteCheckEnumById(id);
    }

    @Override
    public List<String> distinctListByType(CheckEnum checkEnum) {
        return checkEnumMapper.distinctListByType(checkEnum);
    }

    @Override
    public CheckEnumRes queryDefaultScoreList(CheckEnum checkEnum) {
        BigDecimal roadScore = checkEnumMapper.queryDefaultScoreList(checkEnum);
        BigDecimal envScore = checkEnumMapper.queryDefaultEnvScoreList(checkEnum);
        if(Objects.isNull(envScore)){
            envScore=BigDecimal.ZERO;
        }
        CheckEnumRes checkEnumRes = new CheckEnumRes();
        checkEnumRes.setRoadScore(roadScore);
        checkEnumRes.setEnvScore(envScore);
        return checkEnumRes;
    }

    @Override
    public List<CheckEnum> listDistinctCheckContent() {
        return checkEnumMapper.listDistinctCheckContent();
    }

    @Override
    public List<BigDecimal> distinctListScoreAll() {
        return checkEnumMapper.distinctListScoreAll();
    }

}
