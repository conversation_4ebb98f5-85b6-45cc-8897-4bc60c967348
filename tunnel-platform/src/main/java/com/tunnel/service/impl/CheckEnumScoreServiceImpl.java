package com.tunnel.service.impl;

import java.util.List;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.CheckEnumScore;
import com.tunnel.mapper.CheckEnumScoreMapper;
import com.tunnel.service.CheckEnumScoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 检测类别问题项-最大分值Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class CheckEnumScoreServiceImpl implements CheckEnumScoreService
{
    @Autowired
    private CheckEnumScoreMapper checkEnumScoreMapper;

    /**
     * 查询检测类别问题项-最大分值
     * 
     * @param id 检测类别问题项-最大分值主键
     * @return 检测类别问题项-最大分值
     */
    @Override
    public CheckEnumScore selectCheckEnumScoreById(Long id)
    {
        return checkEnumScoreMapper.selectCheckEnumScoreById(id);
    }

    /**
     * 查询检测类别问题项-最大分值列表
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 检测类别问题项-最大分值
     */
    @Override
    public List<CheckEnumScore> selectCheckEnumScoreList(CheckEnumScore checkEnumScore)
    {
        return checkEnumScoreMapper.selectCheckEnumScoreList(checkEnumScore);
    }

    /**
     * 新增检测类别问题项-最大分值
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 结果
     */
    @Override
    public int insertCheckEnumScore(CheckEnumScore checkEnumScore)
    {
        checkEnumScore.setCreateTime(DateUtils.getNowDate());
        return checkEnumScoreMapper.insertCheckEnumScore(checkEnumScore);
    }

    /**
     * 修改检测类别问题项-最大分值
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 结果
     */
    @Override
    public int updateCheckEnumScore(CheckEnumScore checkEnumScore)
    {
        checkEnumScore.setUpdateTime(DateUtils.getNowDate());
        return checkEnumScoreMapper.updateCheckEnumScore(checkEnumScore);
    }

    /**
     * 批量删除检测类别问题项-最大分值
     * 
     * @param ids 需要删除的检测类别问题项-最大分值主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumScoreByIds(Long[] ids)
    {
        return checkEnumScoreMapper.deleteCheckEnumScoreByIds(ids);
    }

    /**
     * 删除检测类别问题项-最大分值信息
     * 
     * @param id 检测类别问题项-最大分值主键
     * @return 结果
     */
    @Override
    public int deleteCheckEnumScoreById(Long id)
    {
        return checkEnumScoreMapper.deleteCheckEnumScoreById(id);
    }
}
