package com.tunnel.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateUtil;
import com.tunnel.CommonBaseEntityUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.core.domain.entity.SysRole;
import com.tunnel.common.core.page.PageDomain;
import com.tunnel.common.core.page.TableSupport;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.file.AliYunOSSFileUtil;
import com.tunnel.domain.*;
import com.tunnel.domain.dto.*;
import com.tunnel.domain.vo.TaskDetailCountVO;
import com.tunnel.mapper.*;
import com.tunnel.service.TaskDetailService;
import com.tunnel.service.TaskEvaluateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tunnel.common.utils.PageUtils.startPage;

/**
 * 检测列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
@Slf4j
public class TaskDetailServiceImpl implements TaskDetailService {
    //base64文件前缀
    public static final String DATA_IMAGE = "data:image";
    @Autowired
    private TaskDetailMapper taskDetailMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;
    @Resource
    private TaskMapper taskMapper;
    @Autowired
    private CheckEnumRelationMapper checkEnumRelationMapper;
    @Autowired
    private TaskEvaluateMapper taskEvaluateMapper;
    @Resource
    private TaskEvaluateService taskEvaluateService;
    @Resource
    private TaskDetailFixedMapper taskDetailFixedMapper;

    /**
     * 查询检测列表
     *
     * @param id 检测列表主键
     * @return 检测列表
     */
    @Override
    public TaskDetail selectScTaskDetailById(Long id) {
        return taskDetailMapper.selectScTaskDetailById(id);
    }

    /**
     * 查询检测列表列表
     *
     * @param scTaskDetail 检测列表
     * @return 检测列表
     */
    @DataScope(deptAlias = "de")
    @Override
    public List<TaskDetail> selectScTaskDetailList(TaskDetail scTaskDetail) {
        Integer fixTimeRange = scTaskDetail.getFixTimeRange();
        if (fixTimeRange != null) {
            scTaskDetail.setSearchStartTime(new Date());
            LocalDate futureDate = LocalDate.now().plusDays(fixTimeRange);
            // 转换为当天的00:00时间（系统默认时区）
            LocalDateTime futureDateTime = futureDate.atTime(23, 59, 59);
            // 结合时区转为ZonedDateTime，再转化为Date
            ZonedDateTime zonedDateTime = futureDateTime.atZone(ZoneId.systemDefault());
            scTaskDetail.setSearchEndTime(Date.from(zonedDateTime.toInstant()));
        }
        List<TaskDetail> list = taskDetailMapper.selectScTaskDetailList(scTaskDetail);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<String> enumList = list.stream().map(TaskDetail::getItemId).collect(Collectors.toList());
        Set<Long> itemIdSet = new HashSet<>();
        for (String item : enumList) {
            for (String itemId : item.split("&")) {
                itemIdSet.add(Long.valueOf(itemId));
            }
        }
        List<CheckEnum> itemList = checkEnumMapper.selectListByIds(Lists.newArrayList(itemIdSet.iterator()));
        Map<String, CheckEnum> itemMap = itemList.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), Function.identity(), (key1, key2) -> key1));
        for (TaskDetail taskDetail : list) {
            List<String> questionTypeList = Lists.newArrayList();
            for (String itemId : taskDetail.getItemId().split("&")) {
                if (!itemMap.containsKey(itemId)) {
                    continue;
                }
                CheckEnum checkEnum = itemMap.get(itemId);
                taskDetail.setCheckType(checkEnum.getCheckType());
                taskDetail.setCheckProject(checkEnum.getCheckProject());
                if (taskDetail.getItemId().contains("&")) {
                    taskDetail.setCheckContent(checkEnum.getCheckContent());
                    questionTypeList.add(checkEnum.getQuestionType());
                }
            }
            if (!CollectionUtils.isEmpty(questionTypeList)) {
                taskDetail.setQuestionType(String.join("&", questionTypeList));
            }
        }
        CommonBaseEntityUtil.initUserNameByList(list);
        return list;
    }


    private List<ExportTaskDetailDTO> toExportTaskDetailDTOList(List<TaskDetail> taskDetailList) throws IOException {
        List<ExportTaskDetailDTO> result = new ArrayList<>(taskDetailList.size());
        for (int i = 0; i < taskDetailList.size(); i++) {
            TaskDetail taskDetail = taskDetailList.get(i);
            ExportTaskDetailDTO dto = new ExportTaskDetailDTO();
            dto.setIndex(String.valueOf(i));
            dto.setCompany(taskDetail.getCompanyName());
            dto.setJcCompany("安环院");
            dto.setMainQuestion(taskDetail.getQuestionType());
            String checkCodeStr = String.valueOf(taskDetail.getCheckCode());
            String roadSection = taskDetail.getCheckProject().contains("收费站") || taskDetail.getCheckProject().contains("服务区")
                    ? taskDetail.getName()
                    : StringUtils.isEmpty(checkCodeStr) ? "" : "K" + checkCodeStr.replace(".", "+");
            StringBuilder roadBuilder = new StringBuilder()
                    .append(taskDetail.getRoadCode())
                    .append(taskDetail.getRoadName())
                    .append(taskDetail.getDirection() == 1 ? "左幅" : "右幅")
                    .append(roadSection)
                    .append("处");
            if (StringUtils.isNotEmpty(taskDetail.getRemark())) {
                roadBuilder.append("(").append(taskDetail.getRemark()).append(")");
            }
            dto.setQuestionRoad(roadBuilder.toString());
            dto.setAfterImg(getImageBytes(taskDetail.getScenePic1()));
            result.add(dto);
        }
        return result;
    }

    private byte[] getImageBytes(String imgUrl) throws IOException {
        if (StringUtils.isEmpty(imgUrl)) {
            return new byte[0];
        }
        try (InputStream stream = new URL(imgUrl).openStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] data = new byte[4096];
            int bytesRead;
            while ((bytesRead = stream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }
            return buffer.toByteArray();
        }
    }

    /**
     * 导出问题清单
     *
     * @param response
     * @param taskDetail
     */
    @Override
    public void exportNew(HttpServletResponse response, TaskDetail taskDetail) {
        try {
            // 获取数据
            List<TaskDetail> exportList = this.selectScTaskDetailList(taskDetail);
            if (CollectionUtils.isEmpty(exportList)) {
                throw new RuntimeException("没有可导出的数据");
            }
            String fileName = "问题清单-" + taskDetail.getTaskDate() + ".xlsx";
            //设置信息头，告诉浏览器内容为excel类型
            response.setHeader("content-Type", "application/vnd.ms-excel");
            //设置下载名称
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
            //字节流输出
            ServletOutputStream out = response.getOutputStream();
            //设置excel参数
            ExportParams params = new ExportParams();
            //设置sheet名名称
            params.setSheetName("问题清单-" + taskDetail.getTaskDate());
            params.setTitle("问题清单-" + taskDetail.getTaskDate());
            params.setType(ExcelType.XSSF);
            //转成对应的类型;要不然会报错，虽然也可以导出成功
            List<ExportTaskDetailDTO> exportDataList = toExportTaskDetailDTOList(exportList);
            //导入excel
            Workbook workbook = ExcelExportUtil.exportExcel(params, ExportTaskDetailDTO.class, exportDataList);
            //写入
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            log.error("导出Excel异常", e);
            throw new ServiceException("导出Excel失败: " + e.getMessage());
        }
    }


    /**
     * 新增检测列表
     *
     * @param scTaskDetail 检测列表
     * @return 结果
     */
    @Override
    public int insertScTaskDetail(TaskDetail scTaskDetail) {
        scTaskDetail.setCreateTime(DateUtils.getNowDate());
        //根据检测内容计算对应的整改截止时间
        getFixedEndTime(scTaskDetail);
        int num = taskDetailMapper.insertScTaskDetail(scTaskDetail);
        //如果参与评定了的数据,把评定数据删掉,再重新评定
        this.reInitScore(scTaskDetail.getTaskId(), 1);
        this.reInitScore(scTaskDetail.getTaskId(), 2);
        return num;
    }

    /**
     * 修改检测列表
     *
     * @param scTaskDetail 检测列表
     * @return 结果
     */
    @Override
    public int updateScTaskDetail(TaskDetail scTaskDetail) {
        scTaskDetail.setUpdateTime(DateUtils.getNowDate());
        //根据检测内容计算对应的整改截止时间
        getFixedEndTime(scTaskDetail);
        int num = taskDetailMapper.updateScTaskDetail(scTaskDetail);
        //如果参与评定了的数据,把评定数据删掉,再重新评定
        this.reInitScore(scTaskDetail.getTaskId(), 1);
        this.reInitScore(scTaskDetail.getTaskId(), 2);
        return num;
    }

    /**
     * 根据检测内容计算检测项整改截止时间
     *
     * @param scTaskDetail 入参
     */
    private void getFixedEndTime(TaskDetail scTaskDetail) {
        try {
            if (!StringUtils.isEmpty(scTaskDetail.getCheckContent())) {
                CheckEnum checkEnum = new CheckEnum();
                checkEnum.setCheckContent(scTaskDetail.getCheckContent());
                List<CheckEnum> checkEnumList = checkEnumMapper.selectCheckEnumList(checkEnum);
                if (!CollectionUtils.isEmpty(checkEnumList)) {
                    CheckEnum checkEnumRes = checkEnumList.get(0);
                    Integer fixedTime = checkEnumRes.getFixedTime();
                    //整改截止时间为当前时间加上整改期限
                    // 获取当前系统默认时区的日期
                    LocalDate futureDate = LocalDate.now().plusDays(fixedTime);
                    // 转换为当天的00:00时间（系统默认时区）
                    LocalDateTime futureDateTime = futureDate.atTime(23, 59, 59);
                    // 结合时区转为ZonedDateTime，再转化为Date
                    ZonedDateTime zonedDateTime = futureDateTime.atZone(ZoneId.systemDefault());
                    scTaskDetail.setFixedEndDate(Date.from(zonedDateTime.toInstant()));
                }
            }
        } catch (Exception e) {
            log.error("根据检测内容计算检测项整改截止时间错误：{}", e.getMessage());
        }
    }

    /**
     * 批量删除检测列表
     *
     * @param ids 需要删除的检测列表主键
     * @return 结果
     */
    @Override
    public int deleteScTaskDetailByIds(Long[] ids) {
        //获取taskId
        List<TaskDetail> details = taskDetailMapper.selectScTaskDetailByIdList(ids);
        Map<Long, Long> detailIdMap = details.stream().collect(Collectors.toMap(TaskDetail::getId, TaskDetail::getTaskId, (o1, o2) -> o1));
        int num = taskDetailMapper.deleteScTaskDetailByIds(ids);
        //如果参与评定了的数据,把评定数据删掉,再重新评定
        for (Long id : ids) {
            if (detailIdMap.containsKey(id)) {
                this.reInitScore(detailIdMap.get(id), 1);
                this.reInitScore(detailIdMap.get(id), 2);
            }
        }
        return num;
    }


    /**
     * 删除检测列表信息
     *
     * @param id 检测列表主键
     * @return 结果
     */
    @Override
    public int deleteScTaskDetailById(Long id) {
        return taskDetailMapper.deleteScTaskDetailById(id);
    }


    private final static String IDEMPOTENT_ERROR_1 = "Duplicate entry";
    private final static String IDEMPOTENT_ERROR_2 = "sc_task_detail";

    @Override
    @Transactional
    public void saveOrUpdate(TaskDetail taskDetail) throws IOException {
        boolean checkFlag = false;
        if (Objects.nonNull(taskDetail.getCheckCode()) && !StringUtils.isEmpty(taskDetail.getName())) {
            throw new ServiceException("填写桩号后,无需填写名称");
        }
        if (StringUtils.isEmpty(taskDetail.getName()) && Objects.isNull(taskDetail.getCheckCode())) {
            if (Objects.equals("路域环境检查", taskDetail.getCheckType()) &&
                    (Objects.equals("植物种植", taskDetail.getCheckContent())
                            || Objects.equals("绿化管养", taskDetail.getCheckContent()))) {
                //对路域环境检查中的植物种植和绿化管养设置成不需要输入桩号和名称
                checkFlag = true;
            } else {
                throw new ServiceException("桩号和检测名称不能同时为空");
            }
        }
        Task task = taskMapper.selectScTaskById(taskDetail.getTaskId());
        if (Objects.isNull(task)) {
            throw new ServiceException("检测任务不存在");
        }
        taskDetail.setRoadId(task.getRoadId());
        taskDetail.setSourceType(0);
        List<CheckEnum> checkEnumList = Lists.newArrayList();
        if (Objects.equals("公路用地", taskDetail.getCheckType()) || Objects.equals("建筑用地", taskDetail.getCheckType())) {
            if (Objects.equals("建筑用地", taskDetail.getCheckType()) && StringUtils.isEmpty(taskDetail.getCity())) {
                throw new ServiceException("建筑用地的城市不能为空");
            }
            taskDetail.setType(1);
        } else {
            taskDetail.setType(2);
        }
        if (Objects.isNull(taskDetail.getId())
                && !Objects.equals("互通区及收费站", taskDetail.getCheckProject())
                && !Objects.equals("服务区", taskDetail.getCheckProject())
                && !Objects.equals("隧道出入口", taskDetail.getCheckProject())) {
            //新增的时候,如果既不是收费站,又不是服务区,不允许录入重复病害
            if (Objects.isNull(taskDetail.getCheckCode()) && !checkFlag) {
                throw new ServiceException("检测内容不是服务区,隧道出入口或收费站时,桩号不能为空");
            }
            int j = taskDetailMapper.selectByTaskDetail(taskDetail);
            if (j > 0) {
                throw new ServiceException("当前路段检测病害类型和桩号已存在");
            }
        }
        if (Objects.equals(taskDetail.getCheckProject(), "其他")) {
            //检测项目=其他的时候,checkContent和questionType都是用户手动输入的值
            //需要查询
            CheckEnum checkEnum = checkEnumMapper.queryByCheckTypeAndCheckProject(taskDetail.getCheckType(), taskDetail.getCheckProject());
            if (Objects.isNull(checkEnum)) {
                throw new ServiceException("检测项目对应列表不存在,其他");
            }
            taskDetail.setItemId(String.valueOf(checkEnum.getId()));
        } else {
            String[] array = taskDetail.getQuestionType().split("&");
            for (String questionType : array) {
                CheckEnum checkEnum = new CheckEnum();
                checkEnum.setCheckType(taskDetail.getCheckType());
                checkEnum.setCheckProject(taskDetail.getCheckProject());
                checkEnum.setCheckContent(taskDetail.getCheckContent());
                checkEnum.setQuestionType(questionType);
                checkEnumList.add(checkEnum);
            }
            checkEnumList = checkEnumMapper.selectCheckEnumListByList(checkEnumList);
            if (Objects.equals("服务区", taskDetail.getCheckProject())) {
                //如果是服务区,需要把多个问题的分数累加
                taskDetail.setScore(checkEnumList.stream().map(v -> v.getScore()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (CollectionUtils.isEmpty(checkEnumList) || checkEnumList.size() != array.length) {
                throw new ServiceException("检测项目对应列表不存在");
            }
            List<String> itemIdList = checkEnumList.stream().map(v -> String.valueOf(v.getId())).collect(Collectors.toList());
            taskDetail.setItemId(String.join("&", itemIdList));
        }
        //处理图片上传
        System.out.println("开始上传照片,时间:" + DateUtil.now());
        this.dealWithPhoto(taskDetail);
        System.out.println("结束上传照片,时间:" + DateUtil.now());
        Long userId = SecurityUtils.getLoginUser().getUserId();
        if (Objects.equals(taskDetail.getIsRoadCheck(), 0)) {
            taskDetail.setRoadCheckScore(BigDecimal.ZERO);
        }
        boolean flag;
        try {
            if (StringUtils.isEmpty(taskDetail.getName())) {
                taskDetail.setName("");
            }
            if (Objects.isNull(taskDetail.getId())) {
                taskDetail.setCreator(userId);
                this.insertScTaskDetail(taskDetail);
            } else {
                taskDetail.setModifier(userId);
                this.updateScTaskDetail(taskDetail);
            }
        } catch (DuplicateKeyException e) {
            flag = e.getMessage() != null && (e.getMessage().indexOf(IDEMPOTENT_ERROR_1) > 0 && e.getMessage().indexOf(IDEMPOTENT_ERROR_2) > 0);
            if (flag) {
                throw new ServiceException("当前路段检测病害类型和桩号已存在");
            } else {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
        //处理关联的路域环境监测项
        this.dealEnvDetail(taskDetail);
        //如果参与评定了的数据,把评定数据删掉,再重新评定
        this.reInitScore(taskDetail.getTaskId(), 1);
        this.reInitScore(taskDetail.getTaskId(), 2);
    }


    /**
     * 重新生成评定数据
     *
     * @param taskId
     * @param type
     */
    private void reInitScore(Long taskId, Integer type) {
        TaskEvaluate taskEvaluate = taskEvaluateMapper.selectByTaskId(taskId, type);
        if (Objects.nonNull(taskEvaluate)) {
            //删除对应的评定
            taskEvaluateMapper.deleteTaskEvaluateById(taskEvaluate.getId());
            //新增对应的评定
            taskEvaluateService.insertTaskEvaluate(taskEvaluate);
        }
    }


    /**
     * 处理关联的路域环境监测项
     *
     * @param taskDetail
     */
    private void dealEnvDetail(TaskDetail taskDetail) {
        Long originId = taskDetail.getId();
        //查询对应的路域环境ID
        TaskDetail taskDetailTemp = taskDetailMapper.selectScTaskDetailById(originId);
        if (Objects.isNull(taskDetailTemp)) {
            throw new ServiceException("查询检测明细失败");
        }
        Long envId = null;
        if (Objects.equals(taskDetail.getIsRoadCheck(), 0)) {
            if (Objects.nonNull(taskDetailTemp.getEnvDetailId())) {
                //删除对应路域环境的检测明细
                taskDetailMapper.deleteScTaskDetailById(taskDetailTemp.getEnvDetailId());
            }
        } else {
            //查询对应的路域环境是否已经存在
            TaskDetail envTemp = taskDetailMapper.selectScTaskDetailById(taskDetailTemp.getEnvDetailId());
            if (Objects.nonNull(envTemp)) {
                //本身存在对应的路域检测数据,而且不用改变状态
                //更新路域环境监测的分数
                taskDetailMapper.updateRoadCheckScoreByEnvId(taskDetail.getRoadCheckScore(), taskDetailTemp.getEnvDetailId());
                return;
            }
            //查询对应路域检测的项
            String[] array = taskDetail.getItemId().split("&");
            List<CheckEnumRelation> list = checkEnumRelationMapper.selectByRoadItemIdList(Arrays.asList(array));
            if (CollectionUtils.isEmpty(list)) {
                log.error("公路用地对应的路域环境关系不存在");
                return;
            }
            List<Long> itemIds = list.stream().map(CheckEnumRelation::getEnvItemId).collect(Collectors.toList());
            List<String> itemIdList = itemIds.stream().map(String::valueOf).collect(Collectors.toList());
            taskDetail.setItemId(String.join("&", itemIdList));
            //填充检测类别  检测类型  检测内容
            List<CheckEnum> checkEnumList = checkEnumMapper.selectCheckEnumByIdList(itemIds);
            if (CollectionUtils.isEmpty(checkEnumList)) {
                throw new ServiceException("公路检测类别问题项不存在");
            }
            List<String> questionTypeList = checkEnumList.stream().map(CheckEnum::getQuestionType).collect(Collectors.toList());
            taskDetail.setCheckType(checkEnumList.get(0).getCheckType());
            taskDetail.setCheckProject(checkEnumList.get(0).getCheckProject());
            taskDetail.setCheckContent(checkEnumList.get(0).getCheckContent());
            taskDetail.setQuestionType(String.join("&", questionTypeList));
            taskDetail.setScore(taskDetail.getRoadCheckScore());
            taskDetail.setSourceType(1);
            taskDetail.setType(2);
            taskDetailMapper.insertScTaskDetail(taskDetail);
            envId = taskDetail.getId();
        }
        int j = taskDetailMapper.updateEnvId(originId, envId);
    }

    @Override
    @Transactional
    public void deleteById(TaskDetail taskDetail) {
        taskDetail.setIsRoadCheck(0);
        //处理关联的路域环境监测项
        this.dealEnvDetail(taskDetail);
        TaskDetail taskDetailTemp = taskDetailMapper.selectScTaskDetailById(taskDetail.getId());
        if (Objects.isNull(taskDetailTemp)) {
            throw new ServiceException("查询检测明细失败");
        }
        int j = taskDetailMapper.deleteScTaskDetailById(taskDetail.getId());
        //如果参与评定了的数据,把评定数据删掉,再重新评定
        this.reInitScore(taskDetailTemp.getTaskId(), 1);
        this.reInitScore(taskDetailTemp.getTaskId(), 2);
        if (j == 0) {
            throw new ServiceException("删除失败");
        }
    }

    /**
     * 处理图片上传
     *
     * @param taskDetail
     */
    private void dealWithPhoto(TaskDetail taskDetail) throws IOException {
        if (StringUtils.isNotEmpty(taskDetail.getWorkPic()) && taskDetail.getWorkPic().startsWith("data") && taskDetail.getWorkPic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getWorkPic(), "jpg", this.initPicNo(1));
            taskDetail.setWorkPic(workPic);
        }
        if (StringUtils.isNotEmpty(taskDetail.getCenterPic()) && taskDetail.getCenterPic().startsWith("data") && taskDetail.getCenterPic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getCenterPic(), "jpg", this.initPicNo(2));
            taskDetail.setCenterPic(workPic);
        }
        if (StringUtils.isNotEmpty(taskDetail.getSidePic()) && taskDetail.getSidePic().startsWith("data") && taskDetail.getSidePic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getSidePic(), "jpg", this.initPicNo(3));
            taskDetail.setSidePic(workPic);
        }
        if (StringUtils.isNotEmpty(taskDetail.getScenePic1()) && taskDetail.getScenePic1().startsWith("data") && taskDetail.getScenePic1().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getScenePic1(), "jpg", this.initPicNo(4));
            taskDetail.setScenePic1(workPic);
        }
        if (StringUtils.isNotEmpty(taskDetail.getScenePic2()) && taskDetail.getScenePic2().startsWith("data") && taskDetail.getScenePic2().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getScenePic2(), "jpg", this.initPicNo(5));
            taskDetail.setScenePic2(workPic);
        }
        if (StringUtils.isNotEmpty(taskDetail.getScenePic3()) && taskDetail.getScenePic3().startsWith("data") && taskDetail.getScenePic3().startsWith(DATA_IMAGE)) {
            //2024-07-01
            String workPic = AliYunOSSFileUtil.uploadBase64File(taskDetail.getScenePic3(), "jpg", this.initPicNo(6));
            taskDetail.setScenePic3(workPic);
        }
    }

    @Override
    public void dealWithTaskPhoto(Task task) throws IOException {
        if (StringUtils.isNotEmpty(task.getWorkPic()) && task.getWorkPic().startsWith("data") && task.getWorkPic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(task.getWorkPic(), "jpg", this.initPicNo(1));
            task.setWorkPic(workPic);
        }
        if (StringUtils.isNotEmpty(task.getCenterPic()) && task.getCenterPic().startsWith("data") && task.getCenterPic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(task.getCenterPic(), "jpg", this.initPicNo(2));
            task.setCenterPic(workPic);
        }
        if (StringUtils.isNotEmpty(task.getSidePic()) && task.getSidePic().startsWith("data") && task.getSidePic().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(task.getSidePic(), "jpg", this.initPicNo(3));
            task.setSidePic(workPic);
        }
    }


    /**
     * 图片名称
     *
     * @param num
     * @return
     */
    private String initPicNo(int num) {
        return DateUtil.now().substring(5, 19).replaceAll("-", "").replaceAll(":", "").replaceAll(" ", "") + "0" + num;
    }


    @DataScope(deptAlias = "de")
    @Override
    public TaskDetailCountRes selectScTaskDetailNumWithCompany(TaskGroupQueryDTO queryDTO) {
        return combineScTaskForCount(queryDTO, Boolean.TRUE);
    }

    /**
     * 老方法
     */
    @Deprecated
    private TaskDetailCountRes combineScTaskForCount(TaskGroupQueryDTO queryDTO, Boolean isPage) {
        TaskDetailCountRes res = new TaskDetailCountRes();
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        log.info("当前用户角色：{}", roleList);
        queryDTO.setIsSpecial(Boolean.FALSE);
        // 获取任务基本信息
        List<Task> tasks = taskMapper.selectScTaskForCount(queryDTO);
        if (CollectionUtils.isEmpty(tasks)) {
            return res;
        }
        log.info("任务基本信息数量：{}", tasks.size());
        List<Task> substringTasks = tasks.stream().peek(task ->
                task.setTaskDate(task.getTaskDate().substring(0, 7))).distinct().collect(Collectors.toList());
        Map<String, List<Task>> taskMap = substringTasks.stream()
                .collect(Collectors.groupingBy(vo -> String.join("_",
                        vo.getTaskDate(),
                        vo.getCompanyName(),
                        vo.getRoadId().toString())));
        List<Long> taskIds = substringTasks.stream().map(Task::getId).collect(Collectors.toList());
        log.info("监测任务id：{}", taskIds);
        //检测明细
        List<TaskDetail> taskDetailList = new ArrayList<>();
        //特殊处理
        if (DateUtils.isBefore(queryDTO.getTaskDate(), "2025-06") || roleList.contains("JCDW")) {
            log.info("特殊查询，不过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIds(taskIds));
        } else {
            log.info("非特殊查询，过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIdsWithStatus(taskIds));
        }
        Map<Long, List<TaskDetail>> taskDetailMap = taskDetailList.stream().collect(Collectors.groupingBy(TaskDetail::getTaskId));
        //获取评分
        List<TaskEvaluate> taskEvaluateList = taskEvaluateService.selectByTaskIdList(taskIds);
        Map<Long, BigDecimal> taskEvaluateMap = taskEvaluateList.stream().collect(Collectors.toMap(TaskEvaluate::getTaskId, TaskEvaluate::getScore, (o1, o2) -> o1));
        //组装数据
        List<TaskDetailCountVO> voList = new ArrayList<>();
        taskMap.forEach((key, value) -> {
            List<Task> taskList = taskMap.get(key);
            TaskDetailCountVO vo = new TaskDetailCountVO();
            Task task = taskList.get(0);
            vo.setTaskDate(task.getTaskDate());
            vo.setCompanyName(task.getCompanyName());
            vo.setRoadName(task.getRoadName());
            vo.setRoadCode(task.getRoadCode());
            vo.setStartCode(task.getStartCode());
            vo.setEndCode(task.getEndCode());
            vo.setScore(taskEvaluateMap.get(task.getId()));
            List<TaskDetail> allDetailList = new ArrayList<>();
            vo.setId(null);
            List<Long> taskIdList = new ArrayList<>();
            for (Task t : taskList) {
                Long taskId = t.getId();
                if (taskDetailMap.containsKey(taskId)) {
                    taskIdList.add(taskId);
                    allDetailList.addAll(taskDetailMap.get(taskId));
                }
            }
            vo.setIdList(taskIdList);
            if (!CollectionUtils.isEmpty(allDetailList)) {
                int totalCount = allDetailList.size();
                vo.setTotalCount(totalCount);
                long fixedCount = allDetailList.stream()
                        .filter(detail -> Objects.equals(1, detail.getIsFixed())).count();
                vo.setFixedCount((int) fixedCount);
                vo.setPendingCount(vo.getTotalCount() - vo.getFixedCount());
                double fixedRate;
                double rate = (fixedCount * 100.0) / totalCount;
                fixedRate = BigDecimal.valueOf(rate)
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue();
                vo.setFixedRate(fixedRate);
                voList.add(vo);
            }
        });
        res.setTotal(voList.size());
        //手动分页
        if (isPage) {
            PageDomain jsonPageDomain = TableSupport.buildPageRequest();
            Integer pageNum = jsonPageDomain.getPageNum();
            Integer pageSize = jsonPageDomain.getPageSize();
            if (pageNum == null || pageSize == null) {
                throw new RuntimeException("未获取到分页参数值");
            }
            int fromIndex = pageSize * (pageNum - 1);
            int toIndex = Math.min(pageSize * pageNum, voList.size());
            List<TaskDetailCountVO> subList = voList.subList(fromIndex, toIndex);
            res.setResult(subList);
        } else {
            res.setResult(voList);
        }
        return res;
    }

    @DataScope(deptAlias = "de")
    @Override
    public TaskDetailCountRes exportDetailGroup(TaskGroupQueryDTO queryDTO) {
        return combineScTaskForCount(queryDTO, Boolean.FALSE);
    }

    @Override
    public List<NoticeTaskDetailDTO> selectNoticeTaskDetail() {
        return taskDetailMapper.selectNoticeTaskDetail();
    }


    @Override
    public boolean oneClickConfirm(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return false;
        }
        //根据taskId获取所有已经整改的数据
        List<TaskDetail> fixedDetailList = taskDetailMapper.selectFixedDetailByTaskIds(taskIdList);
        if (CollectionUtils.isEmpty(fixedDetailList)) {
            throw new ServiceException("当前路线没有已整改待确认数据");
        }
        //已经整改但是没有确认的数据，直接操作确认
        List<Long> fixedIdList = fixedDetailList.stream().map(TaskDetail::getFixedId).collect(Collectors.toList());
        List<TaskDetailFixed> fixedList = taskDetailFixedMapper.selectByIdList(fixedIdList);
        if (!CollectionUtils.isEmpty(fixedList)) {
            List<TaskDetailFixed> confirmList = fixedList.stream().peek(dto -> {
                dto.setIsConfirm(1);
                dto.setConfirmPinion("一键确认");
                dto.setConfirmPeople("系统");
                dto.setConfirmTime(new Date());
            }).collect(Collectors.toList());
            taskDetailFixedMapper.batchUpdateTaskDetailFixed(confirmList);
        }
        return true;
    }

    private TaskDetailCountRes selectByCompany(TaskGroupQueryDTO queryDTO) {
        //根据入参查询  月份  管养单位
        TaskDetailCountRes res = new TaskDetailCountRes();
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        queryDTO.setIsSpecial(Boolean.FALSE);
        List<Task> tasks = taskMapper.selectTaskByCompany(queryDTO);
        if (CollectionUtils.isEmpty(tasks)) {
            res.setResult(new ArrayList<>());
            return res;
        }
        List<Task> substringTasks = tasks.stream().peek(task ->
                task.setTaskDate(task.getTaskDate().substring(0, 7))).distinct().collect(Collectors.toList());
        //根据公司分组
        Map<String, List<Task>> taskMap = substringTasks.stream().collect(Collectors.groupingBy(Task::getCompanyName));
        List<Long> taskIds = substringTasks.stream().map(Task::getId).collect(Collectors.toList());
        //检测明细
        List<TaskDetail> taskDetailList = new ArrayList<>();
        //特殊处理
        if (DateUtils.isBefore(queryDTO.getTaskDate(), "2025-06") || roleList.contains("JCDW")) {
            log.info("新特殊查询，不过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIds(taskIds));
        } else {
            log.info("新非特殊查询，过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIdsWithStatus(taskIds));
        }
        Map<Long, List<TaskDetail>> taskDetailMap = taskDetailList.stream().collect(Collectors.groupingBy(TaskDetail::getTaskId));
        //获取评分
        List<TaskEvaluate> taskEvaluateList = taskEvaluateService.selectByTaskIdList(taskIds);
        Map<Long, BigDecimal> taskEvaluateMap = taskEvaluateList.stream().collect(Collectors.toMap(TaskEvaluate::getTaskId, TaskEvaluate::getScore, (o1, o2) -> o1));
        //组装数据
        List<TaskDetailCountVO> voList = new ArrayList<>();
        taskMap.forEach((key, value) -> {
            TaskDetailCountVO vo = new TaskDetailCountVO();
            vo.setCompanyName(key);
            vo.setTaskDate(queryDTO.getTaskDate());
            //当前公司的监测任务
            List<Task> taskList = taskMap.get(key);
            List<Long> resIdList = new ArrayList<>();
            List<TaskDetail> allDetailList = new ArrayList<>();
            BigDecimal haveScoreCount = BigDecimal.ZERO;
            BigDecimal haveScore = BigDecimal.ZERO;
            for (Task task : taskList) {
                Long taskId = task.getId();
                //当前id有没有明细
                if (taskDetailMap.containsKey(taskId)) {
                    resIdList.add(taskId);
                    allDetailList.addAll(taskDetailMap.get(taskId));
                }
                //当前id有没有分数
                if (taskEvaluateMap.containsKey(taskId)) {
                    BigDecimal evaluateScore = taskEvaluateMap.get(taskId);
                    haveScore = haveScore.add(BigDecimal.ONE);
                    haveScoreCount = haveScoreCount.add(evaluateScore);
                }
            }
            vo.setIdList(resIdList);
            //计算平均分
            if (haveScore.compareTo(BigDecimal.ZERO) > 0) {
                vo.setScore(haveScoreCount.divide(haveScore, 2, RoundingMode.HALF_UP));
            }
            //计算已经整改和未整改
            if (!CollectionUtils.isEmpty(allDetailList)) {
                int totalCount = allDetailList.size();
                vo.setTotalCount(totalCount);
                long fixedCount = allDetailList.stream()
                        .filter(detail -> Objects.equals(1, detail.getIsFixed())).count();
                vo.setFixedCount((int) fixedCount);
                vo.setPendingCount(vo.getTotalCount() - vo.getFixedCount());
                double fixedRate;
                double rate = (fixedCount * 100.0) / totalCount;
                fixedRate = BigDecimal.valueOf(rate)
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue();
                vo.setFixedRate(fixedRate);
                voList.add(vo);
            } else if (vo.getScore() != null) {
                vo.setTotalCount(0);
                vo.setFixedCount(0);
                vo.setPendingCount(0);
                vo.setFixedRate(0.00);
                voList.add(vo);
            }
        });
        res.setResult(voList);
        res.setTotal(voList.size());
        return res;
    }

    @DataScope(deptAlias = "de")
    @Override
    public TaskDetailCountRes listByCompany(TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = this.selectByCompany(queryDTO);
        PageDomain jsonPageDomain = TableSupport.buildPageRequest();
        Integer pageNum = jsonPageDomain.getPageNum();
        Integer pageSize = jsonPageDomain.getPageSize();
        int fromIndex = pageSize * (pageNum - 1);
        int toIndex = Math.min(pageSize * pageNum, res.getResult().size());
        List<TaskDetailCountVO> subList = res.getResult().subList(fromIndex, toIndex);
        res.setResult(subList);
        return res;
    }

    @Override
    public TaskDetailCountRes exportByCompany(TaskGroupQueryDTO queryDTO) {
        return this.selectByCompany(queryDTO);
    }

    @DataScope(deptAlias = "de")
    @Override
    public TaskDetailCountRes listByRoad(TaskGroupQueryDTO queryDTO) {
        //根据入参查询
        TaskDetailCountRes res = new TaskDetailCountRes();
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        queryDTO.setIsSpecial(Boolean.FALSE);
        List<Task> tasks = taskMapper.selectTaskByRoad(queryDTO);
        if (CollectionUtils.isEmpty(tasks)) {
            return res;
        }
        List<Task> substringTasks = tasks.stream().peek(task ->
                task.setTaskDate(task.getTaskDate().substring(0, 7))).distinct().collect(Collectors.toList());
        //根据路段分组  兼容之前的数据，一条路会有多条检测任务
        Map<Long, List<Task>> taskMap = substringTasks.stream().collect(Collectors.groupingBy(Task::getRoadId));
        List<Long> taskIds = substringTasks.stream().map(Task::getId).collect(Collectors.toList());
        //检测明细
        List<TaskDetail> taskDetailList = new ArrayList<>();
        //特殊处理
        if (DateUtils.isBefore(queryDTO.getTaskDate(), "2025-06") || roleList.contains("JCDW")) {
            log.info("新根据路段特殊查询，不过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIds(taskIds));
        } else {
            log.info("新根据路段非特殊查询，过滤已确认");
            taskDetailList.addAll(taskDetailMapper.selectScTaskDetailByTaskIdsWithStatus(taskIds));
        }
        Map<Long, List<TaskDetail>> taskDetailMap = taskDetailList.stream().collect(Collectors.groupingBy(TaskDetail::getTaskId));
        //获取评分
        List<TaskEvaluate> taskEvaluateList = taskEvaluateService.selectByTaskIdList(taskIds);
        Map<Long, BigDecimal> taskEvaluateMap = taskEvaluateList.stream().collect(Collectors.toMap(TaskEvaluate::getTaskId, TaskEvaluate::getScore, (o1, o2) -> o1));
        //组装数据
        List<TaskDetailCountVO> voList = new ArrayList<>();
        taskMap.forEach((key, value) -> {
            TaskDetailCountVO vo = new TaskDetailCountVO();
            //set返回值
            Task t = taskMap.get(key).get(0);
            vo.setRoadCode(t.getRoadCode());
            vo.setRoadName(t.getRoadName());
            vo.setStartCode(t.getStartCode());
            vo.setEndCode(t.getEndCode());
            vo.setTaskDate(queryDTO.getTaskDate());
            //当前路段的监测任务
            List<Task> taskList = taskMap.get(key);
            List<Long> resIdList = new ArrayList<>();
            List<TaskDetail> allDetailList = new ArrayList<>();
            BigDecimal haveScoreCount = BigDecimal.ZERO;
            BigDecimal haveScore = BigDecimal.ZERO;
            for (Task task : taskList) {
                Long taskId = task.getId();
                //当前id有没有明细
                if (taskDetailMap.containsKey(taskId)) {
                    resIdList.add(taskId);
                    allDetailList.addAll(taskDetailMap.get(taskId));
                }
                //当前id有没有分数
                if (taskEvaluateMap.containsKey(taskId)) {
                    BigDecimal evaluateScore = taskEvaluateMap.get(taskId);
                    haveScore = haveScore.add(BigDecimal.ONE);
                    haveScoreCount = haveScoreCount.add(evaluateScore);
                }
            }
            vo.setIdList(resIdList);
            //计算平均分
            if (haveScore.compareTo(BigDecimal.ZERO) > 0) {
                vo.setScore(haveScoreCount.divide(haveScore, 2, RoundingMode.HALF_UP));
            }
            //计算已经整改和未整改
            if (!CollectionUtils.isEmpty(allDetailList)) {
                int totalCount = allDetailList.size();
                vo.setTotalCount(totalCount);
                long fixedCount = allDetailList.stream()
                        .filter(detail -> Objects.equals(1, detail.getIsFixed())).count();
                vo.setFixedCount((int) fixedCount);
                vo.setPendingCount(vo.getTotalCount() - vo.getFixedCount());
                double fixedRate;
                double rate = (fixedCount * 100.0) / totalCount;
                fixedRate = BigDecimal.valueOf(rate)
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue();
                vo.setFixedRate(fixedRate);
                voList.add(vo);
            }
        });
        res.setTotal(voList.size());
        PageDomain jsonPageDomain = TableSupport.buildPageRequest();
        Integer pageNum = jsonPageDomain.getPageNum();
        Integer pageSize = jsonPageDomain.getPageSize();
        int fromIndex = pageSize * (pageNum - 1);
        int toIndex = Math.min(pageSize * pageNum, voList.size());
        List<TaskDetailCountVO> subList = voList.subList(fromIndex, toIndex);
        res.setResult(subList);
        return res;
    }

    @Override
    public List<TaskDetailQueryDTO> selectScTaskDetailListByTaskId(Long id) {
        return taskDetailMapper.selectScTaskDetailListByTaskId(id);
    }

    @Override
    public Integer fixedTask(Long taskId) {
        //1.根绝taskId查询是否有未整改的数据
        Integer count = taskDetailMapper.selectTaskDetailIsAllFixed(taskId);
        if (count > 0) {
            throw new ServiceException("当前路段还存在未整改数据");
        }
        //2.如果有抛出异常，否则修改task表的整改状态为已整改
        Task task = new Task();
        task.setId(taskId);
        taskMapper.updateScTask(task);
        return 1;
    }
}
