package com.tunnel.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rits.cloning.Cloner;
import com.tunnel.WebSocketServer;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.sms.AliyunSmsUtils;
import com.tunnel.domain.*;
import com.tunnel.domain.dto.ScRoadQueryDTO;
import com.tunnel.domain.stat.RankStat;
import com.tunnel.domain.template.EvaluateRoadSheetOne;
import com.tunnel.domain.template.EvaluateRoadSheetThree;
import com.tunnel.mapper.*;
import com.tunnel.service.TaskEvaluateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 检测任务评定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Service
@Slf4j
public class TaskEvaluateServiceImpl implements TaskEvaluateService {
    @Autowired
    private TaskEvaluateMapper taskEvaluateMapper;
    @Autowired
    private TaskDetailMapper taskDetailMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private CheckEnumMapper checkEnumMapper;
    @Autowired
    private CheckEnumScoreMapper checkEnumScoreMapper;
    @Resource
    private TaskEvaluateService taskEvaluateService;

    @Resource
    private AliyunSmsUtils aliyunSmsUtils;

    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private RoadMapper roadMapper;

    /**
     * 查询检测任务评定
     *
     * @param id 检测任务评定主键
     * @return 检测任务评定
     */
    @Override
    public TaskEvaluate selectTaskEvaluateById(Long id) {
        return taskEvaluateMapper.selectTaskEvaluateById(id);
    }

    /**
     * 查询检测任务评定列表
     *
     * @param taskEvaluate 检测任务评定
     * @return 检测任务评定
     */
    @Override
    public List<TaskEvaluate> selectTaskEvaluateList(TaskEvaluate taskEvaluate) {
        this.commonCheck(taskEvaluate);
        return taskEvaluateMapper.selectTaskEvaluateList(taskEvaluate);
    }


    private void commonCheck(TaskEvaluate taskEvaluate){
        if(!CollectionUtils.isEmpty(taskEvaluate.getTaskDateList()) && taskEvaluate.getTaskDateList().size()>1){
            throw new ServiceException("只能选择一个月份导出");
        }
        if(!CollectionUtils.isEmpty(taskEvaluate.getTaskDateList()) && StringUtils.isEmpty(taskEvaluate.getTaskDate())){
            taskEvaluate.setTaskDate(taskEvaluate.getTaskDateList().get(0));
        }
        if (StringUtils.isEmpty(taskEvaluate.getTaskDate())) {
            throw new ServiceException("月份不能为空");
        }
        if (Objects.isNull(taskEvaluate.getType())) {
            throw new ServiceException("任务类型不能为空");
        }
    }

    /**
     * 新增检测任务评定
     *
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    @Override
    @Transactional
    public synchronized int insertTaskEvaluate(TaskEvaluate taskEvaluate) {
        TaskEvaluate taskEvaluateTemp = taskEvaluateMapper.selectByTaskId(taskEvaluate.getTaskId(), taskEvaluate.getType());
        if (Objects.nonNull(taskEvaluateTemp)) {
            throw new ServiceException("当前任务评估检测已存在");
        }
        Task task = taskMapper.selectScTaskById(taskEvaluate.getTaskId());
        if (Objects.isNull(task)) {
            throw new ServiceException("检测任务不存在");
        }
        if (!Objects.equals(1, task.getStatus())) {
            throw new ServiceException("任务未检测完成");
        }
        taskEvaluate.setRoadId(task.getRoadId());
        //评估分数
        this.initScore(taskEvaluate);
        taskEvaluate.setStatus(1);
        return taskEvaluateMapper.insertTaskEvaluate(taskEvaluate);
    }
    private List<Map<String, CheckEnum>> initScore(TaskEvaluate taskEvaluate) {
        return this.initScore(taskEvaluate, null, null, null);
    }

    private List<Map<String, CheckEnum>> initScore(TaskEvaluate taskEvaluate, Integer direction, String startCheckCode, String endCheckCode) {
        List<Map<String, CheckEnum>> resultList = Lists.newArrayList();
        //根据不同字段查询各项总分值
        List<CheckEnum> allList=checkEnumScoreMapper.selectAllList();
        Map<String,CheckEnum> resultRoadMap=Maps.newHashMap();
        Map<String,CheckEnum> resultEnvMap=Maps.newHashMap();
        Map<String,CheckEnum> resultBuildingMap=Maps.newHashMap();
        //包装总分的结果,各项检测维度的总分
        this.initResultCheckEnumMap(resultRoadMap,resultEnvMap,resultBuildingMap,allList);
        resultList.add(resultRoadMap);
        resultList.add(resultBuildingMap);
        resultList.add(resultEnvMap);

        //查询检测数据
        List<TaskDetail> detailList = taskDetailMapper.selectByTaskId(taskEvaluate.getTaskId(), taskEvaluate.getType()
                ,taskEvaluate.getCity(), direction, startCheckCode, endCheckCode,null);
        detailList=detailList.stream().filter(v->!Objects.equals("无问题",v.getCheckContent())).collect(Collectors.toList());

        Map<String,CountAndScore> roadMap=Maps.newHashMap();
        Map<String,CountAndScore> envMap=Maps.newHashMap();
        Map<String,CountAndScore> buildingMap=Maps.newHashMap();
        //检测数据的扣分值
        this.initCheckEnumMap(roadMap,envMap,buildingMap,detailList);
        taskEvaluate.setScore(new BigDecimal("100"));
        taskEvaluate.setCount(0);
        taskEvaluate.setBuildingScore(new BigDecimal("100"));
        taskEvaluate.setBuildingCount(0);
        //检测数据的总分
        this.initMatchMap(resultRoadMap,roadMap,taskEvaluate);
        this.initMatchMap(resultEnvMap,envMap,taskEvaluate);
        this.initMatchMap(resultBuildingMap,buildingMap,taskEvaluate);
        return resultList;
    }


    /**
     * 计算线路对应项的扣分值和线路的总得分
     * @param resultMap
     * @param map
     * @param taskEvaluate
     */
    private void initMatchMap(Map<String,CheckEnum> resultMap, Map<String,CountAndScore> map,TaskEvaluate taskEvaluate){
        for (Map.Entry<String, CheckEnum> entry : resultMap.entrySet()) {
            if(map.containsKey(entry.getKey())){
                entry.getValue().setScore(map.get(entry.getKey()).getScore());
                entry.getValue().setCount(map.get(entry.getKey()).getCount());
                //当前维度的总扣分值,无限定的分数
                entry.getValue().setUnLimitScore(map.get(entry.getKey()).getScore());
                boolean calRoadScore=Objects.equals(taskEvaluate.getType(),1) && (entry.getKey().contains("建筑用地") || entry.getKey().contains("公路用地"));
                boolean calEnvScore=Objects.equals(taskEvaluate.getType(),2) && (entry.getKey().contains("路域环境检查"));
                boolean isBuildingRoad=entry.getKey().contains("建筑用地");
                if(calRoadScore || calEnvScore){
                    if(entry.getValue().getScore().compareTo(entry.getValue().getLimitScore())<0){
                        if(isBuildingRoad){
                            //建筑用地扣分
                            taskEvaluate.setBuildingScore(taskEvaluate.getBuildingScore().subtract(entry.getValue().getScore()));
                            taskEvaluate.setBuildingCount(taskEvaluate.getBuildingCount()+entry.getValue().getCount());
                        }else{
                            //非建筑用地扣分
                            taskEvaluate.setScore(taskEvaluate.getScore().subtract(entry.getValue().getScore()));
                            taskEvaluate.setCount(taskEvaluate.getCount()+entry.getValue().getCount());
                        }
                    }else{
                        if(isBuildingRoad){
                            taskEvaluate.setBuildingScore(taskEvaluate.getBuildingScore().subtract(entry.getValue().getLimitScore()));
                            taskEvaluate.setBuildingCount(taskEvaluate.getBuildingCount()+entry.getValue().getCount());
                        }else{
                            taskEvaluate.setScore(taskEvaluate.getScore().subtract(entry.getValue().getLimitScore()));
                            taskEvaluate.setCount(taskEvaluate.getCount()+entry.getValue().getCount());
                        }

                    }
                }
            }
        }
    }


    private void initCheckEnumMap(Map<String,CountAndScore> roadMap, Map<String,CountAndScore> envMap,Map<String,CountAndScore> buildingMap
            ,List<TaskDetail> allList){
        for (TaskDetail detail : allList) {
            Integer count = detail.getQuestionType().split("&").length;
            if (Objects.equals(detail.getCheckType(), "公路用地")) {
                String key = detail.getCheckType() + "/" + detail.getCheckProject();
                if (!roadMap.containsKey(key)) {
                    roadMap.put(key, new CountAndScore(detail.getScore(),count));
                } else {
                    CountAndScore countAndScore=roadMap.get(key);
                    countAndScore.setScore(detail.getScore().add(roadMap.get(key).getScore()));
                    countAndScore.setCount(countAndScore.getCount()+count);
                }
            } else if (Objects.equals(detail.getCheckType(), "路域环境检查")) {
                String key = detail.getCheckType() + "/" + detail.getCheckProject();
                if (!envMap.containsKey(key)) {
                    envMap.put(key, new CountAndScore(detail.getScore(),count));
                } else {
                    CountAndScore countAndScore=envMap.get(key);
                    countAndScore.setScore(detail.getScore().add(envMap.get(key).getScore()));
                    countAndScore.setCount(countAndScore.getCount()+count);
                }
            } else {
                //建筑用地
                String key = detail.getCheckType() + "/" + detail.getCheckProject() + "/" + detail.getCheckContent();
                if (!buildingMap.containsKey(key)) {
                    buildingMap.put(key,new CountAndScore(detail.getScore(),count));
                } else {
                    CountAndScore countAndScore=buildingMap.get(key);
                    countAndScore.setScore(detail.getScore().add(buildingMap.get(key).getScore()));
                    countAndScore.setCount(countAndScore.getCount()+count);
                }
            }
        }
    }

    private void initResultCheckEnumMap(Map<String,CheckEnum> resultRoadMap, Map<String,CheckEnum> resultEnvMap,Map<String,CheckEnum> resultBuildingMap
            ,List<CheckEnum> allList){
        for (CheckEnum checkEnum : allList) {
            checkEnum.setLimitScore(checkEnum.getScore());
            checkEnum.setScore(BigDecimal.ZERO);
        }
        for (CheckEnum checkEnum : allList) {
            if (Objects.equals(checkEnum.getCheckType(), "公路用地")) {
                String key = checkEnum.getCheckType() + "/" + checkEnum.getCheckProject();
                if (!resultRoadMap.containsKey(key)) {
                    resultRoadMap.put(key, checkEnum);
                } else {
                    CheckEnum check = resultRoadMap.get(key);
                    check.setLimitScore(check.getLimitScore().add(checkEnum.getLimitScore()));
                }
            } else if (Objects.equals(checkEnum.getCheckType(), "路域环境检查")) {
                String key = checkEnum.getCheckType() + "/" + checkEnum.getCheckProject();
                if (!resultEnvMap.containsKey(key)) {
                    resultEnvMap.put(key, checkEnum);
                } else {
                    CheckEnum check = resultEnvMap.get(key);
                    check.setLimitScore(check.getLimitScore().add(checkEnum.getLimitScore()));
                }
            } else {
                //建筑用地
                String key = checkEnum.getCheckType() + "/" + checkEnum.getCheckProject() + "/" + checkEnum.getCheckContent();
                if (!resultBuildingMap.containsKey(key)) {
                    resultBuildingMap.put(key, checkEnum);
                } else {
                    CheckEnum check = resultBuildingMap.get(key);
                    check.setLimitScore(check.getLimitScore().add(checkEnum.getLimitScore()));
                }
            }
        }
    }

    /**
     * 修改检测任务评定
     *
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTaskEvaluate(TaskEvaluate taskEvaluate) {
        taskEvaluate.setUpdateTime(DateUtils.getNowDate());
        //评估分数如果为空,系统自动计算
        if (Objects.isNull(taskEvaluate.getScore())) {
            this.initScore(taskEvaluate);
        }
        sendMsg(taskEvaluate);
        return taskEvaluateMapper.updateTaskEvaluate(taskEvaluate);
    }

    private void sendMsg(TaskEvaluate taskEvaluate) {
        List<TaskDetail> needSendList = taskDetailMapper.selectScTaskDetailWithTaskByTaskIds(Collections.singletonList(taskEvaluate.getTaskId()));
        List<TaskDetail> filterList = needSendList.stream().filter(dto -> dto.getIsFixed() == 0 && dto.getFixedEndDate() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return;
        }
        List<ScRoadQueryDTO> queryDTOS = filterList.stream().map(item -> {
            ScRoadQueryDTO queryDTO = new ScRoadQueryDTO();
            queryDTO.setCompanyName(item.getCompanyName());
            queryDTO.setRoadName(item.getRoadName());
            return queryDTO;
        }).distinct().collect(Collectors.toList());
        List<Road> roads = roadMapper.queryNoticeUser(queryDTOS);
        if (CollectionUtils.isEmpty(roads)) {
            return;
        }
        Map<String, Road> roadMap = roads.stream()
                .collect(Collectors.toMap(road -> StringUtils.format("{}-{}",road.getCompanyName(), road.getRoadName()), Function.identity(), (o1, o2) -> o1));
        //同一整改时间发一次
        Set<String> processedKeys = new LinkedHashSet<>();
        for (TaskDetail taskDetail : filterList) {
            String compositeKey = generateCompositeKey(taskDetail.getCompanyName(), taskDetail.getRoadName(), taskDetail.getFixedEndDate());
            // 去重校验
            if (!processedKeys.add(compositeKey)) {
                continue; // 已处理则跳过
            }
            String lookupKey = StringUtils.format("{}-{}",
                    taskDetail.getCompanyName(),
                    taskDetail.getRoadName()
            );
            if (roadMap.containsKey(lookupKey)) {
                Road road = roadMap.get(lookupKey);
                if (road.getNotifyUser() != null && road.getNotifyPhone() != null) {
                    String templateParam = String.format("{\"name\":\"%s\",\"roadName\":\"%s\",\"endDate\":\"%s\"}",
                            road.getNotifyUser(), taskDetail.getRoadName(), cn.hutool.core.date.DateUtil.format(taskDetail.getFixedEndDate(), "yyyy-MM-dd"));
                    aliyunSmsUtils.sendMessage("武汉精视遥测科技", "SMS_483485203",
                            road.getNotifyPhone(), templateParam);
                }
            }
        }

    }

    // 复合键生成方法（封装日期格式化逻辑）
    private String generateCompositeKey(String company, String road, Date fixedDate) {
        return String.format("%s::%s::%s",
                company,
                road,
                cn.hutool.core.date.DateUtil.format(fixedDate, "yyyy-MM-dd")
        );
    }

    /**
     * 批量删除检测任务评定
     *
     * @param ids 需要删除的检测任务评定主键
     * @return 结果
     */
    @Override
    public int deleteTaskEvaluateByIds(Long[] ids) {
        return taskEvaluateMapper.deleteTaskEvaluateByIds(ids);
    }

    /**
     * 删除检测任务评定信息
     *
     * @param id 检测任务评定主键
     * @return 结果
     */
    @Override
    public int deleteTaskEvaluateById(Long id) {
        return taskEvaluateMapper.deleteTaskEvaluateById(id);
    }

    @Override
    public List<List<CheckEnum>> getScoreInfo(Long id) {
        TaskEvaluate taskEvaluate = taskEvaluateMapper.selectTaskEvaluateById(id);
        //评估分数
        List<Map<String, CheckEnum>> tempList = this.initScore(taskEvaluate);
        List<List<CheckEnum>> resultList = Lists.newArrayList();
        for (Map<String, CheckEnum> map : tempList) {
            List<CheckEnum> list = Lists.newArrayList();
            for (Map.Entry<String, CheckEnum> entry : map.entrySet()) {
                CheckEnum checkEnum = new CheckEnum();
                checkEnum.setCheckType(entry.getKey());
                checkEnum.setScore(entry.getValue().getScore());
                checkEnum.setLimitScore(entry.getValue().getLimitScore());
                list.add(checkEnum);
            }
            resultList.add(list);
        }
        return resultList;
    }

    /**
     * 专项检测excel汇总导出
     *
     * @param response
     * @param taskEvaluate
     */
    @Override
    public File exportTaskEvaluate(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException {
        File file = null;
        //获取模板
        XSSFWorkbook workbook = this.initExcelTemplate(response, "roadEvaluate");
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectAllTasksByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            throw new ServiceException("当前任务下没有数据");
        }
        //查询当前任务路线对应检测出来的服务区
        List<EvaluateRoadSheetOne> baseInfoList = this.listBaseInfoByTaskIdList(listOne,taskEvaluate.getType());
        //'服务区','隧道出入口','互通区及收费站'
        Map<String, String> serviceAreaMap = baseInfoList.stream().filter(v -> Objects.equals("服务区", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> tunnelMap = baseInfoList.stream().filter(v -> Objects.equals("隧道出入口", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> feeAreaMap = baseInfoList.stream().filter(v -> Objects.equals("互通区及收费站", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            //处理得分汇总数据
            this.dealWithSheetOne(sheetOne, serviceAreaMap, tunnelMap, feeAreaMap, taskEvaluate, true);

            // 在处理完成后，如果分数仍为0或null，说明没有检测数据，设置为满分
            if (sheetOne.getRoadTotalScore() == null || sheetOne.getRoadTotalScore().equals(BigDecimal.ZERO)) {
                sheetOne.setRoadTotalScore(new BigDecimal("100"));
                sheetOne.setRoadTotalCount(0);
                sheetOne.setBuildingTotalScore(new BigDecimal("100"));
                sheetOne.setBuildingTotalCount(0);
                sheetOne.setRoadEnvCount(0);
            }
        }
        //路段得分 汇总及排名
        listOne=listOne.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getRoadTotalScore).reversed()).collect(Collectors.toList());
        this.initExcelSheetOne(listOne, workbook, workbook.getSheetAt(0), 21, 2);
        //路段得分排名--使用公路用地总分排序 desc
//        log.info("路段得分排名"+ cn.hutool.core.date.DateUtil.now());
//        listOne=listOne.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getRoadTotalScore).reversed()).collect(Collectors.toList());
//        this.initExcelSheetTwo(listOne, workbook, workbook.getSheetAt(1), 21, 1,false);

        List<EvaluateRoadSheetThree> listThreeCount = this.dealWithSheetThreeCount(listOne,false);
        //管养单位得分及排名
        this.initExcelSheetThreeCountQuan(listThreeCount, workbook, workbook.getSheetAt(1), 6, 1);
        //路段问题数量 汇总及排名
        listOne=listOne.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getRoadTotalScore).reversed()).collect(Collectors.toList());
        this.initExcelSheetTwo(listOne, workbook, workbook.getSheetAt(2), 21, 1,true);

        //管养单位问题数量及排名
        this.initExcelSheetCompanyCountRank(listThreeCount, workbook, workbook.getSheetAt(3), 5, 1);

        //管养单位问题数量、分数、排名
        this.initExcelSheetCompanyScoreRank(listThreeCount, workbook, workbook.getSheetAt(4), 6, 1);

//        //建筑控制区得分--建筑用地总分排序 desc
//        log.info("建筑控制区得分"+ cn.hutool.core.date.DateUtil.now());
//        List<EvaluateRoadSheetOne> listFour = taskEvaluateMapper.selectByCheckCity(taskEvaluate);
//        //查询当前任务路线对应检测出来的服务区
//        for (EvaluateRoadSheetOne sheetOne : listFour) {
//            //处理得分汇总数据
//            this.dealWithSheetOneWord(sheetOne, taskEvaluate, true);
//            if(StringUtils.isEmpty(sheetOne.getCity())){
//                sheetOne.setCity("-");
//            }
//        }
//        //建筑控制区得分--建筑用地总分排序 desc
//        listFour=listFour.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getBuildingTotalScore).reversed()).collect(Collectors.toList());
//        this.initExcelSheetFour(listFour.stream().filter(v->v.getBuildingTotalScore().compareTo(new BigDecimal(100))!=0).collect(Collectors.toList()), workbook, workbook.getSheetAt(4), 16, 1);
//        this.initExcelSheetFourCount(listFour.stream().filter(v->v.getBuildingTotalCount()>0).collect(Collectors.toList()), workbook, workbook.getSheetAt(5), 16, 1);

        //建筑控制区得分排名
//        log.info("建筑控制区得分排名"+ cn.hutool.core.date.DateUtil.now());
//        List<EvaluateRoadSheetThree> listFive = this.dealWithSheetFive(listFour,true);
//        this.initExcelSheetThree(listFive, workbook, workbook.getSheetAt(6), 3, 1);
        if (taskEvaluate.isInitFile()) {
            file = this.initExcelPath(workbook, "问题数量-得分-排名"+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
        } else {
            try {
                workbook.write(response.getOutputStream());
            } catch (Exception e) {
                log.error("导出Excel异常{}", e.getMessage());
            } finally {
                IOUtils.closeQuietly(workbook);
            }
        }
        return file;
    }


    @Override
    public List<RankStat> matchCompanyList(TaskEvaluate taskEvaluate){
        List<RankStat> resultList = new ArrayList<>();
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            return Lists.newArrayList();
        }
        //查询当前任务路线对应检测出来的服务区
        log.info("查询当前任务路线对应检测出来的服务区"+ cn.hutool.core.date.DateUtil.now());
        List<EvaluateRoadSheetOne> baseInfoList = this.listBaseInfoByTaskIdList(listOne,taskEvaluate.getType());
        //'服务区','隧道出入口','互通区及收费站'
        Map<String, String> serviceAreaMap = baseInfoList.stream().filter(v -> Objects.equals("服务区", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> tunnelMap = baseInfoList.stream().filter(v -> Objects.equals("隧道出入口", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> feeAreaMap = baseInfoList.stream().filter(v -> Objects.equals("互通区及收费站", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            //处理得分汇总数据
            this.dealWithSheetOne(sheetOne, serviceAreaMap, tunnelMap, feeAreaMap, taskEvaluate, true);
        }
        //查询出维护了匹配公司的数据
        List<Road>  matchCompanyList=taskEvaluateMapper.selectSpecialCompanyList(new TaskEvaluate());
        Map<String,String> matchCompanyMap=matchCompanyList.stream().collect(Collectors.toMap(Road::getCompanyName, Road::getMatchCompanyName, (key1, key2) -> key1));
        //把listOne深拷贝一份,新的集合
        List<EvaluateRoadSheetOne> serviceAreaList=JSON.parseArray(JSON.toJSONString(listOne),EvaluateRoadSheetOne.class);
        Map<String, List<EvaluateRoadSheetOne>> companyServiceAreaMap = serviceAreaList.stream().filter(e -> {
            if(matchCompanyMap.containsKey(e.getCompanyName())){
                return true;
            }else{
                return false;
            }
        }).map(e -> {
            e.setCompanyName(matchCompanyMap.get(e.getCompanyName()));
            return e;
        }).collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCompanyName));
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : companyServiceAreaMap.entrySet()) {
            RankStat rankStat = new RankStat();
            rankStat.setCompanyName(entry.getKey());
            rankStat.setCompany(entry.getKey());
            int totalCount = entry.getValue().stream().mapToInt(EvaluateRoadSheetOne::getServiceAreaTempCount).sum();
            rankStat.setScore(new BigDecimal(totalCount));
            resultList.add(rankStat);
        }
        return resultList;
    }

    @Override
    public List<TaskEvaluate> selectByTaskIdList(List<Long> taskIdList) {
        return taskEvaluateMapper.selectByTaskIdList(taskIdList);
    }


    private List<EvaluateRoadSheetOne> listBaseInfoByTaskIdList (List<EvaluateRoadSheetOne> list,Integer type){
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        return taskDetailMapper.listBaseInfoByTaskIdList(list,type);
    }


    /**
     * 生成excel临时文件
     *
     * @param document
     * @return
     */
    private File initWordPath(XWPFDocument document, String fileName, String sourcePath) throws IOException {
        //存储到本地文件目录
        Path path = createDirectories(sourcePath);
        String tempPath = path.toAbsolutePath() + "/" + fileName + ".docx";
        // 本地文件
        try {
            FileOutputStream out = new FileOutputStream(tempPath) ;
            document.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            document.close();
        }
        return new File(tempPath);
    }


    /**
     * 循环创建多级文件目录
     *
     * @param directoryPath
     * @throws IOException
     */
    public static Path createDirectories(String directoryPath) throws IOException {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            log.info("目录已成功创建: " + directoryPath);
        } else {
            log.info("目录已存在: " + directoryPath);
        }
        return path;
    }


    /**
     * 生成excel临时文件
     *
     * @param workbook
     * @return
     */
    private File initExcelPath(XSSFWorkbook workbook, String fileName, String sourcePath) throws IOException {
        //存储到本地文件目录
        Path path = createDirectories(sourcePath);
        String tempPath = path.toAbsolutePath() + "/" + fileName + ".xlsx";
        // 本地文件
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(tempPath);
            workbook.write(out);
            workbook.close();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return new File(tempPath);
    }


    /**
     * 路域环境excel导出
     *
     * @param response
     * @param taskEvaluate
     */
    @Override
    public File exportTaskEvaluateEnv(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException {
        File file = null;
        //获取模板
        XSSFWorkbook workbook = this.initExcelTemplate(response, "roadEvaluateEnv");
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectEnvByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            throw new ServiceException("当前任务下没有检测数据");
        }
        Map<String, List<EvaluateRoadSheetOne>> mapOne = Maps.newLinkedHashMap();
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            String key = sheetOne.getTaskId() + "_" + sheetOne.getStartCheckCode() + "_" + sheetOne.getEndCheckCode();
            if (!mapOne.containsKey(key)) {
                mapOne.put(key, Lists.newArrayList(sheetOne));
            } else {
                mapOne.get(key).add(sheetOne);
            }
        }

        List<EvaluateRoadSheetOne> resultOneList = Lists.newArrayList();
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : mapOne.entrySet()) {
            EvaluateRoadSheetOne sheetOne = Cloner.standard().deepClone(entry.getValue().get(0));
            sheetOne.setRoadMaintenance(this.initItems(entry, "公路设施维护"));
            sheetOne.setRoadClean(this.initItems(entry, "公路沿线保洁"));
            sheetOne.setTunnelManage(this.initItems(entry, "隧道设施管理"));
            sheetOne.setRoadGreen(this.initItems(entry, "公路沿线绿化"));
            sheetOne.setTagManage(this.initItems(entry, "标志标线管理"));
            resultOneList.add(sheetOne);
        }
        //病害明细--
        this.initExcelSheetEnvOne(resultOneList, workbook, workbook.getSheetAt(0), 16, 3);
        //分数明细
        List<EvaluateRoadSheetOne> listTwo = taskEvaluateMapper.selectByParams(taskEvaluate);
        for (EvaluateRoadSheetOne sheet : listTwo) {
            //处理得分汇总数据
            this.dealWithSheetOne(sheet, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap(), taskEvaluate, true);
        }
        //分数明细--使用序号排序 asc
        listTwo=listTwo.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getCode)).collect(Collectors.toList());
        this.initExcelSheetTwoEnv(listTwo, workbook, workbook.getSheetAt(1), 16, 1);
        //分数明细-病害数量统计
        this.initExcelSheetTwoEnvCount(listTwo, workbook, workbook.getSheetAt(2), 16, 1);
        //管养单位
        List<EvaluateRoadSheetThree> listThree = this.dealWithSheetThreeCount(listTwo,true);
        //管养单位--根据路段平均分排序  desc
        this.initExcelSheetThreeCountQuan(listThree, workbook, workbook.getSheetAt(3), 6, 1);
        if (taskEvaluate.isInitFile()) {
            file = this.initExcelPath(workbook, "分数汇总"+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
        } else {
            try {
                workbook.write(response.getOutputStream());
            } catch (Exception e) {
                log.error("导出Excel异常{}", e.getMessage());
            } finally {
                IOUtils.closeQuietly(workbook);
            }
        }
        return file;
    }

    @Override
    public void exportEvaluate(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException {
        taskEvaluate.setStatus(1);
        if (Objects.equals(1, taskEvaluate.getType())) {
            this.exportTaskEvaluate(response, taskEvaluate);
        } else if (Objects.equals(2, taskEvaluate.getType())) {
            this.exportTaskEvaluateEnv(response, taskEvaluate);
        } else {
            //典型病害照片
            taskEvaluate.setType(2);
            this.exportFocusPicEnv(response, taskEvaluate,1);
        }
    }

    @Override
    public File exportWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        File file = null;
        //获取模板
        XWPFDocument document = new XWPFDocument();
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectQuestionListByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            throw new ServiceException("当前任务下没有检测数据");
        }
        Map<String, List<EvaluateRoadSheetOne>> mapOne = Maps.newLinkedHashMap();
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            String key = sheetOne.getCode() + "_" + sheetOne.getStartCheckCode() + "_" + sheetOne.getEndCheckCode() + "_" + sheetOne.getDirection();
            if (!mapOne.containsKey(key)) {
                mapOne.put(key, Lists.newArrayList(sheetOne));
            } else {
                mapOne.get(key).add(sheetOne);
            }
        }
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : mapOne.entrySet()) {
            EvaluateRoadSheetOne sheet = entry.getValue().get(0);
            XWPFParagraph titleParagraph;
            XWPFTable targetTable;
            titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            targetTable = document.createTable(3 + entry.getValue().size(), 9);
            targetTable.setTableAlignment(TableRowAlign.CENTER);
            targetTable.setWidth("10000");
            this.doubleTitle3(targetTable);
            //设置表头名称
            String tableName = sheet.getRoadCode() + sheet.getRoadName() + this.formatCheckCode(sheet.getStartCode()) + "-" + this.formatCheckCode(sheet.getEndCode()) + "段";
            XWPFRun xXWPFRun=this.addTableTitle(titleParagraph, tableName + "养护管理评价问题清单");
            // 创建段落的属性
            CTPPr pPr = titleParagraph.getCTP().getPPr();
            if (pPr == null) {
                pPr = titleParagraph.getCTP().addNewPPr();
            }
            // 设置段落样式为 Word 内置的标题样式（例如 "Heading1" 表示一级标题）
            CTString styleStr = pPr.isSetPStyle() ? pPr.getPStyle() : pPr.addNewPStyle();
            styleStr.setVal("Heading1");  // "Heading1" 对应 Word 中的一级标题样式，"Heading2" 对应二级标题
            xXWPFRun.setFontSize(14);
            //设置固定表头参数
            for (int k = 0; k < 3; k++) {
                XWPFTableRow row = targetTable.getRow(k);
                this.initRowWidthAndHeight(row);
                if (k == 0) {
                    setCellTextDefault(row.getCell(0),"路段：");
                    row.getCell(1).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    row.getCell(2).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    row.getCell(3).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    setCellTextDefault(row.getCell(1),tableName);
                    setCellTextDefault(row.getCell(4),"管养单位：");
                    row.getCell(5).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    row.getCell(6).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    row.getCell(7).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    row.getCell(8).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    setCellTextDefault(row.getCell(5),sheet.getCompanyName());
                } else if (k == 1) {
                    row.getCell(0).getParagraphs().get(0).setAlignment(ParagraphAlignment.CENTER);
                    setCellTextDefault(row.getCell(0),"调查路段：");
                    setCellTextDefault(row.getCell(1),this.formatCheckCode(sheet.getStartCheckCode()) + "-" + this.formatCheckCode(sheet.getEndCheckCode()));
                    setCellTextDefault(row.getCell(2),"方向：");
                    setCellTextDefault(row.getCell(3),Objects.equals(1, sheet.getDirection()) ? "左幅" : "右幅");
                    setCellTextDefault(row.getCell(4),"调查隧道名称：");
                    row.getCell(5).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    row.getCell(6).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    //查询对应的隧道
                    List<String> tunnelList=taskEvaluateMapper.selectTunnelNameByParams(sheet);
                    if(!CollectionUtils.isEmpty(tunnelList)){
                        row.getCell(5).setText(StringUtils.join(tunnelList, ","));
                    }else{
                        row.getCell(5).setText("/");
                    }
                    setCellTextDefault(row.getCell(7),"方向：");
                    setCellTextDefault(row.getCell(8),Objects.equals(1, sheet.getDirection()) ? "左幅" : "右幅");
                } else {
                    setCellTextDefault(row.getCell(0),"类型：");
                    setCellTextDefault(row.getCell(1),"存在问题及位置");
                    row.getCell(2).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    row.getCell(3).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    row.getCell(4).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    setCellTextDefault(row.getCell(2),"照片：");
                    setCellTextDefault(row.getCell(5),"记录日期：");
                    setCellTextDefault(row.getCell(6),"计划整改日期：");
                    row.getCell(7).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    row.getCell(8).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    setCellTextDefault(row.getCell(7),"完成情况");
                }
            }
            this.dealWithTable(entry, targetTable);
        }
        if (taskEvaluate.isInitFile()) {
            file = this.initWordPath(document, "照片结果"+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
        } else {
            // 将文档写入字节数组
            this.initWord(response, document);
        }
        return file;
    }


    // 根据字符类型设置字体
    private static void setCellTextDefault(XWPFTableCell cell, String text) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.createRun();
        if(Objects.nonNull(text)){
            for (char c : text.toCharArray()) {
                if (isChinese(c)) {
                    run.setFontFamily("宋体"); // 设置中文字体
                } else {
                    run.setFontFamily("Times New Roman"); // 设置英文和数字字体
                }
                run.setText(String.valueOf(c), run.getTextPosition()); // 逐字符写入
            }
        }
    }

    @Override
    public File exportPicWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        File file = null;
        XWPFDocument document = this.initWordTemplate(response, "picWord");
        //建筑控制区典型照片（×月）
        log.info("工作照片（×月）"+ cn.hutool.core.date.DateUtil.now());
        this.packageWorkPic(taskEvaluate, document);
        // 设置页面宽度和边距
        //监测路段信息（×月）
        log.info("监测路段信息（×月）"+ cn.hutool.core.date.DateUtil.now());
        List<EvaluateRoadSheetOne> listOne = this.packageCheckRoad(document, taskEvaluate);
        //各高速公路监测路段得分（×月）
        log.info("各高速公路监测路段病害数量统计（×月）"+ cn.hutool.core.date.DateUtil.now());
        this.packageCheckRoadScore(document, taskEvaluate);
        //各高速公路管养单位得分（×月）
        log.info("各高速公路管养单位病害数量统计（×月）"+ cn.hutool.core.date.DateUtil.now());
        this.packageRoadByCompanyName(document, listOne);
        //建筑控制区得分（×月）
        log.info("建筑控制区病害数量统计（×月）"+ cn.hutool.core.date.DateUtil.now());
        List<EvaluateRoadSheetOne> listFour = this.packageBuildingScore(document, taskEvaluate);
        //建筑用地-各市州得分（×月）
        log.info("各市州病害数量统计（×月）"+ cn.hutool.core.date.DateUtil.now());
        //TODO 待邓仪莉确认
        this.initBuildingScoreByCity(document, listFour);
        //公路用地典型照片（×月）
        log.info("公路用地典型照片（×月）"+ cn.hutool.core.date.DateUtil.now());
        this.packageFocusPic(taskEvaluate, document, "公路用地",1);
        //建筑控制区典型照片（×月）
        log.info("建筑控制区典型照片（×月）"+ cn.hutool.core.date.DateUtil.now());
        this.packageFocusPic(taskEvaluate, document, "建筑用地",null);
        // 将文档写入字节数组
        if (taskEvaluate.isInitFile()) {
            file = this.initWordPath(document, "典型照片示例-"+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
        } else {
            // 将文档写入字节数组
            this.initWord(response, document);
        }
        return file;
    }

    @Override
    public synchronized void handleAddAll(TaskEvaluate taskEvaluate) {
        //查询已经检测完成,但是没有生成评定数据的task
        if (StringUtils.isEmpty(taskEvaluate.getTaskDate()) && CollectionUtils.isEmpty(taskEvaluate.getTaskDateList())) {
            throw new ServiceException("月份不能为空");
        }
        taskEvaluate.setStatus(1);
        List<Long> taskIdList = taskMapper.selectIdListByStatusParams(taskEvaluate);
        if (CollectionUtils.isEmpty(taskIdList)) {
            throw new ServiceException("已检测完成的任务为空");
        }
        taskEvaluate.setRemark("一键评定");
        for (Long taskId : taskIdList) {
            taskEvaluate.setTaskId(taskId);
            taskEvaluateService.insertTaskEvaluate(taskEvaluate);
        }
    }

    @Override
    public void exportZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        this.commonCheck(taskEvaluate);
        String sourceBasePath="/tmp/taskEvaluate/" + System.currentTimeMillis()+"/";
        String sourceDirPath = sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测结果-":"路域环境结果-")+ this.yearMonthDate(taskEvaluate.getTaskDate()) +"/";
        taskEvaluate.setSourcePath(sourceDirPath);
        taskEvaluate.setInitFile(true);
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);
        //生成对应的zip
        String zipFile=sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测结果":"路域环境结果")+".zip";

        try{
            if (Objects.equals(taskEvaluate.getType(), 1)) {
                TaskEvaluate taskEvaluate1=Cloner.standard().deepClone(taskEvaluate);
                TaskEvaluate taskEvaluate2=Cloner.standard().deepClone(taskEvaluate);
                TaskEvaluate taskEvaluate3=Cloner.standard().deepClone(taskEvaluate);
//                TaskEvaluate taskEvaluate4=Cloner.standard().deepClone(taskEvaluate);
                //专项监测导出
                //典型照片示例.doc
                this.taskEvaluateService.exportPicWord(response, taskEvaluate1);
//            //绿化专项得分排名示例.xlsx
                this.exportTaskEvaluate(response, taskEvaluate2);
//                this.exportFocusPicEnv(response, taskEvaluate4,null);
                this.exportFocusPicEnv(response, taskEvaluate3,1);
//            //绿化专项典型病害照片示例.xlsx
                // 获取文件结果
            } else {
                TaskEvaluate taskEvaluate1=Cloner.standard().deepClone(taskEvaluate);
                TaskEvaluate taskEvaluate2=Cloner.standard().deepClone(taskEvaluate);
                //路域环境导出
                //分数汇总.xlsx
                this.exportTaskEvaluateEnv(response, taskEvaluate1);
                //照片结果示例.docx
                taskEvaluateService.exportWord(response, taskEvaluate2);
            }
            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                File fileToZip = new File(sourceDirPath);
                //将整个文件夹打包成zip
                zipFile(fileToZip, fileToZip.getName(), zos);
            } catch (IOException e) {
                throw e;
            }
            File zip=new File(zipFile);
            // 设置response头信息
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename="+zip.getName());
            response.setContentLengthLong(zip.length());
            // 将zip文件写入response输出流
            // 使用 try-with-resources 保证流的正确关闭
            try (FileInputStream fis = new FileInputStream(zip);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, len);
                }
                os.flush(); // 确保所有数据都写入输出流
            } catch (IOException e) {
                // 记录错误日志或进行必要处理
                e.printStackTrace();
                throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
            }finally {
                // 删除生成的ZIP文件
                zip.delete();
                log.info("生成的ZIP文件已删除: " + zip.getAbsolutePath());
            }
        }catch (Exception e){
            throw e;
        }finally {
            // 删除源文件夹下的所有文件和文件夹
            deleteFolderAndFiles(sourceBasePath);
        }

    }




    @Override
    public void exportSheetPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        this.commonCheck(taskEvaluate);
        String sourceBasePath="/tmp/taskEvaluate/" + System.currentTimeMillis()+"/";
        String sourceDirPath = sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测结果-":"路域环境结果-")+ this.yearMonthDate(taskEvaluate.getTaskDate()) +"/";
        taskEvaluate.setSourcePath(sourceDirPath);
        taskEvaluate.setInitFile(true);
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);
        //生成对应的zip
        String zipFile=sourceBasePath+"全部照片表格.zip";
        File zip = null;
        log.info("第1步");
        try{
            this.exportFocusPicEnv(response, taskEvaluate,null);
            log.info("第2步");
            // 创建ZIP文件
            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                
                // 设置压缩级别以节省内存
                zos.setLevel(ZipOutputStream.DEFLATED);
                
                File fileToZip = new File(sourceDirPath);
                if (!fileToZip.exists()) {
                    throw new IOException("源文件夹不存在: " + sourceDirPath);
                }
                
                //将整个文件夹打包成zip
                zipFile(fileToZip, fileToZip.getName(), zos);
                zos.finish();
            }
            log.info("第3步");
            zip = new File(zipFile);
            if (!zip.exists() || zip.length() == 0) {
                throw new IOException("ZIP文件创建失败或为空");
            }
            
            // 设置response头信息
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + zip.getName());
            response.setContentLengthLong(zip.length());
            log.info("第4步");
            // 将zip文件写入response输出流
            try (FileInputStream fis = new FileInputStream(zip);
                 BufferedInputStream bis = new BufferedInputStream(fis);
                 OutputStream os = response.getOutputStream();
                 BufferedOutputStream bos = new BufferedOutputStream(os)) {
                
                byte[] buffer = new byte[8192]; // 增大缓冲区
                int len;
                while ((len = bis.read(buffer)) != -1) {
                    bos.write(buffer, 0, len);
                }
                bos.flush();
            }
            log.info("ZIP文件导出成功: {}, 大小: {} bytes", zip.getName(), zip.length());
            
        } catch (Exception e) {
            log.error("导出ZIP文件异常", e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        } finally {
            // 删除生成的ZIP文件
            if (zip != null && zip.exists()) {
                boolean deleted = zip.delete();
                log.info("生成的ZIP文件删除{}: {}", deleted ? "成功" : "失败", zip.getAbsolutePath());
            }
            
            // 删除源文件夹下的所有文件和文件夹
            try {
                deleteFolderAndFiles(sourceBasePath);
                log.info("临时文件夹清理完成: {}", sourceBasePath);
            } catch (Exception e) {
                log.error("清理临时文件夹异常: {}", sourceBasePath, e);
            }
            
            // 强制垃圾回收
            System.gc();
        }
    }

    @Override
    public void exportSheetServiceAreaPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException {
        this.commonCheck(taskEvaluate);
        String sourceBasePath="/tmp/taskEvaluate/" + System.currentTimeMillis()+"/";
        String sourceDirPath = sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测结果-":"路域环境结果-")+ this.yearMonthDate(taskEvaluate.getTaskDate()) +"/";
        taskEvaluate.setSourcePath(sourceDirPath);
        taskEvaluate.setInitFile(true);
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);
        //生成对应的zip
        String zipFile=sourceBasePath+"全部照片表格.zip";
        try{
            this.exportFocusPicServiceArea(response, taskEvaluate,null);
//            //绿化专项典型病害照片示例.xlsx
            // 获取文件结果
            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                File fileToZip = new File(sourceDirPath);
                //将整个文件夹打包成zip
                zipFile(fileToZip, fileToZip.getName(), zos);
            } catch (IOException e) {
                throw e;
            }
            File zip=new File(zipFile);
            // 设置response头信息
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename="+zip.getName());
            response.setContentLengthLong(zip.length());
            // 将zip文件写入response输出流
            // 使用 try-with-resources 保证流的正确关闭
            try (FileInputStream fis = new FileInputStream(zip);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, len);
                }
                os.flush(); // 确保所有数据都写入输出流
            } catch (IOException e) {
                // 记录错误日志或进行必要处理
                e.printStackTrace();
                throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
            }finally {
                // 删除生成的ZIP文件
                zip.delete();
                log.info("生成的ZIP文件已删除: " + zip.getAbsolutePath());
                log.info("所有文件已生成并压缩到output.zip");
            }
        }catch (Exception e){
            throw e;
        }finally {
            // 删除源文件夹下的所有文件和文件夹
            deleteFolderAndFiles(sourceBasePath);
        }

    }

    /**
     * 当前年月日
     * @return 格式化后的日期字符串，如 "2025年1月-1"
     */
    public static String yearMonthDate(String taskDate) {
        try {
            Date date;
            String formattedDate;

            // 判断 taskDate 是否包含日（即是否有两个 '-'）
            if (taskDate.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                // 格式 "yyyy-M-d"
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-M-d");
                date = inputFormat.parse(taskDate);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年M月-d");
                formattedDate = outputFormat.format(date);
            } else if (taskDate.matches("\\d{4}-\\d{1,2}")) {
                // 格式 "yyyy-M"
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-M");
                date = inputFormat.parse(taskDate);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年M月");
                formattedDate = outputFormat.format(date);
            } else {
                log.error("输入格式错误: " + taskDate);
                return null;
            }
            log.info(formattedDate); // 示例输出: 2025年1月 或 2025年1月/1
            return formattedDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void exportPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException {
        this.commonCheck(taskEvaluate);

        // 获取当前日期
        String sourceBasePath="/tmp/taskEvaluate/" + System.currentTimeMillis()+"/";
        String sourceDirPath = sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测照片":"路域环境照片")+"/";
        taskEvaluate.setSourcePath(sourceDirPath);
        taskEvaluate.setInitFile(true);
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);

        //专项监测照片
        this.initPictures(taskEvaluate, sourceDirPath);
        //生成对应的zip
        String zipFile=sourceBasePath+(Objects.equals(taskEvaluate.getType(),1)?"专项监测照片":"路域环境照片")+".zip";
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            File fileToZip = new File(sourceDirPath);
            //将整个文件夹打包成zip
            zipFile(fileToZip, fileToZip.getName(), zos);
        } catch (IOException e) {
            // 删除源文件夹下的所有文件和文件夹
            deleteFolderAndFiles(sourceBasePath);
            throw e;
        }
        File zip = new File(zipFile);
        try {
            // 设置 response 头信息
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + zip.getName());
            response.setContentLengthLong(zip.length());

            // 使用 NIO FileChannel 读取文件
            try (FileChannel fileChannel = FileChannel.open(zip.toPath(), StandardOpenOption.READ);
                 ServletOutputStream os = response.getOutputStream()) {
                ByteBuffer buffer = ByteBuffer.allocate(1048576); // 1MB 缓冲区 (1024 * 1024)
                int bytesRead;
                // 边读边写
                while ((bytesRead = fileChannel.read(buffer)) != -1) {
                    buffer.flip(); // 切换到读模式
                    os.write(buffer.array(), 0, bytesRead); // 写入输出流
                    os.flush(); // 确保数据发送到客户端
                    buffer.clear(); // 重置缓冲区
                }
            } catch (IOException e) {
                log.error("文件流传输失败: " + e.getMessage(), e);
                throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
            }
        } finally {
            // 删除临时文件并记录日志
            if (zip.delete()) {
                log.info("生成的ZIP文件已删除: " + zip.getAbsolutePath());
            } else {
                log.error("无法删除ZIP文件: " + zip.getAbsolutePath());
            }
            deleteFolderAndFiles(sourceBasePath);
            log.info("所有文件已生成并压缩到output.zip");
        }
    }


    private static void zipFile(File fileToZip, String fileName, ZipOutputStream zos) throws IOException {
        if (fileToZip.isHidden()) {
            return;
        }
        if (fileToZip.isDirectory()) {
            String entryName = fileName.endsWith("/") ? fileName : fileName + "/";
            zos.putNextEntry(new ZipEntry(entryName));
            zos.closeEntry();
            File[] children = fileToZip.listFiles();
            if (children != null) {
                for (File childFile : children) {
                    zipFile(childFile, fileName + "/" + childFile.getName(), zos);
                }
            }
            return;
        }

        // 使用 NIO 处理文件
        try (FileChannel fileChannel = FileChannel.open(fileToZip.toPath(), StandardOpenOption.READ)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);
            ByteBuffer buffer = ByteBuffer.allocate(1048576); // 1MB 缓冲区
            int bytesRead;
            while ((bytesRead = fileChannel.read(buffer)) != -1) {
                buffer.flip();
                zos.write(buffer.array(), 0, bytesRead);
                buffer.clear();
            }
            zos.closeEntry(); // 确保条目关闭
        }
    }



    private void initPictures(TaskEvaluate taskEvaluate, String sourceDirPath) throws IOException {
        log.info("initPictures"+ cn.hutool.core.date.DateUtil.now());
        List<TaskDetail> detailList=taskEvaluateMapper.selectPicListByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(detailList)){
            throw new ServiceException("导出数据为空");
        }
        // 发送初始进度信息
        if (taskEvaluate.getDownloadTaskId() != null) {
            WebSocketServer.sendProgressInfoWithTaskId(taskEvaluate.getDownloadTaskId(), 2,0, "正在准备照片数据...", 0, 0);
        }
        
        //先根据管养公司进行分组
        Map<String, List<TaskDetail>> taskDetailMap = detailList.stream().collect(Collectors.groupingBy(TaskDetail::getCompanyName));
        
        // 计算总照片数量
        int totalPhotos = 0;
        for (Map.Entry<String, List<TaskDetail>> companyNameEntry : taskDetailMap.entrySet()) {
            for (TaskDetail taskDetail : companyNameEntry.getValue()) {
                if (!StringUtils.isEmpty(taskDetail.getScenePic1())) {
                    totalPhotos++;
                }
            }
        }
        
        // 已处理照片计数
        int processedPhotos = 0;
        
        // 发送总照片数量信息
        if (taskEvaluate.getDownloadTaskId() != null) {
            WebSocketServer.sendProgressInfoWithTaskId(taskEvaluate.getDownloadTaskId(), 2, 1,"正在从服务端加载照片...", totalPhotos, processedPhotos);
        }
        
        for (Map.Entry<String, List<TaskDetail>> companyNameEntry : taskDetailMap.entrySet()) {
            //创建文件路径
            String tempPath=sourceDirPath+"/"+this.yearMonthDate(taskEvaluate.getTaskDate())+"/"+companyNameEntry.getKey();
            File tempDir = new File(tempPath);
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
            Map<String,List<TaskDetail>> checkCodeMap=Maps.newHashMap();
            for (TaskDetail taskDetail : companyNameEntry.getValue()) {
                String key =taskDetail.getRoadCode()+"_"+taskDetail.getRoadName()+ this.formatCheckCode(taskDetail.getStartCode())+"-"+this.formatCheckCode(taskDetail.getEndCode());
                if(checkCodeMap.containsKey(key)){
                    checkCodeMap.get(key).add(taskDetail);
                }else{
                    checkCodeMap.put(key,Lists.newArrayList(taskDetail));
                }
            }
            for (Map.Entry<String, List<TaskDetail>> entry : checkCodeMap.entrySet()) {
                //下载网络图片
                String path=tempPath+"/"+entry.getKey();
                File pathDir = new File(path);
                if (!pathDir.exists()) {
                    pathDir.mkdirs();
                }
                for (TaskDetail taskDetail : entry.getValue()) {
                    if(StringUtils.isEmpty(taskDetail.getScenePic1())){
                        continue;
                    }
                    String picPath=null;
                    if(taskDetail.getScenePic1().indexOf("_")>-1){
                        picPath=path+"/"+taskDetail.getScenePic1().split("_")[1];
                    }else{
                        picPath=path+"/"+extractFileName(taskDetail.getScenePic1());
                    }
                    try {
                        this.downloadImage(taskDetail.getScenePic1(), new File(picPath));
                        
                        // 更新进度
                        processedPhotos++;
                        if (taskEvaluate.getUserId() != null) {
                            int progress = (int) ((processedPhotos * 100.0) / totalPhotos);
                            if (progress > 100) progress = 100;
                            
                            // 每处理5张照片或者是最后一张照片时发送进度更新
                            if (processedPhotos % 5 == 0 || processedPhotos == totalPhotos) {
                                WebSocketServer.sendProgressInfoWithTaskId(taskEvaluate.getDownloadTaskId(),2, progress, "正在从服务端加载照片...", totalPhotos, processedPhotos);
                            }
                        }
                    } catch (Exception e){
                        log.info("下载图片失败："+taskDetail.getScenePic1());
                    }
                }
            }
        }
        
        // 发送完成进度信息
        if (taskEvaluate.getDownloadTaskId() != null) {
            WebSocketServer.sendProgressInfoWithTaskId(taskEvaluate.getDownloadTaskId(), 2,100, "照片加载完成，正在生成压缩包...", totalPhotos, processedPhotos);
        }
    }


    public static String extractFileName(String url) {
        // 找到最后一个斜杠的位置
        int lastSlashIndex = url.lastIndexOf('/');

        // 获取最后一个斜杠后的部分，即文件名
        String fileName = url.substring(lastSlashIndex + 1);

        // 确定需要截取的长度
        int lengthToExtract = 17 + 4; // 17个字符的文件名 + ".jpg"的4个字符

        // 从文件名中截取最后的 lengthToExtract 个字符
        return fileName.substring(fileName.length() - lengthToExtract);
    }

    // 下载图片并存储到指定位置
    private  File downloadImage(String imageUrl, File destination) throws IOException {
        imageUrl=this.replaceImageUrl(imageUrl);
        if(StringUtils.isEmpty(imageUrl)){
            return null;
        }
        
        InputStream inputStream = null;
        BufferedImage originalImage = null;
        BufferedImage compressedImage = null;
        Graphics graphics = null;
        ByteArrayOutputStream baos = null;
        InputStream input = null;
        
        try {
            inputStream = new URL(imageUrl).openStream();
            originalImage = ImageIO.read(inputStream);
            
            // 压缩图片
            compressedImage = new BufferedImage(
                    originalImage.getWidth() / 1,
                    originalImage.getHeight() / 1,
                    originalImage.getType()
            );
            graphics = compressedImage.getGraphics();
            graphics.drawImage(originalImage, 0, 0, compressedImage.getWidth(), compressedImage.getHeight(), null);
            
            // 将压缩后的图片写入字节数组输出流
            baos = new ByteArrayOutputStream();
            ImageIO.write(compressedImage, "jpg", baos);
            baos.flush();
            byte[] imageBytes = baos.toByteArray();
            
            input = new ByteArrayInputStream(imageBytes);
            Files.copy(input, destination.toPath(), StandardCopyOption.REPLACE_EXISTING);
            
        } finally {
            // 确保所有资源都被正确释放
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    log.error("关闭input异常", e);
                }
            }
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    log.error("关闭baos异常", e);
                }
            }
            if (graphics != null) {
                graphics.dispose();
            }
            if (compressedImage != null) {
                compressedImage.flush();
            }
            if (originalImage != null) {
                originalImage.flush();
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭inputStream异常", e);
                }
            }
        }
        return destination;
    }


    public static void deleteFolderAndFiles(String folderPath) {
        log.info("开始进行文件夹删除");
        Path folder = Paths.get(folderPath);
        try {
            Files.walk(folder)
                    .sorted(Comparator.reverseOrder()) // 先删除子文件和子目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            log.info("Deleted: " + path.toString());
                        } catch (IOException e) {
                            System.err.println("Failed to delete: " + path + " due to " + e.getMessage());
                        }
                    });
        } catch (IOException e) {
            System.err.println("Error walking through directory: " + e.getMessage());
        }
    }


    // 将文件夹压缩到zip文件中
    private static void fileToZip(File fileToZip, String fileName, ZipOutputStream zos) throws IOException {
        if (fileToZip.isHidden()) {
            return;
        }
        if (fileToZip.isDirectory()) {
            if (fileName.endsWith("/")) {
                zos.putNextEntry(new ZipEntry(fileName));
                zos.closeEntry();
            } else {
                zos.putNextEntry(new ZipEntry(fileName + "/"));
                zos.closeEntry();
            }
            File[] children = fileToZip.listFiles();
            for (File childFile : children) {
                fileToZip(childFile, fileName + "/" + childFile.getName(), zos);
            }
            return;
        }
        FileInputStream fis = new FileInputStream(fileToZip);
        ZipEntry zipEntry = new ZipEntry(fileName);
        zos.putNextEntry(zipEntry);
        byte[] bytes = new byte[1024];
        int length;
        while ((length = fis.read(bytes)) >= 0) {
            zos.write(bytes, 0, length);
        }
        fis.close();
    }

    private void packageFocusPic(TaskEvaluate taskEvaluate, XWPFDocument document, String checkType,Integer isFocus) throws IOException, InvalidFormatException {
        //获取模板
        taskEvaluate.setCheckType(checkType);
        taskEvaluate.setTaskId(null);
        taskEvaluate.setIsFocus(isFocus);
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectFocusPicEnv(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            log.info("packageFocusPic典型照片为空");
            return;
        }
        //标题
        this.initTitle(document, checkType + "典型照片(" + taskEvaluate.getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 5;
        targetTable = document.createTable(1 + listOne.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("12000");
        // 设置表格重复标题行
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, checkType + "典型照片(" + taskEvaluate.getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"单位名称");
        row.getCell(1).setWidth("3000");
        setCellTextDefault(row.getCell(2),"问题路段");
        row.getCell(2).setWidth("3200");
        setCellTextDefault(row.getCell(3),"主要存在问题");
        row.getCell(3).setWidth("3000");
        setCellTextDefault(row.getCell(4),"整改前照片");

        //设置固定表头参数
        for (int k = 0; k < listOne.size(); k++) {
            EvaluateRoadSheetOne sheet = listOne.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            setCellTextDefault(rowTemp.getCell(1),sheet.getCompanyName());
            rowTemp.getCell(1).setWidth("3000");
            String questionRoad = sheet.getRoadCode() + sheet.getRoadName() + (Objects.equals(1, sheet.getDirection()) ? "左幅" : "右幅") + this.formatCheckCode(sheet.getCheckCode()) + "处";
            setCellTextDefault(rowTemp.getCell(2),questionRoad);
            rowTemp.getCell(2).setWidth("3200");
            setCellTextDefault(rowTemp.getCell(3),sheet.getQuestionType());
            rowTemp.getCell(3).setWidth("3000");
            this.insertWordImage(rowTemp.getCell(4), sheet.getScenePic1(),200,150);
        }
    }


    /**
     * 工作照照片
     * @param taskEvaluate
     * @param document
     * @throws IOException
     * @throws InvalidFormatException
     */
    private void packageWorkPic(TaskEvaluate taskEvaluate, XWPFDocument document) throws IOException, InvalidFormatException {
        //获取模板
        taskEvaluate.setTaskId(null);
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectWorkPic(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            log.info("packageWorkPic工作照照片");
            return;
        }
        //标题
        this.initTitle(document,  "工作照(" + taskEvaluate.getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 3;
        targetTable = document.createTable(1 + listOne.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("12000");
        // 设置表格重复标题行
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph,   "工作照(" + taskEvaluate.getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"路段名称");
        row.getCell(1).setWidth("3200");
        setCellTextDefault(row.getCell(2),"工作照片");
        row.getCell(2).setWidth("4200");

        //设置固定表头参数
        for (int k = 0; k < listOne.size(); k++) {
            EvaluateRoadSheetOne sheet = listOne.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            String questionRoad = sheet.getRoadCode() + sheet.getRoadName();
            setCellTextDefault(rowTemp.getCell(1),questionRoad);
            rowTemp.getCell(1).setWidth("3200");
            this.insertWordImage(rowTemp.getCell(2), sheet.getWorkPic(),200,150);
            rowTemp.getCell(2).setWidth("4200");
        }
    }



    private void doubleTitle(XWPFTable table){
        XWPFTableRow headerRow = table.getRow(0);
        CTTrPr trPr = headerRow.getCtRow().addNewTrPr(); // 获取或创建CTTrPr
        // 设置标题行重复属性
        trPr.addNewTblHeader().setVal(STOnOff.Enum.forString("1"));
    }


    private void doubleTitle3(XWPFTable table){
        // 为前三行设置重复头部属性
        for (int i = 0; i < 3; i++) {
            XWPFTableRow headerRow = table.getRow(i);
            CTTrPr trPr = headerRow.getCtRow().addNewTrPr(); // 获取或创建CTTrPr
            // 设置标题行重复属性
            trPr.addNewTblHeader().setVal(STOnOff.Enum.forString("1"));
        }
    }

    private void initBuildingScoreByCity(XWPFDocument document, List<EvaluateRoadSheetOne> listFour) {
        //建筑控制区得分--建筑用地总分排序 desc
        List<EvaluateRoadSheetThree> listFive = this.dealWithSheetFive(listFour,true);
        if(CollectionUtils.isEmpty(listFive)){
            return;
        }
        //标题
        this.initTitle(document, "各市州得分(" + listFour.get(0).getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 3;
        targetTable = document.createTable(1 + listFive.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("15000");
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, "各高速公路管养单位绿化景观整治提升工作监测得分统计表(" + listFour.get(0).getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"市州");
        setCellTextDefault(row.getCell(2),"得分");

        //设置固定表头参数
        for (int k = 0; k < listFive.size(); k++) {
            EvaluateRoadSheetThree sheet = listFive.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            setCellTextDefault(rowTemp.getCell(1),sheet.getCompanyName());
            setCellTextDefault(rowTemp.getCell(2),String.valueOf(sheet.getScore()));
        }
    }

    private List<EvaluateRoadSheetOne> packageBuildingScore(XWPFDocument document, TaskEvaluate taskEvaluate) {
        //根据线路和检测起点和终点查询对应检测线路
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectByCheckCity(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            return listOne;
        }
        //查询当前任务路线对应检测出来的服务区
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            //处理得分汇总数据
            this.dealWithSheetOneWord(sheetOne, taskEvaluate, true);
            if(StringUtils.isEmpty(sheetOne.getCity())){
                sheetOne.setCity("-");
            }
        }
        //建筑控制区得分--建筑用地总分排序 desc
        listOne=listOne.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getBuildingTotalScore).reversed()).collect(Collectors.toList());
        List<EvaluateRoadSheetOne> listTemp=listOne.stream().filter(v->new BigDecimal(100).compareTo(v.getBuildingTotalScore())!=0).collect(Collectors.toList());

        this.initTitle(document, "建筑控制区病害数量统计(" + listOne.get(0).getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 15;
        targetTable = document.createTable(1 + listTemp.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("15000");
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, "各高速公路管养单位绿化景观整治提升工作监测病害数量统计表(" + listOne.get(0).getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"路线编号");
        setCellTextDefault(row.getCell(2),"路线名称");
        setCellTextDefault(row.getCell(3),"路段起点桩号");
        setCellTextDefault(row.getCell(4),"路段终点桩号");
        setCellTextDefault(row.getCell(5),"里程");
        setCellTextDefault(row.getCell(6),"所在县市区");
        setCellTextDefault(row.getCell(7),"检查起点");
        setCellTextDefault(row.getCell(8),"检查终点");
        setCellTextDefault(row.getCell(9),"清理建筑控制区范围内杂物,白色垃圾等");
        setCellTextDefault(row.getCell(10),"加强树木维护，管养，适时修剪");
        setCellTextDefault(row.getCell(11),"无乱搭乱建临时棚屋，活动板房，栅栏等建(构)筑物");
        setCellTextDefault(row.getCell(12),"结合实际对破损，废弃建(构)筑物采取遮挡，粉刷或拆除等措施");
        setCellTextDefault(row.getCell(13),"生态环境保护");
        setCellTextDefault(row.getCell(14),"合计");
        if(!CollectionUtils.isEmpty(listTemp)){
            //设置固定表头参数
            for (int k = 0; k < listTemp.size(); k++) {
                EvaluateRoadSheetOne sheet = listTemp.get(k);
                XWPFTableRow rowTemp = targetTable.getRow(k + 1);
                this.initRowWidthAndHeightPic(rowTemp, col);
                setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
                setCellTextDefault(rowTemp.getCell(1),sheet.getRoadCode());
                setCellTextDefault(rowTemp.getCell(2),sheet.getRoadName());
                setCellTextDefault(rowTemp.getCell(3),sheet.getStartCode());
                setCellTextDefault(rowTemp.getCell(4),sheet.getEndCode());
                setCellTextDefault(rowTemp.getCell(5),String.valueOf(sheet.getMileage()));
                setCellTextDefault(rowTemp.getCell(6),sheet.getCity());
                setCellTextDefault(rowTemp.getCell(7),sheet.getStartCheckCode());
                setCellTextDefault(rowTemp.getCell(8),sheet.getEndCheckCode());
                setCellTextDefault(rowTemp.getCell(9),String.valueOf(sheet.getBuildingOneCount()));
                setCellTextDefault(rowTemp.getCell(10),String.valueOf(sheet.getBuildingTwoCount()));
                setCellTextDefault(rowTemp.getCell(11),String.valueOf(sheet.getBuildingThreeCount()));
                setCellTextDefault(rowTemp.getCell(12),String.valueOf(sheet.getBuildingFourCount()));
                setCellTextDefault(rowTemp.getCell(13),String.valueOf(sheet.getBuildingFiveCount()));
                setCellTextDefault(rowTemp.getCell(14),String.valueOf(sheet.getBuildingTotalCount()));
            }
        }
        this.addRemark(document.createParagraph(), "备注：其他路段均为满分");
        return listOne;
    }

    /**
     * 各高速公路管养单位得分
     *
     * @param document
     */
    private void packageRoadByCompanyName(XWPFDocument document, List<EvaluateRoadSheetOne> listOne) {
        //管养单位
        List<EvaluateRoadSheetThree> listThree = this.dealWithSheetThreeCount(listOne,false);
        //标题
        this.initTitle(document, "各高速公路管养单位病害数量统计(" + listOne.get(0).getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 5;
        targetTable = document.createTable(1 + listThree.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("15000");
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, "各高速公路管养单位绿化景观整治提升工作监测病害数量统计表(" + listOne.get(0).getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"单位");
        setCellTextDefault(row.getCell(2),"管辖路段数量");
        setCellTextDefault(row.getCell(3),"检测路段数量");
        setCellTextDefault(row.getCell(4),"病害总数");
        //设置固定表头参数
        for (int k = 0; k < listThree.size(); k++) {
            EvaluateRoadSheetThree sheet = listThree.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            setCellTextDefault(rowTemp.getCell(1),sheet.getCompanyName());
            setCellTextDefault(rowTemp.getCell(2),String.valueOf(sheet.getManageCount()));
            setCellTextDefault(rowTemp.getCell(3),String.valueOf(sheet.getCheckRoadCount()));
            setCellTextDefault(rowTemp.getCell(4),String.valueOf(sheet.getCount()));
        }
    }


    private void initWord(HttpServletResponse response, XWPFDocument document) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        document.write(baos);
        document.close();
        // 获取字节数组
        byte[] docBytes = baos.toByteArray();
        int length = docBytes.length;
        // 设置响应头的内容长度
        response.setContentLengthLong(length);
        // 获取输出流
        OutputStream out = response.getOutputStream();
        // 分块发送
        int chunkSize = 1024; // 每次发送1KB
        for (int p = 0; p < length; p += chunkSize) {
            if (p + chunkSize > length) {
                chunkSize = length - p;
            }
            out.write(docBytes, p, chunkSize);
            out.flush();
        }
        out.close();
    }


    /**
     * 监测路段信息（×月）
     *
     * @param taskEvaluate
     */
    private void packageCheckRoadScore(XWPFDocument document, TaskEvaluate taskEvaluate) {
        taskEvaluate.setTaskId(null);
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectByCheckRoadScore(taskEvaluate);
        //查询当前任务路线对应检测出来的服务区
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            //处理得分汇总数据
            this.dealWithSheetOneWord(sheetOne, taskEvaluate, true);
        }
        //路段得分排名--使用公路用地总分排序 desc
        listOne=listOne.stream().sorted(Comparator.comparing(EvaluateRoadSheetOne::getRoadTotalScore).reversed()).collect(Collectors.toList());
        //标题
        this.initTitle(document, "各高速公路监测路段病害数量统计(" + taskEvaluate.getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 15;
        XWPFTable targetTable = document.createTable(1 + listOne.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("15000");
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, "各高速公路监测路段绿化景观整治提升工作监测病害数量统计表(" + taskEvaluate.getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"路线编号");
        setCellTextDefault(row.getCell(2),"路线名称");
        setCellTextDefault(row.getCell(3),"管养单位");
        setCellTextDefault(row.getCell(4),"路段起点");
        setCellTextDefault(row.getCell(5),"路段终点");
        setCellTextDefault(row.getCell(6),"里程");
        setCellTextDefault(row.getCell(7),"中央分隔带");
        setCellTextDefault(row.getCell(8),"路侧边坡");
        setCellTextDefault(row.getCell(9),"互通区及收费站");
        setCellTextDefault(row.getCell(10),"服务区");
        setCellTextDefault(row.getCell(11),"隧道出入口");
        setCellTextDefault(row.getCell(12),"桥梁及桥下空间");
        setCellTextDefault(row.getCell(13),"路基路面及交安设施");
        setCellTextDefault(row.getCell(14),"合计");

        //设置固定表头参数
        for (int k = 0; k < listOne.size(); k++) {
            EvaluateRoadSheetOne sheet = listOne.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            setCellTextDefault(rowTemp.getCell(1),sheet.getRoadCode());
            setCellTextDefault(rowTemp.getCell(2),sheet.getRoadName());
            setCellTextDefault(rowTemp.getCell(3),sheet.getCompanyName());
            setCellTextDefault(rowTemp.getCell(4),sheet.getStartCode());
            setCellTextDefault(rowTemp.getCell(5),sheet.getEndCode());
            setCellTextDefault(rowTemp.getCell(6),String.valueOf(sheet.getMileage()));
            setCellTextDefault(rowTemp.getCell(7),String.valueOf(sheet.getCenterDivideCount()));
            setCellTextDefault(rowTemp.getCell(8),String.valueOf(sheet.getRoadSideCount()));
            setCellTextDefault(rowTemp.getCell(9),String.valueOf(sheet.getFeeAreaCount()));
            setCellTextDefault(rowTemp.getCell(10),String.valueOf(sheet.getServiceAreaCount()));
            setCellTextDefault(rowTemp.getCell(11),String.valueOf(sheet.getTunnelCount()));
            setCellTextDefault(rowTemp.getCell(12),String.valueOf(sheet.getBridgeCount()));
            setCellTextDefault(rowTemp.getCell(13),String.valueOf(sheet.getRoadUpCount()));
            setCellTextDefault(rowTemp.getCell(14),String.valueOf(sheet.getRoadTotalCount()));
        }
    }


    /**
     * 监测路段信息（×月）
     *
     * @param taskEvaluate
     */
    private List<EvaluateRoadSheetOne> packageCheckRoad(XWPFDocument document, TaskEvaluate taskEvaluate) {
        // 设置页面宽度和边距
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectByParams(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            throw new ServiceException("未查询到检测数据");
        }
        //查询当前任务路线对应检测出来的服务区
        List<EvaluateRoadSheetOne> baseInfoList = this.listBaseInfoByTaskIdList(listOne,taskEvaluate.getType());
        //'服务区','隧道出入口','互通区及收费站'
        Map<String, String> serviceAreaMap = baseInfoList.stream().filter(v -> Objects.equals("服务区", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection() + "_" + v.getStartCheckCode() + "_" + v.getEndCheckCode(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> tunnelMap = baseInfoList.stream().filter(v -> Objects.equals("隧道出入口", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection() + "_" + v.getStartCheckCode() + "_" + v.getEndCheckCode(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        Map<String, String> feeAreaMap = baseInfoList.stream().filter(v -> Objects.equals("互通区及收费站", v.getCheckProject())).collect(Collectors.toMap(v -> v.getTaskId() + "_" + v.getDirection() + "_" + v.getStartCheckCode() + "_" + v.getEndCheckCode(), EvaluateRoadSheetOne::getFeeArea, (key1, key2) -> key1));
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            //处理得分汇总数据
            this.dealWithSheetOne(sheetOne, serviceAreaMap, tunnelMap, feeAreaMap, taskEvaluate, true);
        }
        taskEvaluate.setTaskDate(listOne.get(0).getTaskDate());
        //标题
        this.initTitle(document, "监测路段信息(" + listOne.get(0).getTaskDate() + ")");
        //表格
        XWPFParagraph tableParagraph;
        XWPFTable targetTable;
        tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.CENTER);
        int col = 13;
        targetTable = document.createTable(1 + listOne.size(), col);
        targetTable.setTableAlignment(TableRowAlign.CENTER);
        targetTable.setWidth("15000");
        this.doubleTitle(targetTable);
        //设置表头名称
        this.addTableTitle(tableParagraph, "各高速公路绿化景观整治提升工作专项监测外业监测路段信息统计表(" + listOne.get(0).getTaskDate() + ")");
        //初始化表格标题
        XWPFTableRow row = targetTable.getRow(0);
        this.initRowWidthAndHeightPic(row, col);
        setCellTextDefault(row.getCell(0),"序号");
        setCellTextDefault(row.getCell(1),"路线编号");
        setCellTextDefault(row.getCell(2),"路线名称");
        setCellTextDefault(row.getCell(3),"管养单位");
        setCellTextDefault(row.getCell(4),"路段起点");
        setCellTextDefault(row.getCell(5),"路段终点");
        setCellTextDefault(row.getCell(6),"里程");
        setCellTextDefault(row.getCell(7),"检查起点");
        setCellTextDefault(row.getCell(8),"检查终点");
        setCellTextDefault(row.getCell(9),"幅别");
        setCellTextDefault(row.getCell(10),"服务区");
        setCellTextDefault(row.getCell(11),"收费站");
        setCellTextDefault(row.getCell(12),"隧道");

        //设置固定表头参数
        for (int k = 0; k < listOne.size(); k++) {
            EvaluateRoadSheetOne sheet = listOne.get(k);
            XWPFTableRow rowTemp = targetTable.getRow(k + 1);
            this.initRowWidthAndHeightPic(rowTemp, col);
            setCellTextDefault(rowTemp.getCell(0),String.valueOf(k + 1));
            setCellTextDefault(rowTemp.getCell(1),sheet.getRoadCode());
            setCellTextDefault(rowTemp.getCell(2),sheet.getRoadName());
            setCellTextDefault(rowTemp.getCell(3),sheet.getCompanyName());
            setCellTextDefault(rowTemp.getCell(4),sheet.getStartCode());
            setCellTextDefault(rowTemp.getCell(5),sheet.getEndCode());
            setCellTextDefault(rowTemp.getCell(6),String.valueOf(sheet.getMileage()));
            setCellTextDefault(rowTemp.getCell(7),sheet.getStartCheckCode());
            setCellTextDefault(rowTemp.getCell(8),sheet.getEndCheckCode());
            setCellTextDefault(rowTemp.getCell(9),Objects.equals(1, sheet.getDirection()) ? "左幅" : "右幅");
            setCellTextDefault(rowTemp.getCell(10),sheet.getServiceArea());
            setCellTextDefault(rowTemp.getCell(11),sheet.getFeeArea());
            setCellTextDefault(rowTemp.getCell(12),sheet.getTunnel());
        }
        return listOne;
    }


    private void initTitle(XWPFDocument document, String title) {
        XWPFParagraph xWPFParagraph=document.createParagraph();
        xWPFParagraph.setStyle("Heading1");
        xWPFParagraph.setAlignment(ParagraphAlignment.LEFT);
//        // 创建段落的属性
//        CTPPr pPr = xWPFParagraph.getCTP().getPPr();
//        if (pPr == null) {
//            pPr = xWPFParagraph.getCTP().addNewPPr();
//        }
//        // 设置段落样式为 Word 内置的标题样式（例如 "Heading1" 表示一级标题）
//        CTString styleStr = pPr.isSetPStyle() ? pPr.getPStyle() : pPr.addNewPStyle();
//        styleStr.setVal("Heading1");  // "Heading1" 对应 Word 中的一级标题样式，"Heading2" 对应二级标题
        XWPFRun xWPFRun =xWPFParagraph.createRun();
        xWPFRun.setFontSize(20);
        xWPFRun.setBold(true);
        if(Objects.nonNull(title)){
            for (char c : title.toCharArray()) {
                if (isChinese(c)) {
                    xWPFRun.setFontFamily("宋体"); // 设置中文字体
                } else {
                    xWPFRun.setFontFamily("Times New Roman"); // 设置英文和数字字体
                }
                xWPFRun.setText(String.valueOf(c), xWPFRun.getTextPosition()); // 逐字符写入
            }
        }
        // 设置大纲级别
//        CTPPr pPr = xWPFParagraph.getCTP().getPPr(); // 段落的属性
//        if (pPr == null) {
//            pPr = xWPFParagraph.getCTP().addNewPPr();
//        }
//
//        // 设置目录级别
//        CTOutlineLvl outlineLvl = pPr.addNewOutlineLvl();
//        outlineLvl.setVal(CTDecimalNumber.Factory.newInstance());
//        outlineLvl.getVal().setBigIntegerValue(java.math.BigInteger.valueOf(0));
    }

    /**
     * 设置单元格内的内容垂直和水平居中
     *
     * @param row
     */
    private void initRowWidthAndHeight(XWPFTableRow row) {
        for (int i = 0; i < 9; i++) {
            XWPFTableCell cell = row.getCell(i);
            cell.getParagraphs().get(0).setAlignment(ParagraphAlignment.CENTER);
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            if (i == 0) {
                cell.setWidth("800");
            } else if (i == 1) {
                cell.setWidth("800");
            } else if (i == 2 || i == 3) {
                cell.setWidth("800");
            } else if (i == 4 || i == 5) {
                cell.setWidth("1200");
            } else if (i == 6) {
                cell.setWidth("800");
            } else if (i == 7) {
                cell.setWidth("1800");
            }else if (i == 8) {
                cell.setWidth("500");
            }
        }
    }


    /**
     * 设置单元格内的内容垂直和水平居中
     *
     * @param row
     */
    private void initRowWidthAndHeightPic(XWPFTableRow row, int col) {
        for (int i = 0; i < col; i++) {
            XWPFTableCell cell = row.getCell(i);
            cell.getParagraphs().get(0).setAlignment(ParagraphAlignment.CENTER);
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            cell.setWidth("1200");
        }
    }


    private void dealWithTable(Map.Entry<String, List<EvaluateRoadSheetOne>> entry, XWPFTable targetTable) throws IOException, InvalidFormatException {
        //设置动态表头参数
        for (int t = 0; t < entry.getValue().size(); t++) {
            EvaluateRoadSheetOne sheetOne = entry.getValue().get(t);
            XWPFTableRow row = targetTable.getRow(t + 3);
            this.initRowWidthAndHeight(row);
            setCellTextDefault(row.getCell(0),sheetOne.getCheckProject());
            setCellTextDefault(row.getCell(1),this.formatCheckCode(sheetOne.getCheckCode()) + sheetOne.getQuestionType());
            row.getCell(2).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            row.getCell(3).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            row.getCell(4).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            this.insertWordImage(row.getCell(2), sheetOne.getScenePic1(),150,120);
            setCellTextDefault(row.getCell(5),cn.hutool.core.date.DateUtil.format(sheetOne.getCreateTime(), "yyyy-MM-dd"));
            row.getCell(6).setText("");
            row.getCell(7).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            row.getCell(8).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
        }
    }


    private void insertWordImage(XWPFTableCell cell, String imgUrl ,int width,int height) throws IOException, InvalidFormatException {
        InputStream inputStream = null;
        BufferedImage originalImage = null;
        BufferedImage compressedImage = null;
        Graphics graphics = null;
        ByteArrayOutputStream baos = null;
        InputStream picStream = null;
        
        try{
            // 下载图片
            imgUrl=this.replaceImageUrl(imgUrl);
            if(StringUtils.isEmpty(imgUrl)){
                return;
            }
            // 读取图片
            inputStream = new URL(imgUrl).openStream();
            originalImage = ImageIO.read(inputStream);
            
            // 压缩图片
            compressedImage = new BufferedImage(
                    originalImage.getWidth() / 1,
                    originalImage.getHeight() / 1,
                    originalImage.getType()
            );
            graphics = compressedImage.getGraphics();
            graphics.drawImage(originalImage, 0, 0, compressedImage.getWidth(), compressedImage.getHeight(), null);
            
            // 将压缩后的图片写入字节数组输出流
            baos = new ByteArrayOutputStream();
            ImageIO.write(compressedImage, "jpg", baos);
            baos.flush();
            byte[] imageBytes = baos.toByteArray();
            
            // 插入图片
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            XWPFRun run = paragraph.createRun();
            picStream = new ByteArrayInputStream(imageBytes);
            run.addPicture(picStream, Document.PICTURE_TYPE_JPEG, imgUrl, Units.toEMU(width), Units.toEMU(height));
            
        }catch (Exception e){
            log.error("操作图片异常", e);
        } finally {
            // 确保所有资源都被正确释放
            if (picStream != null) {
                try {
                    picStream.close();
                } catch (IOException e) {
                    log.error("关闭picStream异常", e);
                }
            }
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    log.error("关闭baos异常", e);
                }
            }
            if (graphics != null) {
                graphics.dispose();
            }
            if (compressedImage != null) {
                compressedImage.flush();
            }
            if (originalImage != null) {
                originalImage.flush();
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭inputStream异常", e);
                }
            }
        }

    }

    /**
     * 将图片地址替换为内网地址
     * @param imgUrl
     * @return
     */
    private String replaceImageUrl(String imgUrl) {
        if(StringUtils.isEmpty(imgUrl)){
            return "";
        }
        if (Objects.equals( "pro", active)) {
            imgUrl = imgUrl.replace("shanghai", "shanghai-internal");
        }
        return imgUrl;
    }

    private XWPFRun addTableTitle(XWPFParagraph titleParagraph, String title) {
        XWPFRun titleRun = titleParagraph.createRun();
        //标题居中
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        titleRun.setBold(true); // 标题加粗
        titleRun.setFontSize(16);
        setCellText(titleRun,title,"宋体","Times New Roman");
        return titleRun;
    }


    private XWPFRun addRemark(XWPFParagraph titleParagraph, String title) {
        XWPFRun titleRun = titleParagraph.createRun();
        //标题居中
        titleRun.setFontSize(14);
        setCellText(titleRun,title,"宋体","Times New Roman");
        return titleRun;
    }


    // 根据字符类型设置字体
    private static void setCellText(XWPFRun run, String text, String chineseFont, String englishFont) {
        if(Objects.nonNull(text.toCharArray())){
            for (char c : text.toCharArray()) {
                if (isChinese(c)) {
                    run.setFontFamily(chineseFont); // 设置中文字体
                } else {
                    run.setFontFamily(englishFont); // 设置英文和数字字体
                }
                run.setText(String.valueOf(c), run.getTextPosition()); // 逐字符写入
            }
        }
    }

    // 判断字符是否为中文
    private static boolean isChinese(char c) {
        return String.valueOf(c).matches("[\u4e00-\u9fa5]");
    }

    /**
     * 典型病害照片
     *
     * @param response
     * @param taskEvaluate
     */
    private File exportFocusPicEnv(HttpServletResponse response, TaskEvaluate taskEvaluate,Integer type) throws IOException {
        File file = null;
        XSSFWorkbook workbook = null;
        
        try {
            //获取模板
            workbook = this.initExcelTemplate(response, "pictureListEnv");
            taskEvaluate.setIsFocus(type);
            List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectFocusPicEnv(taskEvaluate);
            if (CollectionUtils.isEmpty(listOne)){
                return file;
            }
            //导出典型病害及图片
            this.initExcelSheetFocusPicEnv(listOne, workbook, workbook.getSheetAt(0), 6, 2);
            String fileName="典型问题照片-";
            if(Objects.equals(type,1)){
                fileName="典型问题照片-";
            }
            if (taskEvaluate.isInitFile()) {
                file = this.initExcelPath(workbook, fileName+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
            } else {
                try (OutputStream outputStream = response.getOutputStream()) {
                    workbook.write(outputStream);
                    outputStream.flush();
                } catch (Exception e) {
                    log.error("导出Excel异常{}", e.getMessage(), e);
                    throw new IOException("导出Excel失败", e);
                }
            }
        } finally {
            // 确保workbook资源被释放
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook异常", e);
                }
            }
            // 强制垃圾回收
//            System.gc();
        }
        return file;
    }




    /**
     * 典型病害照片
     *
     * @param response
     * @param taskEvaluate
     */
    private File exportFocusPicServiceArea(HttpServletResponse response, TaskEvaluate taskEvaluate,Integer type) throws IOException {
        File file = null;
        //获取模板
        XSSFWorkbook workbook = this.initExcelTemplate(response, "pictureListEnv");
        taskEvaluate.setIsFocus(type);
        //先查询交投实业的公司
        List<Road> roadList=taskEvaluateMapper.selectSpecialCompanyList(taskEvaluate);
        if(CollectionUtils.isEmpty(roadList)){
            throw new ServiceException("未查询到交投实业的公司");
        }
        Map<String,String> matchCompanyMap=roadList.stream().collect(Collectors.toMap(Road::getCompanyName, Road::getMatchCompanyName, (key1, key2) -> key1));
        List<Long> roadIdList = roadList.stream().map(Road::getId).collect(Collectors.toList());
        taskEvaluate.setRoadIdList(roadIdList);
        taskEvaluate.setCheckProject("服务区");
        List<EvaluateRoadSheetOne> listOne = taskEvaluateMapper.selectFocusPicEnv(taskEvaluate);
        if(CollectionUtils.isEmpty(listOne)){
            throw new ServiceException("未查询到交投实业的公司对应的服务区数据");
        }
        for (EvaluateRoadSheetOne sheetOne : listOne) {
            if(!matchCompanyMap.containsKey(sheetOne.getCompanyName())){
                continue;
            }
            sheetOne.setCompanyName(matchCompanyMap.get(sheetOne.getCompanyName()));
        }
        if (CollectionUtils.isEmpty(listOne)){
            return null;
        }
        //导出典型病害及图片
        this.initExcelSheetFocusPicEnv(listOne, workbook, workbook.getSheetAt(0), 6, 2);
        String fileName="典型问题照片-";
        if(Objects.equals(type,1)){
            fileName="典型问题照片-";
        }
        if (taskEvaluate.isInitFile()) {
            file = this.initExcelPath(workbook, fileName+this.yearMonthDate(taskEvaluate.getTaskDate()), taskEvaluate.getSourcePath());
        } else {
            try {
                workbook.write(response.getOutputStream());
            } catch (Exception e) {
                log.error("导出Excel异常{}", e.getMessage());
            } finally {
                IOUtils.closeQuietly(workbook);
            }
        }
        return file;
    }



    private void initExcelSheetFocusPicEnv(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, XSSFSheet sheet, int totalCol, int firstDataRow) throws IOException {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        sheet.setColumnWidth(4, 30 * 256);
        int k=1;
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(String.valueOf(k));
            row.getCell(1).setCellValue(base.getCompanyName());
            String checkCode=this.formatCheckCode(base.getCheckCode());
            if(base.getCheckProject().contains("收费站") || base.getCheckProject().contains("服务区")){
                checkCode=base.getName();
            }
            String questionRoad = base.getRoadCode() + base.getRoadName() + (Objects.equals(1, base.getDirection()) ? "左幅" : "右幅") + checkCode + "处";
            if(!StringUtils.isEmpty(base.getRemark())){
                questionRoad+="("+base.getRemark()+")";
            }
            row.getCell(2).setCellValue(questionRoad);
            row.getCell(3).setCellValue(base.getQuestionType());
            // 单元格高度为单位20，400为高度值
            row.setHeightInPoints(100);
            try{
                insertImage(workbook, sheet, base.getScenePic1(), j, 4);
            }catch (Exception e){
                log.error("插入图片异常{}", e.getMessage());
            }
            row.getCell(5).setCellValue("安环院");
            k++;
        }
        workbook.setForceFormulaRecalculation(true);
    }

    @Override
    public void insertExcelImage(Workbook workbook, Sheet sheet, String imageUrl, int rowNum, int colNum) {
        try {
            this.insertImage(workbook, sheet, imageUrl, rowNum, colNum);
        } catch (IOException e) {
            log.error("插入图片异常：{}", e.getMessage());
        }
    }


    @Override
    public File initExcelPathFun(XSSFWorkbook workbook, String fileName, String sourcePath) {
        File file = new File(fileName);
        try {
            file =  this.initExcelPath(workbook, fileName, sourcePath);
        } catch (IOException e) {
            log.error("初始化导出excel路径异常：{}", e.getMessage());
        }
        return file;
    }

    /**
     * excel中插入图片
     *
     * @param workbook
     * @param sheet
     * @param imageUrl
     * @param rowNum
     * @param colNum
     * @throws IOException
     */
    private void insertImage(Workbook workbook, Sheet sheet, String imageUrl, int rowNum, int colNum) throws IOException {
        imageUrl=this.replaceImageUrl(imageUrl);
        if(StringUtils.isEmpty(imageUrl)){
            return;
        }
        
        BufferedImage originalImage = null;
        BufferedImage resizedImage = null;
        Graphics2D g = null;
        
        try {
            URL url = new URL(imageUrl);
            // 使用 try-with-resources 确保流正确关闭
            try (InputStream inputStream = url.openStream();
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                
                IOUtils.copy(inputStream, byteArrayOutputStream);
                
                // 读取图片
                try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray())) {
                    originalImage = ImageIO.read(byteArrayInputStream);
                    
                    if (originalImage == null) {
                        log.warn("无法读取图片: {}", imageUrl);
                        return;
                    }
                    
                    // 调整图片尺寸
                    int newWidth = originalImage.getWidth();
                    int newHeight = originalImage.getHeight();
                    resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                    g = resizedImage.createGraphics();
                    
                    // 设置高质量渲染
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    
                    g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
                    
                    // 将调整尺寸后的图片写入字节数组
                    try (ByteArrayOutputStream resizedOutputStream = new ByteArrayOutputStream()) {
                        ImageIO.write(resizedImage, "png", resizedOutputStream);
                        byte[] resizedImageBytes = resizedOutputStream.toByteArray();
                        
                        // 将压缩后的图片添加到工作簿中
                        int pictureIdx = workbook.addPicture(resizedImageBytes, Workbook.PICTURE_TYPE_PNG);
                        
                        CreationHelper helper = workbook.getCreationHelper();
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        
                        ClientAnchor anchor = helper.createClientAnchor();
                        anchor.setCol1(colNum);
                        anchor.setRow1(rowNum);
                        anchor.setCol2(colNum + 1);
                        anchor.setRow2(rowNum + 1);
                        
                        drawing.createPicture(anchor, pictureIdx);
                    }
                }
            }
        } catch (Exception e) {
            log.error("插入图片异常: {}, URL: {}", e.getMessage(), imageUrl, e);
            throw new IOException("插入图片失败: " + e.getMessage(), e);
        } finally {
            // 显式释放图片资源
            if (g != null) {
                g.dispose();
            }
            if (originalImage != null) {
                originalImage.flush();
            }
            if (resizedImage != null) {
                resizedImage.flush();
            }
            // 建议垃圾回收
//            System.gc();
        }
    }

    private String initItems(Map.Entry<String, List<EvaluateRoadSheetOne>> entry, String filterKey) {
        StringBuilder roadMaintenanceBuilder = new StringBuilder();
        List<EvaluateRoadSheetOne> roadMaintenanceList = entry.getValue().stream().filter(v -> Objects.equals(filterKey, v.getCheckProject())).collect(Collectors.toList());
        Map<String, List<String>> roadMaintenanceMap = Maps.newLinkedHashMap();
        //照片Map
        Map<String, List<String>> picMap = Maps.newLinkedHashMap();
        for (EvaluateRoadSheetOne sheetOne : roadMaintenanceList) {
            String key =sheetOne.getCheckProject() + "_" + sheetOne.getQuestionType();
            if (!roadMaintenanceMap.containsKey(key)) {
                roadMaintenanceMap.put(key, Lists.newArrayList(sheetOne.getCheckCode()));
            } else {
                roadMaintenanceMap.get(key).add(sheetOne.getCheckCode());
            }
            if (!picMap.containsKey(key)) {
                picMap.put(key, Lists.newArrayList(sheetOne.getScenePic1()));
            } else {
                picMap.get(key).add(sheetOne.getScenePic1());
            }
        }
        int i = 1;
        for (Map.Entry<String, List<String>> entryOne : roadMaintenanceMap.entrySet()) {
            List<String> checkCodeList = Lists.newArrayList();
            for (String checkCode : entryOne.getValue()) {
                checkCodeList.add(this.formatCheckCode(checkCode));
            }
            roadMaintenanceBuilder.append(i).append("、")
                    .append(StringUtils.join(checkCodeList, "、"))
                    .append(entryOne.getKey().split("_")[1]);

            List<String> picUrlList = Lists.newArrayList();
            if (picMap.containsKey(entryOne.getKey())) {
                List<String> picList = picMap.get(entryOne.getKey());
                for (String picUrl : picList) {
                    if (!StringUtils.isEmpty(picUrl) && picUrl.indexOf("_") > -1) {
                        picUrlList.add(this.dealPoint(picUrl.split("_")[1]));
                    }
                }
            }
            if (!CollectionUtils.isEmpty(picUrlList)) {
                roadMaintenanceBuilder.append(",照片编号依次为：").append(StringUtils.join(picUrlList, "、")).append("\n");
            }
            roadMaintenanceBuilder.append("\n");
            i++;
        }
        return roadMaintenanceBuilder.toString();
    }

    private String dealPoint(String input) {
        // 寻找小数点的位置
        int dotIndex = input.indexOf('.');
        // 截取小数点之前的内容
        String result;
        if (dotIndex != -1) {
            result = input.substring(0, dotIndex);
        } else {
            result = input;  // 如果没有小数点，则返回原始字符串
        }
        return result;
    }

    private String formatCheckCode(String checkCode) {
        if(StringUtils.isEmpty(checkCode)){
            return "";
        }
        return "K" + checkCode.replace(".", "+");
    }


    /**
     * 处理管养单位数据
     *
     * @param listOne
     * @return
     */
    private List<EvaluateRoadSheetThree> dealWithSheetFive(List<EvaluateRoadSheetOne> listOne,boolean isCity) {
        Map<String, List<EvaluateRoadSheetOne>> cityMap;
        if(isCity){
            cityMap = listOne.stream().collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCity));
        }else{
            cityMap = listOne.stream().collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCompanyName));
        }
        List<EvaluateRoadSheetThree> listThree = Lists.newArrayList();
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : cityMap.entrySet()) {
            BigDecimal score = BigDecimal.ZERO;
            for (EvaluateRoadSheetOne sheetOne : entry.getValue()) {
                score = score.add(sheetOne.getBuildingTotalScore());
            }
            EvaluateRoadSheetThree evaluateRoadSheetThree = new EvaluateRoadSheetThree();
            evaluateRoadSheetThree.setCompanyName(entry.getKey());
            //路段分的平均分
            evaluateRoadSheetThree.setScore(score.divide(new BigDecimal(entry.getValue().size()), 2, BigDecimal.ROUND_HALF_UP));
            listThree.add(evaluateRoadSheetThree);
        }
        listThree=listThree.stream().sorted(Comparator.comparing(EvaluateRoadSheetThree::getScore).reversed()).collect(Collectors.toList());
        return listThree;
    }

    /**
     * 处理管养单位数据
     *
     * @param listOne
     * @return
     */
    private List<EvaluateRoadSheetThree> dealWithSheetThree(List<EvaluateRoadSheetOne> listOne) {
        Map<String, List<EvaluateRoadSheetOne>> companyMap = listOne.stream().collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCompanyName));
        List<EvaluateRoadSheetThree> listThree = Lists.newArrayList();
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : companyMap.entrySet()) {
            BigDecimal totalScore = BigDecimal.ZERO;
            for (EvaluateRoadSheetOne sheetOne : entry.getValue()) {
                totalScore = totalScore.add(sheetOne.getRoadTotalScore());
            }
            EvaluateRoadSheetThree evaluateRoadSheetThree = new EvaluateRoadSheetThree();
            evaluateRoadSheetThree.setCompanyName(entry.getKey());
            //路段分的平均分
            evaluateRoadSheetThree.setScore(totalScore.divide(new BigDecimal(entry.getValue().size()), 2, BigDecimal.ROUND_HALF_UP));
            listThree.add(evaluateRoadSheetThree);
        }
        listThree=listThree.stream().sorted(Comparator.comparing(EvaluateRoadSheetThree::getScore).reversed()).collect(Collectors.toList());
        return listThree;
    }


    public List<EvaluateRoadSheetThree> getSortCompanyList(){
        List<EvaluateRoadSheetThree> resultList=Lists.newArrayList();
        List<String> companylist=Lists.newArrayList("湖北交投鄂东高速公路运营管理有限公司","湖北交投鄂西高速公路运营管理有限公司","湖北交投鄂西北高速公路运营管理有限公司","湖北交投江汉高速公路运营管理有限公司","湖北交投襄阳高速公路运营管理有限公司","湖北交投京珠高速公路运营管理有限公司","湖北交投随岳高速公路运营管理有限公司","湖北交投武黄高速公路运营管理有限公司","湖北交投宜昌高速公路运营管理有限公司","湖北楚天智能交通股份有限公司","湖北联合交通投资开发有限公司","中交投资（湖北）运营管理有限公司","中交资产管理有限公司湖北区域管理总部","湖北武荆高速公路发展有限公司","武汉交投高速公路运营管理有限公司","湖北武麻高速公路有限公司","越秀（湖北）高速公路有限公司","湖北随岳南高速公路有限公司","葛洲坝湖北襄荆高速公路有限公司","湖北荆宜高速公路有限公司","荆州市路桥投资开发有限公司","湖北荆东高速公路建设开发有限公司","湖北鄂东长江公路大桥有限公司","湖北汉洪东荆河桥梁建设管理有限公司","湖北汉孝高速公路建设经营有限公司","湖北樊魏高速公路有限公司","华益路桥管理有限公司","湖北老谷高速公路开发有限公司","武汉青山长江大桥建设有限公司","宜昌长江大桥建设营运集团有限公司","湖北黄石武阳高速公路发展有限公司","武汉市武阳高速公路投资管理有限公司");
        List<Integer> manageCountList=Lists.newArrayList(12,8,11,10,10,10,6,8,6,5,9,3,3,1,8,2,3,1,1,2,3,1,2,1,3,1,1,1,1,1,1,1);
        for (int i = 0; i < companylist.size(); i++) {
            EvaluateRoadSheetThree evaluateRoadSheetThree = new EvaluateRoadSheetThree();
            evaluateRoadSheetThree.setCompanyName(companylist.get(i));
            evaluateRoadSheetThree.setManageCount(manageCountList.get(i));
            resultList.add(evaluateRoadSheetThree);
        }
        return resultList;
    }


    /**
     * 处理管养单位数据
     *
     * @param listOne
     * @return
     */
    private List<EvaluateRoadSheetThree> dealWithSheetThreeCount(List<EvaluateRoadSheetOne> listOne,boolean isEnv) {
        List<EvaluateRoadSheetThree> listThree = Lists.newArrayList();
        Map<String, List<EvaluateRoadSheetOne>> companyMap = listOne.stream().collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCompanyName));
        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : companyMap.entrySet()) {
            BigDecimal totalScore = BigDecimal.ZERO;
            int totalCount=0;
            Set<String> roadSet = new HashSet<>();
            for (EvaluateRoadSheetOne sheetOne : entry.getValue()) {
                if(isEnv){
                    totalScore = totalScore.add(sheetOne.getRoadEnvScore());
                    totalCount+=sheetOne.getRoadEnvCount();
                }else{
                    totalScore = totalScore.add(sheetOne.getRoadTotalScore());
                    totalCount+=sheetOne.getRoadTotalCount();
                }
                roadSet.add(sheetOne.getRoadCode());
            }
            EvaluateRoadSheetThree evaluateRoadSheetThree = new EvaluateRoadSheetThree();
            evaluateRoadSheetThree.setCompanyName(entry.getKey());
            evaluateRoadSheetThree.setCheckRoadCount(roadSet.size());
            //路段分的平均分
            evaluateRoadSheetThree.setScore(totalScore.divide(new BigDecimal(entry.getValue().size()), 2, BigDecimal.ROUND_HALF_UP));
            evaluateRoadSheetThree.setCount(totalCount);
            listThree.add(evaluateRoadSheetThree);
        }

        //查询出维护了匹配公司的数据
//        List<Road>  matchCompanyList=taskEvaluateMapper.selectSpecialCompanyList(new TaskEvaluate());
//        Map<String,String> matchCompanyMap=matchCompanyList.stream().collect(Collectors.toMap(Road::getCompanyName, Road::getMatchCompanyName, (key1, key2) -> key1));
//        //把listOne深拷贝一份,新的集合
//        List<EvaluateRoadSheetOne> serviceAreaList=JSON.parseArray(JSON.toJSONString(listOne),EvaluateRoadSheetOne.class);
//        Map<String, List<EvaluateRoadSheetOne>> companyServiceAreaMap = serviceAreaList.stream().filter(e -> matchCompanyMap.containsKey(e.getCompanyName())).peek(e -> e.setCompanyName(matchCompanyMap.get(e.getCompanyName()))).collect(Collectors.groupingBy(EvaluateRoadSheetOne::getCompanyName));
//        for (Map.Entry<String, List<EvaluateRoadSheetOne>> entry : companyServiceAreaMap.entrySet()) {
//            //当前公司下,服务区的总分为100
//            BigDecimal totalScore = new BigDecimal(100);
//            int totalCount=0;
//            Set<String> roadSet=new HashSet<>();
//            for (EvaluateRoadSheetOne sheetOne : entry.getValue()) {
//                if(totalScore.compareTo(BigDecimal.ZERO)>0){
//                    totalScore = totalScore.subtract(sheetOne.getUnLimitScore());
//                    totalScore=BigDecimal.ZERO.max(totalScore);
//                }
//                totalCount+=sheetOne.getServiceAreaTempCount();
//                roadSet.add(sheetOne.getRoadCode());
//            }
//            EvaluateRoadSheetThree evaluateRoadSheetThree = new EvaluateRoadSheetThree();
//            evaluateRoadSheetThree.setCompanyName(entry.getKey());
//            evaluateRoadSheetThree.setCheckRoadCount(roadSet.size());
//            //路段分的平均分
//            evaluateRoadSheetThree.setScore(totalScore);
//            evaluateRoadSheetThree.setCount(totalCount);
//            listThree.add(evaluateRoadSheetThree);
//        }

        Map<String,EvaluateRoadSheetThree> map=listThree.stream().collect(Collectors.toMap(EvaluateRoadSheetThree::getCompanyName, Function.identity()));
        //按照固定顺序获取公司和基本信息
        List<EvaluateRoadSheetThree> result=this.getSortCompanyList();
        int totalCount=0;
        int totalManageCount=0;
        int totalCheckRoadCount=0;
        BigDecimal totalScore=BigDecimal.ZERO;
        int j=0;
        for (EvaluateRoadSheetThree temp : result) {
            if(map.containsKey(temp.getCompanyName())){
                temp.setScore(map.get(temp.getCompanyName()).getScore());
                temp.setCount(map.get(temp.getCompanyName()).getCount());
                temp.setCheckRoadCount(map.get(temp.getCompanyName()).getCheckRoadCount());
            }
            if(temp.getScore().compareTo(BigDecimal.ZERO)>0){
                j++;
            }
            totalCount+=temp.getCount();
            totalManageCount+=temp.getManageCount();
            totalScore = totalScore.add(temp.getScore());
            totalCheckRoadCount+=temp.getCheckRoadCount();
        }
        //把交投实业的分数算到合计上


        EvaluateRoadSheetThree evaluateRoadSheetThree=new EvaluateRoadSheetThree();
        if(j>0){
            BigDecimal avg= totalScore.divide(new BigDecimal(j),2, BigDecimal.ROUND_HALF_UP);
            evaluateRoadSheetThree.setScore(avg);
        }else{
            evaluateRoadSheetThree.setScore(BigDecimal.ZERO);
        }
        evaluateRoadSheetThree.setCount(totalCount);
        evaluateRoadSheetThree.setManageCount(totalManageCount);
        evaluateRoadSheetThree.setCheckRoadCount(totalCheckRoadCount);
        evaluateRoadSheetThree.setCompanyName("合计");
        result.add(evaluateRoadSheetThree);
        return result;
    }

    /**
     * 处理得分汇总数据
     *
     * @param sheetOne
     * @param taskEvaluate
     */
    private void dealWithSheetOne(EvaluateRoadSheetOne sheetOne, Map<String, String> serviceAreaMap, Map<String, String> tunnelMap, Map<String, String> feeAreaMap
            , TaskEvaluate taskEvaluate, boolean needScore) {
        String key = sheetOne.getTaskId() + "_" + sheetOne.getDirection();
        //服务区
        if (serviceAreaMap.containsKey(key)) {
            sheetOne.setServiceArea(serviceAreaMap.get(key));
        }else {
            sheetOne.setServiceArea("/");
        }
        //隧道出入口
        if (tunnelMap.containsKey(key)) {
            sheetOne.setTunnel(tunnelMap.get(key));
        }else{
            sheetOne.setTunnel("/");
        }
        //互通区及收费站
        if (feeAreaMap.containsKey(key)) {
            sheetOne.setFeeArea(feeAreaMap.get(key));
        }else{
            sheetOne.setFeeArea("/");
        }
        taskEvaluate.setTaskId(sheetOne.getTaskId());
        if (needScore) {
            List<Map<String, CheckEnum>> checkEnumList = this.initScore(taskEvaluate, sheetOne.getDirection(),null,null);
            if (CollectionUtils.isEmpty(checkEnumList)) {
                return;
            }
            //公路用地分数
            this.initRoadBuilding(checkEnumList, sheetOne);
            //公路用地数量
            this.initRoadBuildingCount(checkEnumList,sheetOne);
            //建筑用地得分
            this.initBuildingLand(checkEnumList, sheetOne);
            //建筑用地数量
            this.initBuildingLandCount(checkEnumList, sheetOne);
            //路域环境检查得分
            this.initRoadEnv(checkEnumList, sheetOne);
            //路域环境检查数量
            this.initRoadEnvCount(checkEnumList, sheetOne);
        }
        taskEvaluate.setTaskId(null);
    }


    /**
     * 处理得分汇总数据
     *
     * @param sheetOne
     * @param taskEvaluate
     */
    private void dealWithSheetOneWord(EvaluateRoadSheetOne sheetOne, TaskEvaluate taskEvaluate, boolean needScore) {
        taskEvaluate.setTaskId(sheetOne.getTaskId());
        if (needScore) {
            List<Map<String, CheckEnum>> checkEnumList = this.initScore(taskEvaluate, sheetOne.getDirection(), sheetOne.getStartCheckCode(), sheetOne.getEndCheckCode());
            if (CollectionUtils.isEmpty(checkEnumList)) {
                return;
            }
            //公路用地得分
            this.initRoadBuilding(checkEnumList, sheetOne);
            //公路用地数量
            this.initRoadBuildingCount(checkEnumList, sheetOne);
            //建筑用地得分
            this.initBuildingLand(checkEnumList, sheetOne);
            //建筑用地数量
            this.initBuildingLandCount(checkEnumList, sheetOne);
            //路域环境检查
            this.initRoadEnv(checkEnumList, sheetOne);
            //路域环境检查数量
            this.initRoadEnvCount(checkEnumList, sheetOne);
        }
        //参数重置
        taskEvaluate.setTaskId(null);
    }


    private void initRoadEnv(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        Map<String, CheckEnum> roadMap = checkEnumList.get(2);
        String baseKey = "路域环境检查/";
        if (roadMap.containsKey(baseKey + "公路设施维护")) {
            sheetOne.setRoadMaintenanceScore(roadMap.get(baseKey + "公路设施维护").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "公路沿线保洁")) {
            sheetOne.setRoadCleanScore(roadMap.get(baseKey + "公路沿线保洁").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "隧道设施管理")) {
            sheetOne.setTunnelManageScore(roadMap.get(baseKey + "隧道设施管理").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "公路沿线绿化")) {
            sheetOne.setRoadGreenScore(roadMap.get(baseKey + "公路沿线绿化").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "标志标线管理")) {
            sheetOne.setTagManageScore(roadMap.get(baseKey + "标志标线管理").getRealScore());
        }
        sheetOne.setRoadEnvScore(sheetOne.getRoadMaintenanceScore()
                .add(sheetOne.getRoadCleanScore())
                .add(sheetOne.getTunnelManageScore())
                .add(sheetOne.getRoadGreenScore())
                .add(sheetOne.getTagManageScore())
        );
    }


    private void initRoadEnvCount(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        Map<String, CheckEnum> roadMap = checkEnumList.get(2);
        String baseKey = "路域环境检查/";
        if (roadMap.containsKey(baseKey + "公路设施维护")) {
            sheetOne.setRoadMaintenanceCount(roadMap.get(baseKey + "公路设施维护").getCount());
        }
        if (roadMap.containsKey(baseKey + "公路沿线保洁")) {
            sheetOne.setRoadCleanCount(roadMap.get(baseKey + "公路沿线保洁").getCount());
        }
        if (roadMap.containsKey(baseKey + "隧道设施管理")) {
            sheetOne.setTunnelManageCount(roadMap.get(baseKey + "隧道设施管理").getCount());
        }
        if (roadMap.containsKey(baseKey + "公路沿线绿化")) {
            sheetOne.setRoadGreenCount(roadMap.get(baseKey + "公路沿线绿化").getCount());
        }
        if (roadMap.containsKey(baseKey + "标志标线管理")) {
            sheetOne.setTagManageCount(roadMap.get(baseKey + "标志标线管理").getCount());
        }
        sheetOne.setRoadEnvCount(sheetOne.getRoadMaintenanceCount()
                +(sheetOne.getRoadCleanCount())
                +(sheetOne.getTunnelManageCount())
                +(sheetOne.getRoadGreenCount())
                +(sheetOne.getTagManageCount())
        );
    }


    private void initRoadBuilding(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        if (CollectionUtils.isEmpty(checkEnumList)) {
            return;
        }
        Map<String, CheckEnum> roadMap = checkEnumList.get(0);
        String baseKey = "公路用地/";
        if (roadMap.containsKey(baseKey + "中央分隔带")) {
            sheetOne.setCenterDivideScore(roadMap.get(baseKey + "中央分隔带").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "路侧边坡")) {
            sheetOne.setRoadSideScore(roadMap.get(baseKey + "路侧边坡").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "互通区及收费站")) {
            sheetOne.setFeeAreaScore(roadMap.get(baseKey + "互通区及收费站").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "服务区")) {
            sheetOne.setServiceAreaScore(roadMap.get(baseKey + "服务区").getLimitScore());
            sheetOne.setUnLimitScore(roadMap.get(baseKey + "服务区").getUnLimitScore());
        }
        if (roadMap.containsKey(baseKey + "隧道出入口")) {
            sheetOne.setTunnelScore(roadMap.get(baseKey + "隧道出入口").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "桥梁及桥下空间")) {
            sheetOne.setBridgeScore(roadMap.get(baseKey + "桥梁及桥下空间").getRealScore());
        }
        if (roadMap.containsKey(baseKey + "路基路面及交安设施")) {
            sheetOne.setRoadUpScore(roadMap.get(baseKey + "路基路面及交安设施").getRealScore());
        }

        sheetOne.setRoadTotalScore(sheetOne.getCenterDivideScore()
                .add(sheetOne.getRoadSideScore())
                .add(sheetOne.getFeeAreaScore())
                .add(sheetOne.getServiceAreaScore())
                .add(sheetOne.getTunnelScore())
                .add(sheetOne.getBridgeScore())
                .add(sheetOne.getRoadUpScore())
        );
    }

    private void initRoadBuildingCount(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        if (CollectionUtils.isEmpty(checkEnumList)) {
            return;
        }
        Map<String, CheckEnum> roadMap = checkEnumList.get(0);
        String baseKey = "公路用地/";
        if (roadMap.containsKey(baseKey + "中央分隔带")) {
            sheetOne.setCenterDivideCount(roadMap.get(baseKey + "中央分隔带").getCount());
        }
        if (roadMap.containsKey(baseKey + "路侧边坡")) {
            sheetOne.setRoadSideCount(roadMap.get(baseKey + "路侧边坡").getCount());
        }
        if (roadMap.containsKey(baseKey + "互通区及收费站")) {
            sheetOne.setFeeAreaCount(roadMap.get(baseKey + "互通区及收费站").getCount());
        }
        if (roadMap.containsKey(baseKey + "服务区")) {
            sheetOne.setServiceAreaTempCount(roadMap.get(baseKey + "服务区").getCount());
        }
        if (roadMap.containsKey(baseKey + "隧道出入口")) {
            sheetOne.setTunnelCount(roadMap.get(baseKey + "隧道出入口").getCount());
        }
        if (roadMap.containsKey(baseKey + "桥梁及桥下空间")) {
            sheetOne.setBridgeCount(roadMap.get(baseKey + "桥梁及桥下空间").getCount());
        }
        if (roadMap.containsKey(baseKey + "路基路面及交安设施")) {
            sheetOne.setRoadUpCount(roadMap.get(baseKey + "路基路面及交安设施").getCount());
        }
        sheetOne.setRoadTotalCount(sheetOne.getCenterDivideCount()
                +(sheetOne.getRoadSideCount())
                +(sheetOne.getFeeAreaCount())
                +(sheetOne.getServiceAreaCount())
                +(sheetOne.getTunnelCount())
                +(sheetOne.getBridgeCount())
                +(sheetOne.getRoadUpCount())
        );
    }


    private List<EvaluateRoadSheetOne> initBuildingLand(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        List<EvaluateRoadSheetOne> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(checkEnumList)){
            return resultList;
        }
        //建筑用地
        Map<String, CheckEnum> buildingMap = checkEnumList.get(1);
        this.fullItem("建筑用地/空(建筑用地)/", buildingMap, sheetOne);
        return resultList;
    }

    private List<EvaluateRoadSheetOne> initBuildingLandCount(List<Map<String, CheckEnum>> checkEnumList, EvaluateRoadSheetOne sheetOne) {
        List<EvaluateRoadSheetOne> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(checkEnumList)){
            return resultList;
        }
        //建筑用地
        Map<String, CheckEnum> buildingMap = checkEnumList.get(1);
        this.fullItemCount("建筑用地/空(建筑用地)/", buildingMap, sheetOne);
        return resultList;
    }


    private void fullItem(String baseKey, Map<String, CheckEnum> buildingMap, EvaluateRoadSheetOne sheetOne) {
        if (buildingMap.containsKey(baseKey + "清理建筑控制区范围内杂物，白色垃圾等")) {
            sheetOne.setBuildingOneScore(buildingMap.get(baseKey + "清理建筑控制区范围内杂物，白色垃圾等").getRealScore());
        }
        if (buildingMap.containsKey(baseKey + "加强树木维护，管养，适时修剪")) {
            sheetOne.setBuildingTwoScore(buildingMap.get(baseKey + "加强树木维护，管养，适时修剪").getRealScore());
        }
        if (buildingMap.containsKey(baseKey + "无乱搭乱建临时棚屋，活动板房，栅栏等建(构)筑物")) {
            sheetOne.setBuildingThreeScore(buildingMap.get(baseKey + "无乱搭乱建临时棚屋，活动板房，栅栏等建(构)筑物").getRealScore());
        }
        if (buildingMap.containsKey(baseKey + "结合实际对破损，废弃建(构)筑物采取遮挡，粉刷或拆除等措施")) {
            sheetOne.setBuildingFourScore(buildingMap.get(baseKey + "结合实际对破损，废弃建(构)筑物采取遮挡，粉刷或拆除等措施").getRealScore());
        }
        if (buildingMap.containsKey(baseKey + "生态环境保护")) {
            sheetOne.setBuildingFiveScore(buildingMap.get(baseKey + "生态环境保护").getRealScore());
        }
        //建筑用地总分
        sheetOne.setBuildingTotalScore(sheetOne.getBuildingOneScore()
                .add(sheetOne.getBuildingTwoScore())
                .add(sheetOne.getBuildingThreeScore())
                .add(sheetOne.getBuildingFourScore()
                .add(sheetOne.getBuildingFiveScore())));
    }




    private void fullItemCount(String baseKey, Map<String, CheckEnum> buildingMap, EvaluateRoadSheetOne sheetOne) {
        if (buildingMap.containsKey(baseKey + "清理建筑控制区范围内杂物，白色垃圾等")) {
            sheetOne.setBuildingOneCount(buildingMap.get(baseKey + "清理建筑控制区范围内杂物，白色垃圾等").getCount());
        }
        if (buildingMap.containsKey(baseKey + "加强树木维护，管养，适时修剪")) {
            sheetOne.setBuildingTwoCount(buildingMap.get(baseKey + "加强树木维护，管养，适时修剪").getCount());
        }
        if (buildingMap.containsKey(baseKey + "无乱搭乱建临时棚屋，活动板房，栅栏等建(构)筑物")) {
            sheetOne.setBuildingThreeCount(buildingMap.get(baseKey + "无乱搭乱建临时棚屋，活动板房，栅栏等建(构)筑物").getCount());
        }
        if (buildingMap.containsKey(baseKey + "结合实际对破损，废弃建(构)筑物采取遮挡，粉刷或拆除等措施")) {
            sheetOne.setBuildingFourCount(buildingMap.get(baseKey + "结合实际对破损，废弃建(构)筑物采取遮挡，粉刷或拆除等措施").getCount());
        }
        if (buildingMap.containsKey(baseKey + "生态环境保护")) {
            sheetOne.setBuildingFiveCount(buildingMap.get(baseKey + "生态环境保护").getCount());
        }
        //建筑用地总分
        sheetOne.setBuildingTotalCount(sheetOne.getBuildingOneCount()
                +(sheetOne.getBuildingTwoCount())
                +(sheetOne.getBuildingThreeCount())
                +(sheetOne.getBuildingFourCount()
                        +(sheetOne.getBuildingFiveCount())));
    }


    private XSSFWorkbook initExcelTemplate(HttpServletResponse response, String templateName) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        
        try {
            //获取模板文件
            String filePathName = "/public/excel/" + templateName + ".xlsx";
            // 使用 try-with-resources 确保流正确关闭
            try (InputStream inputStream = this.getClass().getResourceAsStream(filePathName)) {
                if (inputStream == null) {
                    throw new FileNotFoundException("模板文件未找到: " + filePathName);
                }
                return new XSSFWorkbook(inputStream);
            }
        } catch (FileNotFoundException e) {
            log.error("模板文件未找到: {}", templateName, e);
            throw new RuntimeException("模板文件未找到: " + templateName, e);
        } catch (IOException e) {
            log.error("读取模板文件异常: {}", templateName, e);
            throw new RuntimeException("读取模板文件异常: " + templateName, e);
        }
    }

    private XWPFDocument initWordTemplate(HttpServletResponse response, String templateName) {
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("utf-8");
        File file = null;
        // POI 打开/保存文件
        InputStream inputStream = null;
        XWPFDocument document = null;
        try {
            //获取模板文件
            String filePathName = "/public/word/" + templateName + ".docx";
            inputStream = this.getClass().getResourceAsStream(filePathName);
            document = new XWPFDocument(inputStream);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return document;
    }

    private void initExcelSheetEnvOne(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getStartCheckCode());
            row.getCell(8).setCellValue(base.getEndCheckCode());
            row.getCell(9).setCellValue(Objects.equals(1, base.getDirection()) ? "左幅" : "右幅");
            row.getCell(10).setCellValue(this.fitEmpty(base.getTunnel()));
            row.getCell(11).setCellValue(this.fitEmpty(base.getRoadMaintenance()));
            row.getCell(12).setCellValue(this.fitEmpty(base.getRoadClean()));
            row.getCell(13).setCellValue(this.fitEmpty(base.getTunnelManage()));
            row.getCell(14).setCellValue(this.fitEmpty(base.getRoadGreen()));
            row.getCell(15).setCellValue(this.fitEmpty(base.getTagManage()));
        }
        workbook.setForceFormulaRecalculation(true);
    }

    private String fitEmpty(String res) {
        return StringUtils.isEmpty(res) ? "/" : res;
    }


    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetOne(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getStartCheckCode());
            row.getCell(8).setCellValue(base.getEndCheckCode());
            row.getCell(9).setCellValue(Objects.equals(1, base.getDirection()) ? "左幅" : "右幅");
            row.getCell(10).setCellValue(base.getServiceArea());
            row.getCell(11).setCellValue(base.getFeeArea());
            row.getCell(12).setCellValue(base.getTunnel());
            row.getCell(13).setCellValue(base.getCenterDivideScore().doubleValue());
            row.getCell(14).setCellValue(base.getRoadSideScore().doubleValue());
            row.getCell(15).setCellValue(base.getFeeAreaScore().doubleValue());
            row.getCell(16).setCellValue(base.getServiceAreaScore().doubleValue());
            row.getCell(17).setCellValue(base.getTunnelScore().doubleValue());
            row.getCell(18).setCellValue(base.getBridgeScore().doubleValue());
            row.getCell(19).setCellValue(base.getRoadUpScore().doubleValue());
            row.getCell(20).setCellValue(base.getRoadTotalScore().doubleValue());
//            row.getCell(21).setCellValue(base.getBuildingOneScore().doubleValue());
//            row.getCell(22).setCellValue(base.getBuildingTwoScore().doubleValue());
//            row.getCell(23).setCellValue(base.getBuildingThreeScore().doubleValue());
//            row.getCell(24).setCellValue(base.getBuildingFourScore().doubleValue());
//            row.getCell(25).setCellValue(base.getBuildingFiveScore().doubleValue());
//            row.getCell(26).setCellValue(base.getBuildingTotalScore().doubleValue());
        }
    }


    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetTwo(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow,boolean isCount) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getStartCheckCode());
            row.getCell(8).setCellValue(base.getEndCheckCode());
            row.getCell(9).setCellValue(Objects.equals(1, base.getDirection()) ? "左幅" : "右幅");
            row.getCell(10).setCellValue(base.getServiceArea());
            row.getCell(11).setCellValue(base.getFeeArea());
            row.getCell(12).setCellValue(base.getTunnel());
            row.getCell(13).setCellValue(isCount?base.getCenterDivideCount():base.getCenterDivideScore().doubleValue());
            row.getCell(14).setCellValue(isCount?base.getRoadSideCount():base.getRoadSideScore().doubleValue());
            row.getCell(15).setCellValue(isCount?base.getFeeAreaCount():base.getFeeAreaScore().doubleValue());
            row.getCell(16).setCellValue(isCount?base.getServiceAreaCount():base.getServiceAreaScore().doubleValue());
            row.getCell(17).setCellValue(isCount?base.getTunnelCount():base.getTunnelScore().doubleValue());
            row.getCell(18).setCellValue(isCount?base.getBridgeCount():base.getBridgeScore().doubleValue());
            row.getCell(19).setCellValue(isCount?base.getRoadUpCount():base.getRoadUpScore().doubleValue());
            row.getCell(20).setCellValue(isCount?base.getRoadTotalCount():base.getRoadTotalScore().doubleValue());
        }
        workbook.setForceFormulaRecalculation(true);
    }


    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetTwoEnv(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getStartCheckCode());
            row.getCell(8).setCellValue(base.getEndCheckCode());
            row.getCell(9).setCellValue(Objects.equals(1, base.getDirection()) ? "左幅" : "右幅");
            row.getCell(10).setCellValue(base.getRoadMaintenanceScore().doubleValue());
            row.getCell(11).setCellValue(base.getRoadCleanScore().doubleValue());
            row.getCell(12).setCellValue(base.getTunnelManageScore().doubleValue());
            row.getCell(13).setCellValue(base.getRoadGreenScore().doubleValue());
            row.getCell(14).setCellValue(base.getTagManageScore().doubleValue());
            row.getCell(15).setCellValue(base.getRoadEnvScore().doubleValue());
        }
        workbook.setForceFormulaRecalculation(true);
    }
    /**
     * 填充数量
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetTwoEnvCount(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getStartCheckCode());
            row.getCell(8).setCellValue(base.getEndCheckCode());
            row.getCell(9).setCellValue(Objects.equals(1, base.getDirection()) ? "左幅" : "右幅");
            row.getCell(10).setCellValue(base.getRoadMaintenanceCount());
            row.getCell(11).setCellValue(base.getRoadCleanCount());
            row.getCell(12).setCellValue(base.getTunnelManageCount());
            row.getCell(13).setCellValue(base.getRoadGreenCount());
            row.getCell(14).setCellValue(base.getTagManageCount());
            row.getCell(15).setCellValue(base.getRoadEnvCount());
        }
        workbook.setForceFormulaRecalculation(true);
    }

    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetThree(List<EvaluateRoadSheetThree> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        int i = 1;
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetThree base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(i);
            row.getCell(1).setCellValue(base.getCompanyName());
            row.getCell(2).setCellValue(base.getScore().doubleValue());
            i++;
        }
        workbook.setForceFormulaRecalculation(true);
    }

    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetThreeCountQuan(List<EvaluateRoadSheetThree> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        int i = 1;
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetThree base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(i);
            row.getCell(1).setCellValue(base.getCompanyName());
            row.getCell(2).setCellValue(base.getManageCount());
            row.getCell(3).setCellValue(base.getCheckRoadCount());
            row.getCell(4).setCellValue(base.getCount());
            row.getCell(5).setCellValue(base.getScore().doubleValue());
            i++;
        }
        workbook.setForceFormulaRecalculation(true);
    }


    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetThreeCount(List<EvaluateRoadSheetThree> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        int i = 1;
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetThree base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(i);
            row.getCell(1).setCellValue(base.getCompanyName());
//            row.getCell(2).setCellValue(base.getManageCount());
            row.getCell(2).setCellValue(base.getCheckRoadCount());
//            row.getCell(4).setCellValue(base.getCount());
            row.getCell(3).setCellValue(base.getScore().doubleValue());
            i++;
        }
        workbook.setForceFormulaRecalculation(true);
    }



    private void initExcelSheetCompanyCountRank(List<EvaluateRoadSheetThree> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        int i = 1;
        for (int j = firstDataRow; j < lastDataRow-1; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetThree base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(i);
            row.getCell(1).setCellValue(base.getCompanyName());
            row.getCell(2).setCellValue(base.getCheckRoadCount());
            row.getCell(3).setCellValue(base.getCount());
            if(base.getCheckRoadCount()!=0){
                row.getCell(4).setCellValue(new BigDecimal(base.getCount()).divide(new BigDecimal(base.getCheckRoadCount()),2,BigDecimal.ROUND_HALF_UP).doubleValue());
            }else{
                row.getCell(4).setCellValue(0);
            }
            i++;
        }
        workbook.setForceFormulaRecalculation(true);
    }



    private void initExcelSheetCompanyScoreRank(List<EvaluateRoadSheetThree> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        int i = 1;
        for (int j = firstDataRow; j < lastDataRow-1; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetThree base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(i);
            row.getCell(1).setCellValue(base.getCompanyName());
            row.getCell(2).setCellValue(base.getCheckRoadCount());
            row.getCell(3).setCellValue(base.getCount());
            row.getCell(4).setCellValue(base.getScore().doubleValue());
            if(base.getCheckRoadCount()!=0){
                row.getCell(5).setCellValue(new BigDecimal(base.getCount()).divide(new BigDecimal(base.getCheckRoadCount()),2,BigDecimal.ROUND_HALF_UP).doubleValue());
            }else{
                row.getCell(5).setCellValue(0);
            }
            i++;
        }
        workbook.setForceFormulaRecalculation(true);
    }



    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetFour(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getCity());
            row.getCell(8).setCellValue(base.getStartCheckCode());
            row.getCell(9).setCellValue(base.getEndCheckCode());
            row.getCell(10).setCellValue(base.getBuildingOneScore().doubleValue());
            row.getCell(11).setCellValue(base.getBuildingTwoScore().doubleValue());
            row.getCell(12).setCellValue(base.getBuildingThreeScore().doubleValue());
            row.getCell(13).setCellValue(base.getBuildingFourScore().doubleValue());
            row.getCell(14).setCellValue(base.getBuildingFiveScore().doubleValue());
            row.getCell(15).setCellValue(base.getBuildingTotalScore().doubleValue());
        }
        Row row = sheet.createRow(lastDataRow);
        row.createCell(0).setCellValue("备注：其他路段均为满分");
        //基础列0-10
        workbook.setForceFormulaRecalculation(true);
    }




    /**
     * 填充指定excel的sheet数据-子表
     *
     * @param skuList
     * @param sheet
     * @param workbook
     */
    private void initExcelSheetFourCount(List<EvaluateRoadSheetOne> skuList, XSSFWorkbook workbook, Sheet sheet, int totalCol, int firstDataRow) {
        //业务数据第一行的行数
        int lastDataRow = skuList.size() + firstDataRow;
        //初始化每一行每一列
        this.initSheetRowAndCol(workbook, sheet, totalCol, firstDataRow, lastDataRow);
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.getRow(j);
            //填充当前行每一列的数据字段
            EvaluateRoadSheetOne base = skuList.get(j - firstDataRow);
            //基础列0-10
            row.getCell(0).setCellValue(base.getCode());
            row.getCell(1).setCellValue(base.getRoadCode());
            row.getCell(2).setCellValue(base.getRoadName());
            row.getCell(3).setCellValue(base.getCompanyName());
            row.getCell(4).setCellValue(base.getStartCode());
            row.getCell(5).setCellValue(base.getEndCode());
            row.getCell(6).setCellValue(base.getMileage().doubleValue());
            row.getCell(7).setCellValue(base.getCity());
            row.getCell(8).setCellValue(base.getStartCheckCode());
            row.getCell(9).setCellValue(base.getEndCheckCode());
            row.getCell(10).setCellValue(base.getBuildingOneCount());
            row.getCell(11).setCellValue(base.getBuildingTwoCount());
            row.getCell(12).setCellValue(base.getBuildingThreeCount());
            row.getCell(13).setCellValue(base.getBuildingFourCount());
            row.getCell(14).setCellValue(base.getBuildingFiveCount());
            row.getCell(15).setCellValue(base.getBuildingTotalCount());
        }
        Row row = sheet.createRow(lastDataRow);
        row.createCell(0).setCellValue("备注：其他路段均为满分");
        //基础列0-10
        workbook.setForceFormulaRecalculation(true);
    }


    /**
     * 初始化每一行每一列
     *
     * @param workbook
     * @param sheet
     * @param totalCol
     * @param firstDataRow
     * @param lastDataRow
     */
    private void initSheetRowAndCol(Workbook workbook, Sheet sheet, int totalCol, int firstDataRow, int lastDataRow) {
        // 创建一个单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);  // 设置自动换行
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
        //不初始化第5行的样例数据
        for (int j = firstDataRow; j < lastDataRow; j++) {
            Row row = sheet.createRow(j);
            //操作当前行的每一列
            //初始化当前行的每一列数据的基本单元格格式和数据
            for (int k = 0; k < totalCol; k++) {
                Cell cell = row.createCell(k);
                if (cell != null) {
                    cell.setCellStyle(cellStyle);
                }
            }
            row.setHeight((short) -1);  // 设置自适应行高
        }
    }


    /**
     * 复制单元格
     *
     * @param srcCell
     * @param distCell
     * @param copyValueFlag true则连同cell的内容一起复制
     */
//    private static void copyCell(Workbook wb, Cell srcCell, Cell distCell, CellStyle newStyle, boolean copyValueFlag) {
//        if (Objects.isNull(srcCell)) {
//            return;
//        }
//        CellStyle srcStyle = srcCell.getCellStyle();
//        newStyle.cloneStyleFrom(srcStyle);
//        newStyle.setFont(wb.getFontAt(srcStyle.getFontIndex()));
//
//        // 样式
//        distCell.setCellStyle(newStyle);
//
//        // 内容
//        if (srcCell.getCellComment() != null) {
//            distCell.setCellComment(srcCell.getCellComment());
//        }
//
//        // 不同数据类型处理
//        CellType srcCellType = srcCell.getCellTypeEnum();
//        if (Objects.equals(srcCellType, CellType.FORMULA)) {
//            distCell.setCellType(CellType.STRING);
//        } else {
//            distCell.setCellType(srcCellType);
//        }
//        if (copyValueFlag) {
//            if (srcCellType == CellType.NUMERIC) {
//                if (DateUtil.isCellDateFormatted(srcCell)) {
//                    distCell.setCellValue(srcCell.getDateCellValue());
//                } else {
//                    distCell.setCellValue(srcCell.getNumericCellValue());
//                }
//            } else if (srcCellType == CellType.STRING) {
//                distCell.setCellValue(srcCell.getRichStringCellValue());
//            } else if (srcCellType == CellType.BLANK) {
//
//            } else if (srcCellType == CellType.BOOLEAN) {
//                distCell.setCellValue(srcCell.getBooleanCellValue());
//            } else if (srcCellType == CellType.ERROR) {
//                distCell.setCellErrorValue(srcCell.getErrorCellValue());
//            } else if (srcCellType == CellType.FORMULA) {
//                distCell.setCellFormula(srcCell.getCellFormula());
//            }
//        }
//    }
}

