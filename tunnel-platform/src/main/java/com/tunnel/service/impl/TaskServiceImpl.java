package com.tunnel.service.impl;

import java.io.IOException;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSON;
import com.tunnel.CommonBaseEntityUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.core.domain.entity.SysDept;
import com.tunnel.common.enums.TaskNameTypeVO;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.Road;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.TaskEvaluate;
import com.tunnel.mapper.TaskDetailMapper;
import com.tunnel.mapper.TaskMapper;
import com.tunnel.service.TaskDetailService;
import com.tunnel.service.TaskEvaluateService;
import com.tunnel.service.TaskService;
import com.tunnel.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 任务列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class TaskServiceImpl implements TaskService {
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskDetailMapper taskDetailMapper;
    @Resource
    private TaskEvaluateService taskEvaluateService;
    @Resource
    private TaskDetailService taskDetailService;
    @Resource
    private ISysDeptService iSysDeptService;


    /**
     * 查询任务列表
     *
     * @param id 任务列表主键
     * @return 任务列表
     */
    @Override
    public Task selectScTaskById(Long id) {
        return taskMapper.selectScTaskById(id);
    }

    /**
     * 查询任务列表列表
     *
     * @param scTask 任务列表
     * @return 任务列表
     */
    @DataScope(deptAlias = "de")
    @Override
    public List<Task> selectScTaskList(Task scTask) {
        List<Task> list = taskMapper.selectScTaskList(scTask);
        for (Task task : list) {
            task.setTaskName(TaskNameTypeVO.getInspectionName(task.getTaskDate()));
        }
        CommonBaseEntityUtil.initUserNameByList(list);
        return list;
    }

    /**
     * 新增任务列表
     *
     * @param scTask 任务列表
     * @return 结果
     */
    @Override
    public int insertScTask(Task scTask) {
        scTask.setCreateTime(DateUtils.getNowDate());
        List<SysDept> deptList = iSysDeptService.selectAllDeptList();
        Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
        scTask.setDeptId(deptMap.get(scTask.getCompanyName()));
        scTask.setUserId(0L);
        return taskMapper.insertScTask(scTask);
    }

    /**
     * 修改任务列表
     *
     * @param scTask 任务列表
     * @return 结果
     */
    @Override
    public int updateScTask(Task scTask) throws IOException {
        scTask.setUpdateTime(DateUtils.getNowDate());
        taskDetailService.dealWithTaskPhoto(scTask);
        System.out.println(JSON.toJSONString(scTask));
        return taskMapper.updateScTask(scTask);
    }

    /**
     * 批量删除任务列表
     *
     * @param ids 需要删除的任务列表主键
     * @return 结果
     */
    @Override
    public int deleteScTaskByIds(Long[] ids) {
        return taskMapper.deleteScTaskByIds(ids);
    }

    /**
     * 删除任务列表信息
     *
     * @param id 任务列表主键
     * @return 结果
     */
    @Override
    public int deleteScTaskById(Long id) {
        return taskMapper.deleteScTaskById(id);
    }

    @Override
    @Transactional
    public void taskComplete(Task task) {
        if (Objects.isNull(task.getId())) {
            throw new ServiceException("任务ID不能为空");
        }
        //查询任务
        Task taskTemp = taskMapper.selectScTaskById(task.getId());
        if(Objects.isNull(taskTemp)){
            throw new ServiceException("任务不存在");
        }
        if(Objects.equals(taskTemp.getStatus(),1)){
            return;
        }
        if(StringUtils.isEmpty(task.getStartCheckCode()) || StringUtils.isEmpty(task.getEndCheckCode())){
            throw new ServiceException("检测起点桩号或检测讫点桩号不能为空");
        }
        if (Objects.isNull(task.getDirection())) {
            throw new ServiceException("检测方向不能为空");
        }
        //更新当前任务下的所有检测明细的检测起点桩号或检测讫点桩号和方向
        int k=taskDetailMapper.updateByTask(task);
        int j = taskMapper.taskComplete(task.getId());
        if (j == 0) {
            throw new ServiceException("更新失败任务状态");
        }
        //进行评定
        TaskEvaluate taskEvaluate=new TaskEvaluate();
        taskEvaluate.setTaskId(taskTemp.getId());
        taskEvaluate.setType(1);
        taskEvaluateService.insertTaskEvaluate(taskEvaluate);
        taskEvaluate.setType(2);
        taskEvaluateService.insertTaskEvaluate(taskEvaluate);
    }

    @Override
    @Transactional
    public void taskCompleteWX(Task task) {
        if (Objects.isNull(task.getId())) {
            throw new ServiceException("任务ID不能为空");
        }
        //查询任务
        Task taskTemp = taskMapper.selectScTaskById(task.getId());
        if(Objects.isNull(taskTemp)){
            throw new ServiceException("任务不存在");
        }
        if(Objects.equals(taskTemp.getStatus(),1)){
            return;
        }
        //更新当前任务下的所有检测明细的检测起点桩号或检测讫点桩号和方向
        int k=taskDetailMapper.updateByTask(task);
        int j = taskMapper.taskComplete(task.getId());
        if (j == 0) {
            throw new ServiceException("更新失败任务状态");
        }
        //进行评定
        TaskEvaluate taskEvaluate=new TaskEvaluate();
        taskEvaluate.setTaskId(taskTemp.getId());
        taskEvaluate.setType(1);
        taskEvaluateService.insertTaskEvaluate(taskEvaluate);
        taskEvaluate.setType(2);
        taskEvaluateService.insertTaskEvaluate(taskEvaluate);
    }

    @Override
    public List<String> listAllMonth() {
        boolean isAfter =  DateUtils.isAfter21thOfMonth();
        String filterDate = null;
        if (!isAfter) {
            filterDate = DateUtils.getNowMonth();
        }
        return taskMapper.listAllMonth(filterDate);
    }

    @Override
    public Task taskDetailList(Task task) {
        Task taskTemp = taskMapper.selectScTaskById(task.getId());
        if(Objects.isNull(taskTemp)){
            throw new ServiceException("任务不存在");
        }
        List<TaskDetail> taskDetailList = taskDetailMapper.selectByTaskId(task.getId(), null, null, null
                , null,null, SecurityUtils.getLoginUser().getUserId());
        taskDetailList=taskDetailList.stream().sorted(Comparator.comparing(TaskDetail::getCreateTime).reversed()).collect(Collectors.toList());
        taskTemp.setTaskDetailList(taskDetailList);
        //获取前3个月的检测区间
        List<String> previousThreeMonths = getPreviousThreeMonths(taskTemp.getTaskDate());
        if (CollectionUtils.isEmpty(previousThreeMonths)) {
            return taskTemp;
        }
        List<String> threeMonthCheckCode = taskDetailMapper.selectStartAndEndCheckCode(previousThreeMonths.get(0), taskTemp.getRoadId());
        taskTemp.setThreeMonthCheckCode(joinWithComma(threeMonthCheckCode));
        List<String> twoMonthCheckCode = taskDetailMapper.selectStartAndEndCheckCode(previousThreeMonths.get(1), taskTemp.getRoadId());
        taskTemp.setTwoMonthCheckCode(joinWithComma(twoMonthCheckCode));
        List<String> oneMonthCheckCode = taskDetailMapper.selectStartAndEndCheckCode(previousThreeMonths.get(2), taskTemp.getRoadId());
        taskTemp.setOneMonthCheckCode(joinWithComma(oneMonthCheckCode));
        return taskTemp;
    }

    /**
     * 字符串集合转为逗号分隔
     * @param strings 集合
     * @return 逗号分隔字符串
     */
    public static String joinWithComma(List<String> strings) {
        return String.join(",", strings);
    }

    /**
     * 获取前三个月份
     * @param inputMonth 2025-08
     * @return 输出: [2025-07, 2025-06, 2025-05]
     */
    public static List<String> getPreviousThreeMonths(String inputMonth) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(inputMonth, formatter);
        List<String> previousMonths = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            YearMonth previousMonth = yearMonth.minusMonths(i);
            previousMonths.add(previousMonth.format(formatter));
        }
        return previousMonths;
    }


    @Override
    public List<Task> listAllMonthDTO() {
        List<Task> list=taskMapper.listAllMonthDTO();
        for (Task task : list) {
            task.setTaskName(TaskNameTypeVO.getInspectionName(task.getTaskDate()));
        }
        return list;
    }

    @Override
    public void batchAdd(List<Road> roadList) {
        List<SysDept> deptList = iSysDeptService.selectAllDeptList();
        Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
        for (Road road : roadList) {
            road.setDeptId(deptMap.get(road.getCompanyName()));
            road.setUserId(0L);
        }
        taskMapper.batchAdd(roadList);
    }

    /**
     * 查询整改任务列表列表
     *
     * @param task 任务列表
     * @return 整改任务列表
     */
    @DataScope(deptAlias = "de")
    @Override
    public List<Task> selectScFixedTaskList(Task task) {
        List<Task> list = taskMapper.selectScFixedTaskList(task);
        for (Task t : list) {
            t.setTaskName(TaskNameTypeVO.getInspectionName(t.getTaskDate()));
        }
        CommonBaseEntityUtil.initUserNameByList(list);
        return list;
    }


}
