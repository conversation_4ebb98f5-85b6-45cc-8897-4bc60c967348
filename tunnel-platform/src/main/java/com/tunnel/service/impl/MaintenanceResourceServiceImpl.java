package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.MaintenanceResource;
import com.tunnel.domain.vo.MaintenanceResourceCountVO;
import com.tunnel.mapper.MaintenanceResourceMapper;
import com.tunnel.service.MaintenanceResourceService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class MaintenanceResourceServiceImpl implements MaintenanceResourceService {

    @Resource
    private MaintenanceResourceMapper maintenanceResourceMapper;

    @DataScope(deptAlias = "de")
    @Override
    public List<MaintenanceResource> selectMaintenanceResourceList(MaintenanceResource maintenanceResource) {
        return maintenanceResourceMapper.selectMaintenanceResourceList(maintenanceResource);
    }

    @Override
    public MaintenanceResource selectMaintenanceResourceById(Long id) {
        return maintenanceResourceMapper.selectMaintenanceResourceById(id);
    }

    @Override
    public int insertMaintenanceResource(MaintenanceResource maintenanceResource) {
        maintenanceResource.setCreator(SecurityUtils.getLoginUser().getUserId());
        maintenanceResource.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        maintenanceResource.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        maintenanceResource.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        //校验一下是不是有了
        String addTimePrefix = DateUtil.format(DateUtil.date(), "yyyy-MM");
        List<MaintenanceResource> exitList = this.selectMaintenanceResourceByCompanyList(addTimePrefix, Collections.singletonList(maintenanceResource.getCompanyName()));
        if (!CollectionUtils.isEmpty(exitList)) {
            throw new ServiceException("该公司当月投入情况数据已存在！");
        }
        if (maintenanceResource.getAddTime() == null) {
            maintenanceResource.setAddTime(new Date());
        }
        return maintenanceResourceMapper.insertMaintenanceResource(maintenanceResource);
    }

    @Override
    public int updateMaintenanceResource(MaintenanceResource maintenanceResource) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        maintenanceResource.setModifier(userId);
        maintenanceResource.setUpdateBy(userName);
        return maintenanceResourceMapper.updateMaintenanceResource(maintenanceResource);
    }

    @Override
    public int deleteMaintenanceResourceByIds(List<Long> ids) {
        return maintenanceResourceMapper.deleteMaintenanceResourceByIds(ids);
    }

    @Override
    public List<MaintenanceResource> selectMaintenanceResourceByCompanyList(String addTimePrefix, List<String> list) {
        return maintenanceResourceMapper.selectMaintenanceResourceByCompanyList(addTimePrefix, list);
    }

    @Override
    public int batchInsertResources(List<MaintenanceResource> list) {
        return maintenanceResourceMapper.batchInsertResources(list);
    }

    @Override
    public MaintenanceResourceCountVO queryDataBoardCount() {
        return maintenanceResourceMapper.queryDataBoardCount();
    }


}