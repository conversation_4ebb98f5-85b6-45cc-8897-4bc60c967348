package com.tunnel.service;

import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.CheckEnumRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 公路检测类别问题项Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface CheckEnumService
{
    /**
     * 查询公路检测类别问题项
     * 
     * @param id 公路检测类别问题项主键
     * @return 公路检测类别问题项
     */
    public CheckEnum selectCheckEnumById(Long id);

    /**
     * 查询公路检测类别问题项列表
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 公路检测类别问题项集合
     */
    public List<CheckEnum> selectCheckEnumList(CheckEnum checkEnum);

    /**
     * 新增公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    public int insertCheckEnum(CheckEnum checkEnum);

    /**
     * 修改公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    public int updateCheckEnum(CheckEnum checkEnum);

    /**
     * 批量删除公路检测类别问题项
     * 
     * @param ids 需要删除的公路检测类别问题项主键集合
     * @return 结果
     */
    public int deleteCheckEnumByIds(Long[] ids);

    /**
     * 删除公路检测类别问题项信息
     * 
     * @param id 公路检测类别问题项主键
     * @return 结果
     */
    public int deleteCheckEnumById(Long id);

    List<String> distinctListByType(CheckEnum checkEnum);

    CheckEnumRes queryDefaultScoreList(CheckEnum checkEnum);

    List<CheckEnum> listDistinctCheckContent();

    List<BigDecimal> distinctListScoreAll();
}
