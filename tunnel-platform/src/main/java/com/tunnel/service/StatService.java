package com.tunnel.service;

import com.tunnel.domain.MapQueryDTO;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.stat.ResultStat;
import com.tunnel.domain.stat.ResultStatNew;
import org.apache.poi.xssf.model.MapInfo;

import java.util.List;

/**
 * 检测任务评定Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface StatService
{

    ResultStat queryStatInfo(Task task);

    ResultStat queryStatInfoCount(Task task);

    ResultStatNew queryStatInfoCountNew(Task task);

    ResultStatNew queryStatInfoNew(Task task);

    List<TaskDetail> queryMapInfo(MapQueryDTO mapQueryDTO);
}
