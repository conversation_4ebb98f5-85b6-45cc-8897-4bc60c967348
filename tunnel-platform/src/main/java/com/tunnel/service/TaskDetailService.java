package com.tunnel.service;

import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.dto.NoticeTaskDetailDTO;
import com.tunnel.domain.dto.TaskDetailCountRes;
import com.tunnel.domain.dto.TaskDetailQueryDTO;
import com.tunnel.domain.dto.TaskGroupQueryDTO;
import com.tunnel.domain.vo.TaskDetailCountVO;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
/**
 * 检测列表Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface TaskDetailService
{
    /**
     * 查询检测列表
     * 
     * @param id 检测列表主键
     * @return 检测列表
     */
    public TaskDetail selectScTaskDetailById(Long id);

    /**
     * 查询检测列表列表
     * 
     * @param taskDetail 检测列表
     * @return 检测列表集合
     */
    public List<TaskDetail> selectScTaskDetailList(TaskDetail taskDetail);

    /**
     * 导出问题清单
     */
    void exportNew(HttpServletResponse response, TaskDetail taskDetail);

    /**
     * 新增检测列表
     * 
     * @param taskDetail 检测列表
     * @return 结果
     */
    public int insertScTaskDetail(TaskDetail taskDetail);

    /**
     * 修改检测列表
     * 
     * @param taskDetail 检测列表
     * @return 结果
     */
    public int updateScTaskDetail(TaskDetail taskDetail);

    /**
     * 批量删除检测列表
     * 
     * @param ids 需要删除的检测列表主键集合
     * @return 结果
     */
    public int deleteScTaskDetailByIds(Long[] ids);

    /**
     * 删除检测列表信息
     * 
     * @param id 检测列表主键
     * @return 结果
     */
    public int deleteScTaskDetailById(Long id);

    void saveOrUpdate(TaskDetail taskDetail) throws IOException;

    void deleteById(TaskDetail taskDetail);

    void dealWithTaskPhoto(Task task) throws IOException;

    TaskDetailCountRes selectScTaskDetailNumWithCompany(TaskGroupQueryDTO queryDTO);

    TaskDetailCountRes exportDetailGroup(TaskGroupQueryDTO queryDTO);

    List<NoticeTaskDetailDTO> selectNoticeTaskDetail();

    boolean oneClickConfirm(List<Long> taskIdList);

    TaskDetailCountRes listByCompany(TaskGroupQueryDTO queryDTO);

    TaskDetailCountRes exportByCompany(TaskGroupQueryDTO queryDTO);

    TaskDetailCountRes listByRoad(TaskGroupQueryDTO queryDTO);

    List<TaskDetailQueryDTO> selectScTaskDetailListByTaskId(Long id);

    Integer fixedTask(Long taskId);
}
