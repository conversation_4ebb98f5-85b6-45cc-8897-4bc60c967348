package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.Road;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 基础数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface RoadService
{
    /**
     * 查询基础数据
     * 
     * @param id 基础数据主键
     * @return 基础数据
     */
    public Road selectScRoadById(Long id);

    /**
     * 查询基础数据列表
     * 
     * @param road 基础数据
     * @return 基础数据集合
     */
    public List<Road> selectScRoadList(Road road);

    /**
     * 新增基础数据
     * 
     * @param road 基础数据
     * @return 结果
     */
    public int insertScRoad(Road road);

    /**
     * 修改基础数据
     * 
     * @param road 基础数据
     * @return 结果
     */
    public int updateScRoad(Road road);

    /**
     * 批量删除基础数据
     * 
     * @param ids 需要删除的基础数据主键集合
     * @return 结果
     */
    public int deleteScRoadByIds(Long[] ids);

    /**
     * 删除基础数据信息
     * 
     * @param id 基础数据主键
     * @return 结果
     */
    public int deleteScRoadById(Long id);

    BatchAddResponse batchAdd(MultipartFile file);

    List<Road> listAllCompany(Road road);

}
