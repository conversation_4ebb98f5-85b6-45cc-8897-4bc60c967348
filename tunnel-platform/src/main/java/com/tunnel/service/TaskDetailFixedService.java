package com.tunnel.service;

import com.tunnel.domain.TaskDetailFixed;
import com.tunnel.domain.dto.NoticeTaskDetailDTO;

import java.util.List;

public interface TaskDetailFixedService {
    
    /**
     * 查询整改信息列表
     */
    List<TaskDetailFixed> selectTaskDetailFixedList(TaskDetailFixed taskDetailFixed);

    /**
     * 根据ID查询整改信息
     */
    TaskDetailFixed selectTaskDetailFixedByDetailId(Long detailId);

    /**
     * 新增整改信息
     */
    int insertTaskDetailFixed(TaskDetailFixed taskDetailFixed);

    /**
     * 修改整改信息
     */
    int updateTaskDetailFixed(TaskDetailFixed taskDetailFixed);

    /**
     * 批量删除整改信息
     */
    int deleteTaskDetailFixedByIds(List<Long> ids);
}