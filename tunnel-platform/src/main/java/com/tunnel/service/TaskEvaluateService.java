package com.tunnel.service;

import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.TaskEvaluate;
import com.tunnel.domain.stat.RankStat;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 检测任务评定Service接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface TaskEvaluateService {
    /**
     * 查询检测任务评定
     *
     * @param id 检测任务评定主键
     * @return 检测任务评定
     */
    public TaskEvaluate selectTaskEvaluateById(Long id);

    /**
     * 查询检测任务评定列表
     *
     * @param taskEvaluate 检测任务评定
     * @return 检测任务评定集合
     */
    public List<TaskEvaluate> selectTaskEvaluateList(TaskEvaluate taskEvaluate);

    /**
     * 新增检测任务评定
     *
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    public int insertTaskEvaluate(TaskEvaluate taskEvaluate);

    /**
     * 修改检测任务评定
     *
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    public int updateTaskEvaluate(TaskEvaluate taskEvaluate);

    /**
     * 批量删除检测任务评定
     *
     * @param ids 需要删除的检测任务评定主键集合
     * @return 结果
     */
    public int deleteTaskEvaluateByIds(Long[] ids);

    /**
     * 删除检测任务评定信息
     *
     * @param id 检测任务评定主键
     * @return 结果
     */
    public int deleteTaskEvaluateById(Long id);

    List<List<CheckEnum>> getScoreInfo(Long id);

    File exportTaskEvaluate(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException;

    File exportTaskEvaluateEnv(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException;

    void exportEvaluate(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException;

    File exportWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException;

    File exportPicWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException;

    void handleAddAll(TaskEvaluate taskEvaluate);

    void exportZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException, ExecutionException, InterruptedException;

    void exportPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException;

    void exportSheetPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException;

    void exportSheetServiceAreaPicZip(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException;

    List<RankStat> matchCompanyList(TaskEvaluate taskEvaluate);

    List<TaskEvaluate> selectByTaskIdList(List<Long> taskIdList);

    void insertExcelImage(Workbook workbook, Sheet sheet, String imageUrl, int rowNum, int colNum);

    File initExcelPathFun(XSSFWorkbook workbook, String fileName, String sourcePath);
}
