package com.tunnel.service;

import com.tunnel.domain.Road;
import com.tunnel.domain.Task;

import java.io.IOException;
import java.util.List;

/**
 * 任务列表Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface TaskService
{
    /**
     * 查询任务列表
     * 
     * @param id 任务列表主键
     * @return 任务列表
     */
    public Task selectScTaskById(Long id);

    /**
     * 查询任务列表列表
     * 
     * @param task 任务列表
     * @return 任务列表集合
     */
    public List<Task> selectScTaskList(Task task);

    /**
     * 新增任务列表
     * 
     * @param task 任务列表
     * @return 结果
     */
    public int insertScTask(Task task);

    /**
     * 修改任务列表
     * 
     * @param task 任务列表
     * @return 结果
     */
    public int updateScTask(Task task) throws IOException;

    /**
     * 批量删除任务列表
     * 
     * @param ids 需要删除的任务列表主键集合
     * @return 结果
     */
    public int deleteScTaskByIds(Long[] ids);

    /**
     * 删除任务列表信息
     * 
     * @param id 任务列表主键
     * @return 结果
     */
    public int deleteScTaskById(Long id);

    void taskComplete(Task task);
    void taskCompleteWX(Task task);

    List<String> listAllMonth();

    Task taskDetailList(Task task);

    List<Task> listAllMonthDTO();

    void batchAdd(List<Road> roadList);

    List<Task> selectScFixedTaskList(Task task);
}
