package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.ScTaskNormal;
import com.tunnel.domain.dto.TaskDetailCountRes;
import com.tunnel.domain.dto.TaskNormalQueryDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TaskNormalService {

    /**
     * 批量新增/导入
     * @param file 文件
     * @return 结果
     */
    BatchAddResponse batchAdd(MultipartFile file);

    /**
     * 按公司汇总后的列表
     */
    TaskDetailCountRes listByCompanyPage(TaskNormalQueryDTO queryDTO);

    /**
     * 根据id批量查询
     */
    List<ScTaskNormal> queryByIdList(List<Long> idList);

    /**
     * 根据taskDate删除
     */
    Integer deleteByTaskDate(String taskDate);
}
