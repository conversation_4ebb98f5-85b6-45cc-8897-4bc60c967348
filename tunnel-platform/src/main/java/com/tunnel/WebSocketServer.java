package com.tunnel;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket服务器，用于实时发送进度信息
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/{taskId}")
public class WebSocketServer {

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocketServer对象。
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 心跳检测定时任务
     */
    private static final ScheduledExecutorService heartbeatExecutor = Executors.newSingleThreadScheduledExecutor();

    /**
     * 心跳间隔（秒）
     */
    private static final int HEARTBEAT_INTERVAL = 30;

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收taskId
     */
    private String taskId = "";
    
    /**
     * 存储下载任务信息的Map
     */
    private static ConcurrentHashMap<String, DownloadTask> downloadTaskMap = new ConcurrentHashMap<>();
    
    /**
     * 下载任务类
     */
    private static class DownloadTask {
        private String taskId;
        private int downloadType;
        private int progress;
        private String stage;
        private int totalPhotos;
        private int loadedPhotos;
        private boolean completed;
        
        public DownloadTask(String taskId, int downloadType) {
            this.taskId = taskId;
            this.downloadType = downloadType;
            this.progress = 0;
            this.stage = "初始化";
            this.totalPhotos = 0;
            this.loadedPhotos = 0;
            this.completed = false;
        }
    }
    
    /**
     * 注册下载任务
     */
    public static DownloadTask registerTask(String taskId, int downloadType) {
        DownloadTask task = new DownloadTask(taskId, downloadType);
        downloadTaskMap.put(taskId, task);
        log.info("注册下载任务: taskId={}, downloadType={}", taskId, downloadType);
        return task;
    }
    
    /**
     * 更新任务进度
     */
    public static void updateTaskProgress(String taskId, int downloadType, int progress, String stage, int totalPhotos, int loadedPhotos) {
        DownloadTask task = downloadTaskMap.get(taskId);
        if (task != null) {
            task.downloadType = downloadType;
            task.progress = progress;
            task.stage = stage;
            task.totalPhotos = totalPhotos;
            task.loadedPhotos = loadedPhotos;
            log.info("更新任务进度: taskId={}, downloadType={}, progress={}, stage={}", taskId, downloadType, progress, stage);
        } else {
            log.warn("尝试更新不存在的任务: taskId={}", taskId);
        }
    }
    
    /**
     * 完成任务
     */
    public static void completeTask(String taskId) {
        DownloadTask task = downloadTaskMap.get(taskId);
        if (task != null) {
            task.completed = true;
            task.progress = 100;
            log.info("任务完成: taskId={}", taskId);
            
            // 延迟清理任务
            heartbeatExecutor.schedule(() -> {
                downloadTaskMap.remove(taskId);
                log.info("清理完成的任务: taskId={}", taskId);
            }, 5, TimeUnit.MINUTES);
        }
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("taskId") String taskId) {
        log.info("WebSocket连接建立开始: taskId={}, sessionId={}", taskId, session.getId());
        this.session = session;
        this.taskId = taskId;
        if (webSocketMap.containsKey(taskId)) {
            webSocketMap.remove(taskId);
            webSocketMap.put(taskId, this);
            log.info("WebSocket连接已存在，已更新: taskId={}", taskId);
        } else {
            webSocketMap.put(taskId, this);
            addOnlineCount();
            log.info("WebSocket连接新建: taskId={}", taskId);
        }
        log.info("用户连接: taskId={}, 当前在线人数: {}", taskId, getOnlineCount());
        try {
            // 发送JSON格式的连接成功消息
            Map<String, Object> connectResponse = new HashMap<>();
            connectResponse.put("type", "connection");
            connectResponse.put("status", "success");
            connectResponse.put("message", "连接成功");
            connectResponse.put("time", System.currentTimeMillis());
            sendMessage(JSON.toJSONString(connectResponse));
            log.info("WebSocket连接建立成功: taskId={}", taskId);
            
            // 启动心跳检测
            startHeartbeat(taskId);
        } catch (IOException e) {
            log.error("WebSocket连接建立失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        log.info("WebSocket连接关闭: taskId={}", taskId);
        if (webSocketMap.containsKey(taskId)) {
            webSocketMap.remove(taskId);
            subOnlineCount();
            
            // 清理当前任务的进度数据
            if (downloadTaskMap.containsKey(taskId)) {
                downloadTaskMap.remove(taskId);
                log.info("已清理任务进度数据: taskId={}", taskId);
            }
            
            log.info("WebSocket连接已移除: taskId={}, 当前在线人数: {}", taskId, getOnlineCount());
        }
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到用户消息: taskId={}, message={}", taskId, message);
        // 处理心跳消息
        if ("ping".equals(message)) {
            try {
                // 发送JSON格式的心跳响应，而不是纯文本
                Map<String, Object> pongResponse = new HashMap<>();
                pongResponse.put("type", "heartbeat");
                pongResponse.put("action", "pong");
                pongResponse.put("time", System.currentTimeMillis());
                sendMessage(JSON.toJSONString(pongResponse));
                log.debug("回复心跳消息: taskId={}", taskId);
            } catch (IOException e) {
                log.error("回复心跳消息失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }
        }
    }

    /**
     * 发生错误时调用
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误: taskId={}, sessionId={}, error={}", 
                this.taskId, session.getId(), error.getMessage(), error);
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        log.debug("发送消息: taskId={}, message={}", this.taskId, message);
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 发送自定义消息
     */
    public static void sendInfo(String message, @PathParam("taskId") String taskId) throws IOException {
        log.info("准备发送消息: taskId={}, message={}", taskId, message);
        if (taskId != null && webSocketMap.containsKey(taskId)) {
            webSocketMap.get(taskId).sendMessage(message);
            log.info("消息发送成功: taskId={}", taskId);
        } else {
            log.warn("用户不在线，消息发送失败: taskId={}", taskId);
        }
    }


    /**
     * 发送带有任务ID的进度信息
     */
    public static void sendProgressInfoWithTaskId(String taskId, int downloadType, int progress, String stage, int totalPhotos, int loadedPhotos) {
        try {
            log.info("准备发送进度信息: taskId={}, downloadType={}, progress={}, stage={}, totalPhotos={}, loadedPhotos={}", taskId, downloadType, progress, stage, totalPhotos, loadedPhotos);
            
            // 注册或更新任务
            DownloadTask task = downloadTaskMap.get(taskId);
            if (task == null) {
                task = registerTask(taskId, downloadType);
            }
            
            // 更新任务进度
            updateTaskProgress(taskId, downloadType, progress, stage, totalPhotos, loadedPhotos);
            
            // 如果进度达到100%，标记任务完成
            if (progress >= 100) {
                completeTask(taskId);
            }
            
            Map<String, Object> progressInfo = new HashMap<>();
            progressInfo.put("type", "progress");
            progressInfo.put("taskId", taskId);
            progressInfo.put("downloadType", downloadType);
            progressInfo.put("progress", progress);
            progressInfo.put("stage", stage);
            progressInfo.put("totalPhotos", totalPhotos);
            progressInfo.put("loadedPhotos", loadedPhotos);
            
            String message = JSON.toJSONString(progressInfo);
            sendInfo(message, taskId);
            
            log.info("进度信息发送完成: taskId={}", taskId);
        } catch (Exception e) {
            log.error("发送进度信息失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeat(String taskId) {
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            try {
                if (webSocketMap.containsKey(taskId)) {
                    WebSocketServer server = webSocketMap.get(taskId);
                    Map<String, Object> heartbeat = new HashMap<>();
                    heartbeat.put("type", "heartbeat");
                    heartbeat.put("time", System.currentTimeMillis());
                    server.sendMessage(JSON.toJSONString(heartbeat));
                    log.debug("发送心跳消息: taskId={}", taskId);
                } else {
                    log.debug("用户不在线，取消心跳: taskId={}", taskId);
                }
            } catch (Exception e) {
                log.error("发送心跳消息失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }

    /**
     * 获取当前在线连接数
     */
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    /**
     * 增加在线连接数
     */
    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
        log.debug("在线连接数+1: 当前在线人数={}", onlineCount);
    }

    /**
     * 减少在线连接数
     */
    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
        log.debug("在线连接数-1: 当前在线人数={}", onlineCount);
    }
} 