package com.tunnel.mapper;


import com.tunnel.domain.TaskTeamRelation;

import java.util.List;

/**
 * 任务和检测组关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface TaskTeamRelationMapper 
{
    /**
     * 查询任务和检测组关系
     * 
     * @param id 任务和检测组关系主键
     * @return 任务和检测组关系
     */
    public TaskTeamRelation selectTaskTeamRelationById(Long id);

    /**
     * 查询任务和检测组关系列表
     * 
     * @param taskTeamRelation 任务和检测组关系
     * @return 任务和检测组关系集合
     */
    public List<TaskTeamRelation> selectTaskTeamRelationList(TaskTeamRelation taskTeamRelation);

    /**
     * 新增任务和检测组关系
     * 
     * @param taskTeamRelation 任务和检测组关系
     * @return 结果
     */
    public int insertTaskTeamRelation(TaskTeamRelation taskTeamRelation);

    /**
     * 修改任务和检测组关系
     * 
     * @param taskTeamRelation 任务和检测组关系
     * @return 结果
     */
    public int updateTaskTeamRelation(TaskTeamRelation taskTeamRelation);

    /**
     * 删除任务和检测组关系
     * 
     * @param id 任务和检测组关系主键
     * @return 结果
     */
    public int deleteTaskTeamRelationById(Long id);

    /**
     * 批量删除任务和检测组关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskTeamRelationByIds(Long[] ids);
}
