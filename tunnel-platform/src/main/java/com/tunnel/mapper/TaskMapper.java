package com.tunnel.mapper;

import com.tunnel.domain.Road;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskEvaluate;
import com.tunnel.domain.dto.TaskGroupQueryDTO;
import com.tunnel.domain.vo.TaskDetailCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务列表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface TaskMapper
{
    /**
     * 查询任务列表
     * 
     * @param id 任务列表主键
     * @return 任务列表
     */
    public Task selectScTaskById(Long id);

    /**
     * 查询任务列表列表
     * 
     * @param task 任务列表
     * @return 任务列表集合
     */
    public List<Task> selectScTaskList(Task task);

    /**
     * 新增任务列表
     * 
     * @param task 任务列表
     * @return 结果
     */
    public int insertScTask(Task task);

    /**
     * 修改任务列表
     * 
     * @param task 任务列表
     * @return 结果
     */
    public int updateScTask(Task task);

    /**
     * 删除任务列表
     * 
     * @param id 任务列表主键
     * @return 结果
     */
    public int deleteScTaskById(Long id);

    /**
     * 批量删除任务列表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScTaskByIds(Long[] ids);

    int taskComplete(Long id);

    void batchAdd(@Param("roadList") List<Road> roadList);

    List<Long> selectByTaskDate(String taskDate);

    List<String> listAllMonth(@Param("filterDate") String filterDate);

    List<Long> selectIdListByStatusParams(TaskEvaluate taskEvaluate);

    List<Task> listAllMonthDTO();

    List<Task> selectScTaskForCount(TaskGroupQueryDTO queryDTO);

    List<Task> selectTaskByCompany(TaskGroupQueryDTO queryDTO);

    List<Task> selectTaskByRoad(TaskGroupQueryDTO queryDTO);

    List<Task> selectScFixedTaskList(Task task);
}
