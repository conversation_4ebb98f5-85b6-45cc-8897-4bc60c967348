package com.tunnel.mapper;

import com.tunnel.domain.ScTaskNormal;
import com.tunnel.domain.dto.TaskNormalQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TaskNormalMapper {

    /**
     * 批量新增
     * @param dataList 数据
     */
    void batchAdd(@Param("dataList") List<ScTaskNormal> dataList);

    /**
     * 按公司汇总后的列表
     *
     */
    List<ScTaskNormal> selectByTaskDateOrCompany(TaskNormalQueryDTO queryDTO);


    /**
     * 根据id批量查询
     */
    List<ScTaskNormal> queryByIdList(@Param("idList") List<Long> idList);

    Integer queryCountByTaskDate(@Param("taskDate") String taskDate);

    Integer deleteByTaskDate(@Param("taskDate") String taskDate);
}
