package com.tunnel.mapper;

import com.tunnel.domain.TaskDetailFixed;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface TaskDetailFixedMapper {
    
    /**
     * 查询整改信息列表
     */
    List<TaskDetailFixed> selectTaskDetailFixedList(TaskDetailFixed taskDetailFixed);

    /**
     * 根据ID查询整改信息
     */
    TaskDetailFixed selectTaskDetailFixedByDetailId(Long detailId);

    /**
     * 新增整改信息
     */
    int insertTaskDetailFixed(TaskDetailFixed taskDetailFixed);

    /**
     * 修改整改信息
     */
    int updateTaskDetailFixed(TaskDetailFixed taskDetailFixed);

    /**
     * 批量删除整改信息
     */
    int deleteTaskDetailFixedByIds(@Param("ids") List<Long> ids);

    List<TaskDetailFixed> selectByIdList(@Param("ids") List<Long> ids);

    int batchUpdateTaskDetailFixed(@Param("list") List<TaskDetailFixed> list);
}