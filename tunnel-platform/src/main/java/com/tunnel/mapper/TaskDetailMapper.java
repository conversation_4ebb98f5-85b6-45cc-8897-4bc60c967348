package com.tunnel.mapper;

import com.tunnel.domain.MapQueryDTO;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.dto.NoticeTaskDetailDTO;
import com.tunnel.domain.dto.TaskDetailQueryDTO;
import com.tunnel.domain.stat.CircleStat;
import com.tunnel.domain.stat.QuestionStat;
import com.tunnel.domain.stat.ZhuStat;
import com.tunnel.domain.template.EvaluateRoadSheetOne;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.model.MapInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 检测列表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface TaskDetailMapper
{
    /**
     * 查询检测列表
     * 
     * @param id 检测列表主键
     * @return 检测列表
     */
    public TaskDetail selectScTaskDetailById(Long id);

    public List<TaskDetail> selectScTaskDetailByIdList(@Param("idList") Long[] idList);

    /**
     * 查询检测列表列表
     * 
     * @param taskDetail 检测列表
     * @return 检测列表集合
     */
    public List<TaskDetail> selectScTaskDetailList(TaskDetail taskDetail);

    /**
     * 新增检测列表
     * 
     * @param taskDetail 检测列表
     * @return 结果
     */
    public int insertScTaskDetail(TaskDetail taskDetail);

    /**
     * 修改检测列表
     * 
     * @param taskDetail 检测列表
     * @return 结果
     */
    public int updateScTaskDetail(TaskDetail taskDetail);

    /**
     * 删除检测列表
     * 
     * @param id 检测列表主键
     * @return 结果
     */
    public int deleteScTaskDetailById(Long id);

    /**
     * 批量删除检测列表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScTaskDetailByIds(Long[] ids);

    List<TaskDetail> selectByTaskId(@Param("taskId") Long taskId, @Param("type") Integer type, @Param("city") String city
            , @Param("direction") Integer direction, @Param("startCheckCode") String startCheckCode
            , @Param("endCheckCode") String endCheckCode, @Param("creator") Long creator);

    int updateByTask(Task task);

    int updateEnvId(@Param("id") Long id, @Param("envId") Long envId);

    void updateRoadCheckScoreByEnvId(@Param("score") BigDecimal score, @Param("id") Long id);

    List<EvaluateRoadSheetOne> listBaseInfoByTaskIdList(@Param("list") List<EvaluateRoadSheetOne> list, @Param("type") Integer type);

    BigDecimal sumCheckMileage(Task task);

    List<TaskDetail> selectGroupByTaskDate(Task task);

    int selectByTaskDetail(TaskDetail taskDetail);

    QuestionStat selectCountByTaskDate(Task task);

    List<ZhuStat> selectQuestionGroupByTaskDate(Task task);

    List<TaskDetail> selectCountByCompanyName(Task task);

    List<TaskDetail> selectCountByRoad(Task task);

    List<TaskDetail> selectByMapInfo(MapQueryDTO mapQueryDTO);

    List<TaskDetail> selectScTaskDetailByTaskIds(@Param("taskIds") List<Long> taskIds);

    List<TaskDetail> selectScTaskDetailByTaskIdsWithStatus(@Param("taskIds") List<Long> taskIds);

    List<TaskDetail> selectScTaskDetailWithTaskByTaskIds(@Param("taskIds") List<Long> taskIds);

    int updateScTaskDetailIsFixed(TaskDetail taskDetail);

    List<NoticeTaskDetailDTO> selectNoticeTaskDetail();

    List<TaskDetail> selectFixedDetailByTaskIds(@Param("taskIds") List<Long> taskIds);

    List<TaskDetailQueryDTO> selectScTaskDetailListByTaskId(Long id);

    Integer selectTaskDetailIsAllFixed(@Param("id") Long id);

    List<String> selectStartAndEndCheckCode(@Param("taskDate") String taskDate, @Param("roadId") Long roadId);
}
