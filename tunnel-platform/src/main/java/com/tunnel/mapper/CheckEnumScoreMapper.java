package com.tunnel.mapper;


import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.CheckEnumScore;
import com.tunnel.domain.TaskDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测类别问题项-最大分值Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface CheckEnumScoreMapper 
{
    /**
     * 查询检测类别问题项-最大分值
     * 
     * @param id 检测类别问题项-最大分值主键
     * @return 检测类别问题项-最大分值
     */
    public CheckEnumScore selectCheckEnumScoreById(Long id);

    /**
     * 查询检测类别问题项-最大分值列表
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 检测类别问题项-最大分值集合
     */
    public List<CheckEnumScore> selectCheckEnumScoreList(CheckEnumScore checkEnumScore);

    /**
     * 新增检测类别问题项-最大分值
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 结果
     */
    public int insertCheckEnumScore(CheckEnumScore checkEnumScore);

    /**
     * 修改检测类别问题项-最大分值
     * 
     * @param checkEnumScore 检测类别问题项-最大分值
     * @return 结果
     */
    public int updateCheckEnumScore(CheckEnumScore checkEnumScore);

    /**
     * 删除检测类别问题项-最大分值
     * 
     * @param id 检测类别问题项-最大分值主键
     * @return 结果
     */
    public int deleteCheckEnumScoreById(Long id);

    /**
     * 批量删除检测类别问题项-最大分值
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckEnumScoreByIds(Long[] ids);

    List<CheckEnumScore> selectByCheckEnumScore(@Param("detailList") List<TaskDetail> detailList);

    List<CheckEnum> selectAllList();
}
