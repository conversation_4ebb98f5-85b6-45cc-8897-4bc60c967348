package com.tunnel.mapper;

import com.tunnel.domain.Team;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测组和用户关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface TeamMapper 
{
    /**
     * 查询检测组和用户关系
     * 
     * @param id 检测组和用户关系主键
     * @return 检测组和用户关系
     */
    public Team selectTeamById(Long id);

    /**
     * 查询检测组和用户关系列表
     * 
     * @param team 检测组和用户关系
     * @return 检测组和用户关系集合
     */
    public List<Team> selectTeamList(Team team);

    /**
     * 新增检测组和用户关系
     * 
     * @param team 检测组和用户关系
     * @return 结果
     */
    public int insertTeam(Team team);

    /**
     * 修改检测组和用户关系
     * 
     * @param team 检测组和用户关系
     * @return 结果
     */
    public int updateTeam(Team team);

    /**
     * 删除检测组和用户关系
     * 
     * @param id 检测组和用户关系主键
     * @return 结果
     */
    public int deleteTeamById(Long id);

    /**
     * 批量删除检测组和用户关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeamByIds(Long[] ids);

    List<Team> listAll();

    List<Team> selectByTeamIdList(@Param("teamIds") List<Long> teamIds);
}
