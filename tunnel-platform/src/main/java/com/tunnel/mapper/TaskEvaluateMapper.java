package com.tunnel.mapper;

import com.tunnel.domain.Road;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.TaskEvaluate;
import com.tunnel.domain.stat.PieStat;
import com.tunnel.domain.stat.RankStat;
import com.tunnel.domain.template.EvaluateRoadSheetOne;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测任务评定Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface TaskEvaluateMapper 
{
    /**
     * 查询检测任务评定
     * 
     * @param id 检测任务评定主键
     * @return 检测任务评定
     */
    public TaskEvaluate selectTaskEvaluateById(Long id);

    /**
     * 查询检测任务评定列表
     * 
     * @param taskEvaluate 检测任务评定
     * @return 检测任务评定集合
     */
    public List<TaskEvaluate> selectTaskEvaluateList(TaskEvaluate taskEvaluate);

    /**
     * 新增检测任务评定
     * 
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    public int insertTaskEvaluate(TaskEvaluate taskEvaluate);

    /**
     * 修改检测任务评定
     * 
     * @param taskEvaluate 检测任务评定
     * @return 结果
     */
    public int updateTaskEvaluate(TaskEvaluate taskEvaluate);

    /**
     * 删除检测任务评定
     * 
     * @param id 检测任务评定主键
     * @return 结果
     */
    public int deleteTaskEvaluateById(Long id);

    /**
     * 批量删除检测任务评定
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskEvaluateByIds(Long[] ids);

    TaskEvaluate selectByTaskId(@Param("taskId") Long taskId, @Param("type") Integer type);

    List<EvaluateRoadSheetOne> selectByParams(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectAllTasksByParams(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectEnvByParams(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectFocusPicEnv(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectWorkPic(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectQuestionListByParams(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectByCheckRoadScore(TaskEvaluate taskEvaluate);

    List<TaskDetail> selectPicListByParams(TaskEvaluate taskEvaluate);

    List<EvaluateRoadSheetOne> selectByCheckCity(TaskEvaluate taskEvaluate);

    List<TaskEvaluate> selectGroupByTaskDate();

    PieStat selectCircleMapList(Task task);

    List<RankStat> selectGroupByCompanyName(Task task);

    List<RankStat> selectGroupByCompanyNameStat(Task task);

    List<String> selectTunnelNameByParams(EvaluateRoadSheetOne sheet);

    List<TaskEvaluate> countGroupByTaskDate(Task task);

    List<TaskEvaluate> selectTaskDetailCountList(TaskEvaluate taskEvaluate);

    List<RankStat> countGroupByCompanyName(Task task);

    List<Road> selectSpecialCompanyList(TaskEvaluate taskEvaluate);

    List<TaskEvaluate> selectTaskEvaluateByRoadIdList(@Param("roadIdList") List<Long> roadIdList, @Param("task") Task task);

    List<TaskEvaluate> selectByTaskIdList(@Param("taskIdList") List<Long> taskIdList);
}
