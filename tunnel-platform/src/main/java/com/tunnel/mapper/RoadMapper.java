package com.tunnel.mapper;


import com.tunnel.domain.Road;
import com.tunnel.domain.dto.ScRoadQueryDTO;
import com.tunnel.domain.stat.RoadStat;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基础数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface RoadMapper
{
    /**
     * 查询基础数据
     * 
     * @param id 基础数据主键
     * @return 基础数据
     */
    public Road selectScRoadById(Long id);

    /**
     * 查询基础数据列表
     * 
     * @param road 基础数据
     * @return 基础数据集合
     */
    public List<Road> selectScRoadList(Road road);

    /**
     * 新增基础数据
     * 
     * @param road 基础数据
     * @return 结果
     */
    public int insertScRoad(Road road);

    /**
     * 修改基础数据
     * 
     * @param road 基础数据
     * @return 结果
     */
    public int updateScRoad(Road road);

    /**
     * 删除基础数据
     * 
     * @param id 基础数据主键
     * @return 结果
     */
    public int deleteScRoadById(Long id);

    /**
     * 批量删除基础数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScRoadByIds(Long[] ids);

    List<Road> selectAll();

    int batchAdd(@Param("dataList") List<Road> dataList);

    RoadStat selectStatInfo();

    List<Road> listAllCompany(Road road);

    List<Road> selectByRoadIdList(@Param("roadIds") List<Long> roadIds);

    List<Road> queryNoticeUser(@Param("list") List<ScRoadQueryDTO> list);

    List<Road> selectByCompanyNameList(@Param("companyList") List<String> companyList);

    List<Road> selectDistinctCompanyNameList();

}
