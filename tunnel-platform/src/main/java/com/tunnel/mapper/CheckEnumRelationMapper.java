package com.tunnel.mapper;

import com.tunnel.domain.CheckEnumRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公路用地-路域环境对应关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface CheckEnumRelationMapper 
{
    /**
     * 查询公路用地-路域环境对应关系
     * 
     * @param id 公路用地-路域环境对应关系主键
     * @return 公路用地-路域环境对应关系
     */
    public CheckEnumRelation selectCheckEnumRelationById(Long id);

    /**
     * 查询公路用地-路域环境对应关系列表
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 公路用地-路域环境对应关系集合
     */
    public List<CheckEnumRelation> selectCheckEnumRelationList(CheckEnumRelation CheckEnumRelation);

    /**
     * 新增公路用地-路域环境对应关系
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 结果
     */
    public int insertCheckEnumRelation(CheckEnumRelation CheckEnumRelation);

    /**
     * 修改公路用地-路域环境对应关系
     * 
     * @param CheckEnumRelation 公路用地-路域环境对应关系
     * @return 结果
     */
    public int updateCheckEnumRelation(CheckEnumRelation CheckEnumRelation);

    /**
     * 删除公路用地-路域环境对应关系
     * 
     * @param id 公路用地-路域环境对应关系主键
     * @return 结果
     */
    public int deleteCheckEnumRelationById(Long id);

    /**
     * 批量删除公路用地-路域环境对应关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckEnumRelationByIds(Long[] ids);

    List<CheckEnumRelation> selectByRoadItemIdList(@Param("list") List<String> list);
}
