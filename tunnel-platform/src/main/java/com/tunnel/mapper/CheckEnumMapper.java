package com.tunnel.mapper;

import com.tunnel.domain.CheckEnum;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 公路检测类别问题项Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface CheckEnumMapper 
{
    /**
     * 查询公路检测类别问题项
     * 
     * @param id 公路检测类别问题项主键
     * @return 公路检测类别问题项
     */
    public CheckEnum selectCheckEnumById(Long id);

    /**
     * 查询公路检测类别问题项列表
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 公路检测类别问题项集合
     */
    public List<CheckEnum> selectCheckEnumList(CheckEnum checkEnum);

    /**
     * 新增公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    public int insertCheckEnum(CheckEnum checkEnum);

    /**
     * 修改公路检测类别问题项
     * 
     * @param checkEnum 公路检测类别问题项
     * @return 结果
     */
    public int updateCheckEnum(CheckEnum checkEnum);

    /**
     * 删除公路检测类别问题项
     * 
     * @param id 公路检测类别问题项主键
     * @return 结果
     */
    public int deleteCheckEnumById(Long id);

    /**
     * 批量删除公路检测类别问题项
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckEnumByIds(Long[] ids);

    List<String> distinctListByType(CheckEnum checkEnum);

    List<CheckEnum> selectListByIds(@Param("idList") List<Long> idList);

    List<CheckEnum> selectCheckEnumListByList(@Param("checkEnumList") List<CheckEnum> checkEnumList);

    CheckEnum queryByCheckTypeAndCheckProject(@Param("checkType") String checkType, @Param("checkProject") String checkProject);

    BigDecimal queryDefaultScoreList(CheckEnum checkEnum);

    List<CheckEnum> selectCheckEnumByIdList(@Param("idList") List<Long> idList);

    List<CheckEnum> listDistinctCheckContent();

    List<CheckEnum> selectGroupByParams(List<Long> idList);

    BigDecimal queryDefaultEnvScoreList(CheckEnum checkEnum);

    List<BigDecimal> distinctListScoreAll();
}
