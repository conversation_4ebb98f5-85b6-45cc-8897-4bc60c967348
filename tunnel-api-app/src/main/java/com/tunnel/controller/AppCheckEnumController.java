package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.CheckEnumRes;
import com.tunnel.service.CheckEnumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 公路检测类别问题项Controller
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("app/highway/enum")
public class AppCheckEnumController extends BaseController {
    @Autowired
    private CheckEnumService checkEnumService;

    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/distinctListByType")
    public AjaxResult distinctListByType(@RequestBody CheckEnum checkEnum) {
        List<String> list = checkEnumService.distinctListByType(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/queryDefaultScoreList")
    public AjaxResult queryDefaultScoreList(@RequestBody CheckEnum checkEnum) {
        CheckEnumRes res = checkEnumService.queryDefaultScoreList(checkEnum);
        return AjaxResult.success(res);
    }


    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/distinctListScoreAll")
    public AjaxResult distinctListScoreAll() {
        List<BigDecimal> list = checkEnumService.distinctListScoreAll();
        return AjaxResult.success(list);
    }

}
