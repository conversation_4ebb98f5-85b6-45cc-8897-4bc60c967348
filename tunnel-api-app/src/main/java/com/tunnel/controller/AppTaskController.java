package com.tunnel.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskTeamRelation;
import com.tunnel.domain.Team;
import com.tunnel.mapper.TaskTeamRelationMapper;
import com.tunnel.mapper.TeamMapper;
import com.tunnel.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务列表Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/app/highway/task")
public class AppTaskController extends BaseController {
    @Autowired
    private TaskService scTaskService;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TaskTeamRelationMapper taskTeamRelationMapper;

    /**
     * 查询任务列表列表
     */
    @PostMapping("/taskList")
    public AjaxResult list(@RequestBody Task task) {
        if (StringUtils.isEmpty(task.getTaskDate())) {
            task.setTaskDate(DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DatePattern.NORM_MONTH_PATTERN));
        }
        startPage();
        List<Task> list = scTaskService.selectScTaskList(task);
        return AjaxResult.success(list);
    }

    /**
     * 查询任务列表列表
     */
    @PostMapping("/fixedTaskList")
    public AjaxResult fixedTaskList(@RequestBody Task task) {
        if (StringUtils.isEmpty(task.getTaskDate())) {
            task.setTaskDate(DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DatePattern.NORM_MONTH_PATTERN));
        }
        startPage();
        List<Task> list = scTaskService.selectScFixedTaskList(task);
        return AjaxResult.success(list);
    }

    /**
     * 新增检测列表
     */
    @PostMapping("/taskComplete")
    public AjaxResult taskComplete(@RequestBody Task task) {
        scTaskService.taskComplete(task);
        return AjaxResult.success();
    }
    /**
     * 新增检测列表WX
     */
    @PostMapping("/taskCompleteWX")
    public AjaxResult taskCompleteWX(@RequestBody Task task) {
        scTaskService.taskCompleteWX(task);
        return AjaxResult.success();
    }


    /**
     * 查询基础数据列表-all
     */
    @GetMapping("/listAllMonth")
    public AjaxResult listAllMonth() {
        List<Task> list = scTaskService.listAllMonthDTO();
        return AjaxResult.success(list);
    }

    @PostMapping("/updateTask")
    public AjaxResult updateTask(@RequestBody Task task) throws IOException {
        scTaskService.updateScTask(task);
        return AjaxResult.success(scTaskService.selectScTaskById(task.getId()));
    }


    @PostMapping("/taskDetailList")
    public AjaxResult taskDetailList(@RequestBody Task task) {
        return AjaxResult.success(scTaskService.taskDetailList(task));
    }

}
