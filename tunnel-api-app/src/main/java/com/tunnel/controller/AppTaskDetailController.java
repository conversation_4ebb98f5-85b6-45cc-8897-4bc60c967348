package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.dto.TaskDetailQueryDTO;
import com.tunnel.service.TaskDetailService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Enumeration;
import java.util.List;

/**
 * 检测列表Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/app/highway/detail")
public class AppTaskDetailController extends BaseController {
    @Resource
    private TaskDetailService scTaskDetailService;

    /**
     * 查询检测列表列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody TaskDetail taskDetail, HttpServletRequest request) {
        taskDetail.setCreator(SecurityUtils.getLoginUser().getUserId());
        startPage();
        List<TaskDetail> list = scTaskDetailService.selectScTaskDetailList(taskDetail);
        return AjaxResult.success(list);
    }


    /**
     * 新增检测列表
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody @Validated TaskDetail taskDetail) throws IOException {
        scTaskDetailService.saveOrUpdate(taskDetail);
        return AjaxResult.success();
    }


    /**
     * 删除
     */
    @PostMapping("/deleteById")
    public AjaxResult deleteById(@RequestBody TaskDetail taskDetail) {
        scTaskDetailService.deleteById(taskDetail);
        return AjaxResult.success();
    }

    @GetMapping("/getByTaskId")
    public AjaxResult getByTaskId(@RequestParam(value = "id") Long id) {
        List<TaskDetailQueryDTO> list = scTaskDetailService.selectScTaskDetailListByTaskId(id);
        return AjaxResult.success(list);
    }

    @GetMapping("/fixedTask")
    public AjaxResult fixedTask(@RequestParam(value = "taskId") Long taskId) {
        return AjaxResult.success(scTaskDetailService.fixedTask(taskId));
    }
}
