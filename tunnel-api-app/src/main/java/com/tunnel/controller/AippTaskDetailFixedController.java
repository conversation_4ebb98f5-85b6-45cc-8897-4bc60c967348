package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.domain.TaskDetailFixed;
import com.tunnel.service.TaskDetailFixedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app/highway/detail/fixed")
public class AippTaskDetailFixedController extends BaseController {

    @Autowired
    private TaskDetailFixedService taskDetailFixedService;

    /**
     * 修改整改信息
     */
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody TaskDetailFixed taskDetailFixed) {
        return toAjax(taskDetailFixedService.updateTaskDetailFixed(taskDetailFixed));
    }

    /**
     * 新增整改信息
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TaskDetailFixed taskDetailFixed) {
        return toAjax(taskDetailFixedService.insertTaskDetailFixed(taskDetailFixed));
    }
}
