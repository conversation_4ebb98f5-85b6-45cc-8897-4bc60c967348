package com.tunnel.web.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.rits.cloning.Cloner;
import com.tunnel.domain.Road;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.mapper.TaskMapper;
import com.tunnel.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class RoadTask {

    @Resource
    private RoadMapper roadMapper;
    @Resource
    private TaskService taskService;

    /**
     * 每月1号生成当月检测任务
     */
    @Scheduled(cron = "0 0 1 1 * ?")
    public void initTaskCheckByMonth() {
        log.info("执行初始化本月的道路检测任务 任务的时间：{}" , new Date(System.currentTimeMillis()));
        List<Road> roadList= roadMapper.selectAll();
        if(CollectionUtils.isEmpty(roadList)){
            log.error("执行初始化本月的道路检测任务,道路信息为空");
            return;
        }
        DateTime nextMonth = DateUtil.offsetMonth(DateUtil.date(), 0);
        String taskDate=DateUtil.format(nextMonth,"yyyy-MM");
//        List<Long> roadIdList=taskMapper.selectByTaskDate(taskDate);
        List<Road> addList= Lists.newArrayList();
//        for (Road road : roadList) {
//            for (int i = 1; i < 8; i++) {
//                Road task = Cloner.standard().deepClone(road);
//                task.setRoadRemark(task.getRemark());
//                task.setTaskDate(taskDate+"-"+i);
//                addList.add(task);
//            }
//        }
        for (Road road : roadList) {
            Road task = Cloner.standard().deepClone(road);
            task.setRoadRemark(task.getRemark());
            task.setTaskDate(taskDate);
            addList.add(task);
        }
        taskService.batchAdd(addList);
        log.info("执行初始化本月的道路检测任务,执行成功");
    }
}
