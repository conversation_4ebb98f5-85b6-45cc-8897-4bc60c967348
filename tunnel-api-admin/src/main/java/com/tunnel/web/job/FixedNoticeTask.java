package com.tunnel.web.job;

import cn.hutool.core.date.DateUtil;
import com.tunnel.common.utils.sms.AliyunSmsUtils;
import com.tunnel.domain.dto.NoticeTaskDetailDTO;
import com.tunnel.service.TaskDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: 检测任务短信通知定时任务
 */
@Component
@Slf4j
public class FixedNoticeTask {

    @Resource
    private AliyunSmsUtils aliyunSmsUtils;

    @Resource
    private TaskDetailService taskDetailService;

    @Scheduled(cron = "0 0 10 * * ?")
    public void noticeTask() {
        log.info("执行检测数据整改通知定时任务，开始时间：{}" , DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        //查询需要通知的检测项
        List<NoticeTaskDetailDTO> noticeTaskDetailDTOS =  taskDetailService.selectNoticeTaskDetail();
        if (CollectionUtils.isEmpty(noticeTaskDetailDTOS)) {
            log.warn("当前没有需要通知的检测任务");
            return;
        }
        noticeTaskDetailDTOS.forEach(item -> {
            String templateParam = String.format("{\"name\":\"%s\",\"roadName\":\"%s\",\"endDate\":\"%s\"}",
                    "道路养护", item.getRoadName(), DateUtil.format(item.getFixedEndDate(), "yyyy-MM-dd"));
            aliyunSmsUtils.sendMessage("武汉精视遥测科技", "SMS_483485203",
                    item.getNotifyPhone(), templateParam);
        });
        log.info("执行检测数据整改通知定时任务，执行成功，今日发送短信条数：{}",  noticeTaskDetailDTOS.size());
    }
}
