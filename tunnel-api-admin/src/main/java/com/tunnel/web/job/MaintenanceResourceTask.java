package com.tunnel.web.job;

import cn.hutool.core.date.DateUtil;
import com.tunnel.common.core.domain.entity.SysDept;
import com.tunnel.domain.MaintenanceResource;
import com.tunnel.domain.Road;
import com.tunnel.service.MaintenanceResourceService;
import com.tunnel.service.RoadService;
import com.tunnel.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 每月定时生产投入资源数据
 */

@Component
@Slf4j
public class MaintenanceResourceTask {

    @Resource
    private RoadService scRoadService;

    @Resource
    private MaintenanceResourceService maintenanceResourceService;

    @Resource
    private ISysDeptService iSysDeptService;


    /**
     * 每月21号 00:10:00
     */
//    @Scheduled(cron = "0 10 0 21 * ?")
    public void initMaintenanceResourceByMonth() {
        log.info("执行初始化本月的投入数据任务，开始时间：{}" , DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        //查询所有的管养公司
        List<Road> allCompanyList =  scRoadService.listAllCompany(new Road());
        if (CollectionUtils.isEmpty(allCompanyList)) {
            log.warn("管养公司查询为空，不初始化投入数据");
            return;
        }
        //批量查询一下本月哪些已经有了
        List<String> companyNameList = allCompanyList.stream().map(Road::getCompanyName).distinct().collect(Collectors.toList());
        String addTimePrefix = DateUtil.format(DateUtil.date(), "yyyy-MM");
        List<MaintenanceResource> exitDataList = maintenanceResourceService.selectMaintenanceResourceByCompanyList(addTimePrefix, companyNameList);
        Map<String,MaintenanceResource> exitDataMap = exitDataList.stream()
                .collect(Collectors.toMap(MaintenanceResource::getCompanyName, Function.identity(), (o1, o2) -> o1));
        List<Road> needInitDataList = allCompanyList.stream().filter(dto -> !exitDataMap.containsKey(dto.getCompany())).collect(Collectors.toList());
        List<SysDept> deptList = iSysDeptService.selectAllDeptList();
        Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId, (o1, o2) -> o1));
        if (!CollectionUtils.isEmpty(needInitDataList)) {
            List<MaintenanceResource> addList = needInitDataList.stream().map(dto -> {
                MaintenanceResource resource = new MaintenanceResource();
                resource.setCompany(dto.getCompany());
                resource.setCompanyName(dto.getCompanyName());
                resource.setAddTime(DateUtil.date());
                resource.setRemark("初始化数据");
                resource.setDeptId(deptMap.getOrDefault(dto.getCompanyName(), 0L));
                resource.setUserId(0L);
                return resource;
            }).collect(Collectors.toList());
            maintenanceResourceService.batchInsertResources(addList);
            log.info("执行初始化本月的投入数据{}条！", addList.size());
        }
        log.info("执行初始化本月的投入数据任务,执行成功");
    }
}
