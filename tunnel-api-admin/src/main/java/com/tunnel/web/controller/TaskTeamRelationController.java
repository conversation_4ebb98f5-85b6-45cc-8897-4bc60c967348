package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.domain.TaskTeamRelation;
import com.tunnel.service.TaskTeamRelationService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 任务和检测组关系Controller
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@RestController
@RequestMapping("/highway/relation")
public class TaskTeamRelationController extends BaseController {
    @Autowired
    private TaskTeamRelationService taskTeamRelationService;

    /**
     * 查询任务和检测组关系列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TaskTeamRelation taskTeamRelation) {
        startPage();
        List<TaskTeamRelation> list = taskTeamRelationService.selectTaskTeamRelationList(taskTeamRelation);
        return getDataTable(list);
    }

    /**
     * 导出任务和检测组关系列表
     */
    @Log(title = "任务和检测组关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskTeamRelation taskTeamRelation) {
        List<TaskTeamRelation> list = taskTeamRelationService.selectTaskTeamRelationList(taskTeamRelation);
        ExcelUtil<TaskTeamRelation> util = new ExcelUtil<TaskTeamRelation>(TaskTeamRelation.class);
        util.exportExcel(response, list, "任务和检测组关系数据");
    }

    /**
     * 获取任务和检测组关系详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskTeamRelationService.selectTaskTeamRelationById(id));
    }

    /**
     * 新增任务和检测组关系
     */
    @Log(title = "任务和检测组关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskTeamRelation taskTeamRelation) {
        return toAjax(taskTeamRelationService.insertTaskTeamRelation(taskTeamRelation));
    }

    /**
     * 修改任务和检测组关系
     */
    @Log(title = "任务和检测组关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskTeamRelation taskTeamRelation) {
        return toAjax(taskTeamRelationService.updateTaskTeamRelation(taskTeamRelation));
    }

    /**
     * 删除任务和检测组关系
     */
    @Log(title = "任务和检测组关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskTeamRelationService.deleteTaskTeamRelationByIds(ids));
    }
}
