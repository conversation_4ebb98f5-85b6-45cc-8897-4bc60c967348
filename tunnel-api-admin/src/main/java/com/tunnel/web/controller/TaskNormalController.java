package com.tunnel.web.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.dto.TaskDetailCountRes;
import com.tunnel.domain.dto.TaskNormalQueryDTO;
import com.tunnel.service.TaskNormalService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/highway/taskNormal")
public class TaskNormalController extends BaseController {

    @Resource
    private TaskNormalService taskNormalService;

//    @PreAuthorize("@ss.hasPermi('highway:taskNormal:add')")
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/batchAdd", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult doUploadFile(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(taskNormalService.batchAdd(file));
    }

    /**
     * 按公司汇总后的列表
     */
//    @PreAuthorize("@ss.hasPermi('highway:taskNormal:list')")
    @GetMapping("/listByCompanyPage")
    public TableDataInfo listByPage(TaskNormalQueryDTO queryDTO) {
        TaskDetailCountRes res = taskNormalService.listByCompanyPage(queryDTO);
        return getDataTable(res.getResult(), res.getTotal());
    }


    @ApiOperation(value = "根据id批量查询")
    @PostMapping(value = "/queryByIdList")
    public AjaxResult queryByIdList(@RequestBody List<Long> idList) {
        return AjaxResult.success(taskNormalService.queryByIdList(idList));
    }

    @ApiOperation(value = "根据taskDate删除")
    @GetMapping("/deleteByTaskDate")
    public AjaxResult deleteByTaskDate(@RequestParam("taskDate") String taskDate) {
        return AjaxResult.success(taskNormalService.deleteByTaskDate(taskDate));
    }
}
