package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.web.job.FixedNoticeTask;
import com.tunnel.web.job.MaintenanceResourceTask;
import com.tunnel.web.job.RoadTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 定时任务Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Api("定时任务")
@RestController
@RequestMapping("/highway/job")
public class JobController extends BaseController {

    @Resource
    private RoadTask roadTask;

    @Resource
    private MaintenanceResourceTask maintenanceResourceTask;

    @Resource
    private FixedNoticeTask fixedNoticeTask;

    /**
     * 查询任务列表列表
     */

    @ApiOperation(value = "每月一号凌晨执行初始化本月的道路检测任务")
    @Anonymous
    @GetMapping("/initTaskCheckByMonth")
    public void initTaskCheckByMonth() {
        roadTask.initTaskCheckByMonth();
    }


    @ApiOperation(value = "每月21号初始化本月的投入数据")
    @Anonymous
    @GetMapping("/initMaintenanceResourceByMonth")
    public void initMaintenanceResourceByMonth() {
        maintenanceResourceTask.initMaintenanceResourceByMonth();
    }

    @ApiOperation(value = "每日10点通知未整改检测任务")
    @Anonymous
    @GetMapping("/noticeTask")
    public void noticeTask() {
        fixedNoticeTask.noticeTask();
    }

}
