package com.tunnel.web.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.MaintenanceResource;
import com.tunnel.service.MaintenanceResourceService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * 管养资源投入记录 控制器
 */
@RestController
@RequestMapping("/highway/maintenanceResource")
public class MaintenanceResourceController extends BaseController {
    
    @Resource
    private MaintenanceResourceService maintenanceResourceService;

    /**
     * 查询管养资源投入记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MaintenanceResource maintenanceResource) {
        startPage();
        List<MaintenanceResource> list = maintenanceResourceService.selectMaintenanceResourceList(maintenanceResource);
        return getDataTable(list);
    }

    /**
     * 获取管养资源投入记录详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(maintenanceResourceService.selectMaintenanceResourceById(id));
    }

    /**
     * 新增管养资源投入记录
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody MaintenanceResource maintenanceResource) {
        return toAjax(maintenanceResourceService.insertMaintenanceResource(maintenanceResource));
    }

    /**
     * 修改管养资源投入记录
     */
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody MaintenanceResource maintenanceResource) {
        return toAjax(maintenanceResourceService.updateMaintenanceResource(maintenanceResource));
    }

    /**
     * 删除管养资源投入记录
     */
    @PostMapping("/deleteByIds")
    public AjaxResult remove(@RequestBody List<Long> ids) {
        return toAjax(maintenanceResourceService.deleteMaintenanceResourceByIds(ids));
    }

    @GetMapping("/queryDataBoardCount")
    public AjaxResult queryDataBoardCount() {
        return success(maintenanceResourceService.queryDataBoardCount());
    }
}