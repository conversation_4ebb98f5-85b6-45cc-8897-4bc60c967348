package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.domain.MapQueryDTO;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.stat.ResultStat;
import com.tunnel.domain.stat.ResultStatNew;
import com.tunnel.service.StatService;
import org.apache.poi.xssf.model.MapInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 统计分析Controller
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@RestController
@RequestMapping("/highway/stat")
public class StatController extends BaseController {
    @Resource
    private StatService statService;

    /**
     * 查看评分结果
     */
    @PostMapping(value = "/queryStatInfo")
    public AjaxResult queryStatInfo(Task task) {
        ResultStat result;
        if (Objects.equals(1, task.getCountType())) {
            //按照数量统计
            result = statService.queryStatInfoCount(task);
        } else {
            result = statService.queryStatInfo(task);
        }
        return AjaxResult.success(result);
    }


    /**
     * 查看评分结果
     */
    @PostMapping(value = "/queryStatInfoNew")
    public AjaxResult queryStatInfoNew(@RequestBody Task task) {
        //打印接口响应时间
        Long startTime = System.currentTimeMillis();
        ResultStatNew result = statService.queryStatInfoCountNew(task);
        System.out.println("接口响应时间：" + (System.currentTimeMillis() - startTime));
        return AjaxResult.success(result);
    }

    /**
     * 查看地图
     */
    @PostMapping(value = "/queryMapInfo")
    public AjaxResult queryMapInfo(@RequestBody MapQueryDTO mapQueryDTO) {
        Long startTime = System.currentTimeMillis();
        List<TaskDetail> list= statService.queryMapInfo(mapQueryDTO);
        System.out.println("地图接口响应时间：" + (System.currentTimeMillis() - startTime));
        return AjaxResult.success(list);
    }


}
