package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Task;
import com.tunnel.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 任务列表Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/highway/task")
public class TaskController extends BaseController {
    @Autowired
    private TaskService scTaskService;

    /**
     * 查询任务列表列表
     */
    @PreAuthorize("@ss.hasPermi('highway:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(Task task) {
        startPage();
        List<Task> list = scTaskService.selectScTaskList(task);
        return getDataTable(list);
    }

    /**
     * 查询基础数据列表-all
     */
    @GetMapping("/listAll")
    public List<Task> listAll(Task task) {
        List<Task> list = scTaskService.selectScTaskList(task);
        return list;
    }

    /**
     * 导出任务列表列表
     */
    @PreAuthorize("@ss.hasPermi('highway:task:export')")
    @Log(title = "任务列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task) {
        List<Task> list = scTaskService.selectScTaskList(task);
        for (Task task1 : list) {
            //0.初始化,1.检测完成
            if(Objects.equals(task1.getStatus(),0)){
                task1.setStatusName("未检测");
            }else{
                task1.setStatusName("检测完成");
            }
        }
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        util.exportExcel(response, list, "任务列表数据");
    }

    /**
     * 获取任务列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(scTaskService.selectScTaskById(id));
    }

    /**
     * 新增任务列表
     */
    @PreAuthorize("@ss.hasPermi('highway:task:add')")
    @Log(title = "任务列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Task task) {
        return toAjax(scTaskService.insertScTask(task));
    }

    /**
     * 修改任务列表
     */
    @PreAuthorize("@ss.hasPermi('highway:task:edit')")
    @Log(title = "任务列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Task task) throws IOException {
        return toAjax(scTaskService.updateScTask(task));
    }

    /**
     * 删除任务列表
     */
    @PreAuthorize("@ss.hasPermi('highway:task:remove')")
    @Log(title = "任务列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scTaskService.deleteScTaskByIds(ids));
    }


    /**
     * 查询基础数据列表-all
     */
    @GetMapping("/listAllMonth")
    public List<String> listAllMonth() {
        List<String> list = scTaskService.listAllMonth();
        return list;
    }


    /**
     * 查询基础数据列表-all
     */
    @GetMapping("/listAllMonthDTO")
    public List<Task> listAllMonthDTO() {
        List<Task> list = scTaskService.listAllMonthDTO();
        return list;
    }
}
