package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.CheckEnumRes;
import com.tunnel.service.CheckEnumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 公路检测类别问题项Controller
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("/highway/enum")
public class CheckEnumController extends BaseController {
    @Autowired
    private CheckEnumService checkEnumService;

    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/distinctListByType")
//    @PreAuthorize("@ss.hasPermi('highway:enum:list')")
    public AjaxResult distinctListByType(@RequestBody CheckEnum checkEnum) {
        List<String> list = checkEnumService.distinctListByType(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/queryDefaultScoreList")
    public AjaxResult queryDefaultScoreList(@RequestBody CheckEnum checkEnum) {
        CheckEnumRes res = checkEnumService.queryDefaultScoreList(checkEnum);
        return AjaxResult.success(res);
    }


    /**
     * 查询公路检测类别问题项列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckEnum checkEnum) {
        startPage();
        List<CheckEnum> list = checkEnumService.selectCheckEnumList(checkEnum);
        return getDataTable(list);
    }

    /**
     * 导出公路检测类别问题项列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:export')")
    @Log(title = "公路检测类别问题项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumService.selectCheckEnumList(checkEnum);
        ExcelUtil<CheckEnum> util = new ExcelUtil<CheckEnum>(CheckEnum.class);
        util.exportExcel(response, list, "公路检测类别问题项数据");
    }

    /**
     * 获取公路检测类别问题项详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkEnumService.selectCheckEnumById(id));
    }

    /**
     * 新增公路检测类别问题项
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:add')")
    @Log(title = "公路检测类别问题项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckEnum checkEnum) {
        return toAjax(checkEnumService.insertCheckEnum(checkEnum));
    }

    /**
     * 修改公路检测类别问题项
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:edit')")
    @Log(title = "公路检测类别问题项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckEnum checkEnum) {
        return toAjax(checkEnumService.updateCheckEnum(checkEnum));
    }

    /**
     * 删除公路检测类别问题项
     */
    @PreAuthorize("@ss.hasPermi('highway:enum:remove')")
    @Log(title = "公路检测类别问题项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkEnumService.deleteCheckEnumByIds(ids));
    }



    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/listAll")
    public AjaxResult listAll(@RequestBody CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumService.selectCheckEnumList(checkEnum);
        return AjaxResult.success(list);
    }


    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/listDistinctCheckContent")
    public AjaxResult listDistinctCheckContent() {
        List<CheckEnum> list = checkEnumService.listDistinctCheckContent();
        return AjaxResult.success(list);
    }

    /**
     * 查询公路检测类别问题项列表
     */
    @PostMapping("/distinctListScoreAll")
    public AjaxResult distinctListScoreAll() {
        List<BigDecimal> list = checkEnumService.distinctListScoreAll();
        return AjaxResult.success(list);
    }
}
