package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckEnumRelation;
import com.tunnel.service.CheckEnumRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公路用地-路域环境对应关系Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/highway/enumRelation")
public class CheckEnumRelationController extends BaseController
{
    @Autowired
    private CheckEnumRelationService checkEnumRelationService;

    /**
     * 查询公路用地-路域环境对应关系列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckEnumRelation CheckEnumRelation)
    {
        startPage();
        List<CheckEnumRelation> list = checkEnumRelationService.selectCheckEnumRelationList(CheckEnumRelation);
        return getDataTable(list);
    }

    /**
     * 导出公路用地-路域环境对应关系列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:export')")
    @Log(title = "公路用地-路域环境对应关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckEnumRelation checkEnumRelation)
    {
        List<CheckEnumRelation> list = checkEnumRelationService.selectCheckEnumRelationList(checkEnumRelation);
        ExcelUtil<CheckEnumRelation> util = new ExcelUtil<CheckEnumRelation>(CheckEnumRelation.class);
        util.exportExcel(response, list, "公路用地-路域环境对应关系数据");
    }

    /**
     * 获取公路用地-路域环境对应关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkEnumRelationService.selectCheckEnumRelationById(id));
    }

    /**
     * 新增公路用地-路域环境对应关系
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:add')")
    @Log(title = "公路用地-路域环境对应关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckEnumRelation checkEnumRelation)
    {
        return toAjax(checkEnumRelationService.insertCheckEnumRelation(checkEnumRelation));
    }

    /**
     * 修改公路用地-路域环境对应关系
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:edit')")
    @Log(title = "公路用地-路域环境对应关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckEnumRelation checkEnumRelation)
    {
        return toAjax(checkEnumRelationService.updateCheckEnumRelation(checkEnumRelation));
    }

    /**
     * 删除公路用地-路域环境对应关系
     */
    @PreAuthorize("@ss.hasPermi('highway:enumRelation:remove')")
    @Log(title = "公路用地-路域环境对应关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkEnumRelationService.deleteCheckEnumRelationByIds(ids));
    }
}
