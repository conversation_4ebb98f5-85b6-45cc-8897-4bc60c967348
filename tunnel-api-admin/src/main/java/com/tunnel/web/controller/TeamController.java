package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.domain.Team;
import com.tunnel.service.TeamService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 检测组和用户关系Controller
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@RestController
@RequestMapping("/highway/team")
public class TeamController extends BaseController {
    @Autowired
    private TeamService teamService;

    /**
     * 查询检测组和用户关系列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Team team) {
        startPage();
        List<Team> list = teamService.selectTeamList(team);
        return getDataTable(list);
    }


    @GetMapping("/listAll")
    public List<Team> listAll() {
        List<Team> list = teamService.listAll();
        return list;
    }

    /**
     * 导出检测组和用户关系列表
     */
    @Log(title = "检测组和用户关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Team team) {
        List<Team> list = teamService.selectTeamList(team);
        ExcelUtil<Team> util = new ExcelUtil<Team>(Team.class);
        util.exportExcel(response, list, "检测组和用户关系数据");
    }

    /**
     * 获取检测组和用户关系详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(teamService.selectTeamById(id));
    }

    /**
     * 新增检测组和用户关系
     */
    @Log(title = "检测组和用户关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Team team) {
        return toAjax(teamService.insertTeam(team));
    }

    /**
     * 修改检测组和用户关系
     */
    @Log(title = "检测组和用户关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Team team) {
        return toAjax(teamService.updateTeam(team));
    }

    /**
     * 删除检测组和用户关系
     */
    @Log(title = "检测组和用户关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(teamService.deleteTeamByIds(ids));
    }
}
