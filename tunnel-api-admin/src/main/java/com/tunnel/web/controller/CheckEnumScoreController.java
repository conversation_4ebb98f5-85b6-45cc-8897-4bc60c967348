package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckEnumScore;
import com.tunnel.service.CheckEnumScoreService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 检测类别问题项-最大分值Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/highway/enumScore")
public class CheckEnumScoreController extends BaseController
{
    @Autowired
    private CheckEnumScoreService checkEnumScoreService;

    /**
     * 查询检测类别问题项-最大分值列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckEnumScore checkEnumScore)
    {
        startPage();
        List<CheckEnumScore> list = checkEnumScoreService.selectCheckEnumScoreList(checkEnumScore);
        return getDataTable(list);
    }

    /**
     * 导出检测类别问题项-最大分值列表
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:export')")
    @Log(title = "检测类别问题项-最大分值", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckEnumScore checkEnumScore)
    {
        List<CheckEnumScore> list = checkEnumScoreService.selectCheckEnumScoreList(checkEnumScore);
        ExcelUtil<CheckEnumScore> util = new ExcelUtil<CheckEnumScore>(CheckEnumScore.class);
        util.exportExcel(response, list, "检测类别问题项-最大分值数据");
    }

    /**
     * 获取检测类别问题项-最大分值详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkEnumScoreService.selectCheckEnumScoreById(id));
    }

    /**
     * 新增检测类别问题项-最大分值
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:add')")
    @Log(title = "检测类别问题项-最大分值", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckEnumScore checkEnumScore)
    {
        return toAjax(checkEnumScoreService.insertCheckEnumScore(checkEnumScore));
    }

    /**
     * 修改检测类别问题项-最大分值
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:edit')")
    @Log(title = "检测类别问题项-最大分值", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckEnumScore checkEnumScore)
    {
        return toAjax(checkEnumScoreService.updateCheckEnumScore(checkEnumScore));
    }

    /**
     * 删除检测类别问题项-最大分值
     */
    @PreAuthorize("@ss.hasPermi('highway:enumScore:remove')")
    @Log(title = "检测类别问题项-最大分值", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkEnumScoreService.deleteCheckEnumScoreByIds(ids));
    }
}
