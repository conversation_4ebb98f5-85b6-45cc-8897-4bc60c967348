package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.FileRecord;
import com.tunnel.service.FileRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @author: 专业bug开发（kk）
 * @date: 2025年04月18日 14:18
 */
@RestController
@RequestMapping("/highway/fileRecord")
public class FileRecordController extends BaseController {

    @Resource
    private FileRecordService fileRecordService;

    /**
     * 列表分页查询
     * @param fileRecord
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(FileRecord fileRecord) {
        startPage();
        List<FileRecord> list = fileRecordService.selectFileRecordList(fileRecord);
        return getDataTable(list);
    }

    @Log(title = "资源文件上传", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FileRecord fileRecord) {
        return toAjax(fileRecordService.insertFileRecord(fileRecord));
    }

    @Log(title = "资源文件修改", businessType = BusinessType.INSERT)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FileRecord fileRecord) {
        return toAjax(fileRecordService.updateFileRecord(fileRecord));
    }


    @Log(title = "删除资源文件", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public AjaxResult remove(@RequestBody List<Long> ids) {
        return toAjax(fileRecordService.deleteFileRecordByIds(ids));
    }
}
