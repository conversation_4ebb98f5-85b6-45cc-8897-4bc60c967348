package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.TaskDetail;
import com.tunnel.domain.dto.TaskDetailCountRes;
import com.tunnel.domain.dto.TaskGroupQueryDTO;
import com.tunnel.domain.vo.TaskDetailCountVO;
import com.tunnel.service.TaskDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/**
 * 检测列表Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/highway/detail")
public class TaskDetailController extends BaseController {
    @Resource
    private TaskDetailService scTaskDetailService;

    /**
     * 查询检测列表列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskDetail taskDetail) {
        startPage();
        List<TaskDetail> list = scTaskDetailService.selectScTaskDetailList(taskDetail);
        return getDataTable(list);
    }

    /**
     * 导出检测列表列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:export')")
    @Log(title = "检测列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskDetail taskDetail) {
        List<TaskDetail> list = scTaskDetailService.selectScTaskDetailList(taskDetail);
        ExcelUtil<TaskDetail> util = new ExcelUtil<TaskDetail>(TaskDetail.class);
        util.exportExcel(response, list, "检测列表数据");
    }

    @PreAuthorize("@ss.hasPermi('highway:detail:export')")
    @Log(title = "检测列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportNew")
    public void exportNew(HttpServletResponse response, TaskDetail taskDetail) throws UnsupportedEncodingException {
        scTaskDetailService.exportNew(response, taskDetail);
    }

    /**
     * 获取检测列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(scTaskDetailService.selectScTaskDetailById(id));
    }

    /**
     * 新增检测列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:add')")
    @Log(title = "检测列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskDetail taskDetail) {
        return toAjax(scTaskDetailService.insertScTaskDetail(taskDetail));
    }

    /**
     * 修改检测列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:edit')")
    @Log(title = "检测列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskDetail taskDetail) {
        return toAjax(scTaskDetailService.updateScTaskDetail(taskDetail));
    }

    /**
     * 删除检测列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detail:remove')")
    @Log(title = "检测列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scTaskDetailService.deleteScTaskDetailByIds(ids));
    }

    /**
     * 新增检测列表
     */
    @PostMapping("/saveOrUpdate")
    @PreAuthorize("@ss.hasPermi('highway:detail:add')")
    public AjaxResult saveOrUpdate(@RequestBody @Validated TaskDetail taskDetail) throws IOException {
        scTaskDetailService.saveOrUpdate(taskDetail);
        return AjaxResult.success();
    }

    /**
     * 按公司汇总后的列表
     * @param queryDTO
     * @return
     */
    @PreAuthorize("@ss.hasPermi('highway:detailGroup:list')")
    @GetMapping("/listByPage")
    public TableDataInfo listByPage(TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = scTaskDetailService.selectScTaskDetailNumWithCompany(queryDTO);
        return getDataTable(res.getResult(), res.getTotal());
    }

    /**
     * 按公司汇总后的列表
     */
    @PreAuthorize("@ss.hasPermi('highway:detailGroup:export')")
    @Log(title = "按公司汇总后的列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetailGroup")
    public void exportDetailGroup(HttpServletResponse response, TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = scTaskDetailService.exportDetailGroup(queryDTO);
        ExcelUtil<TaskDetailCountVO> util = new ExcelUtil<>(TaskDetailCountVO.class);
        util.exportExcel(response, res.getResult(), "汇总后的检测数据");
    }

    @PostMapping("/oneClickConfirm")
    @PreAuthorize("@ss.hasPermi('highway:detail:confirmed')")
    public AjaxResult oneClickConfirm(@RequestBody List<Long> taskIdList){
        return AjaxResult.success(scTaskDetailService.oneClickConfirm(taskIdList));
    }

    /**
     * 按公司汇总
     */
//    @PreAuthorize("@ss.hasPermi('highway:listByCompany:list')")
    @GetMapping("/listByCompany")
    public TableDataInfo listByCompany(TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = scTaskDetailService.listByCompany(queryDTO);
        return getDataTable(res.getResult(), res.getTotal());
    }

    @PreAuthorize("@ss.hasPermi('highway:detailGroup:export')")
    @Log(title = "按公司汇总后的列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportByCompany")
    public void exportByCompany(HttpServletResponse response, TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = scTaskDetailService.exportByCompany(queryDTO);
        ExcelUtil<TaskDetailCountVO> util = new ExcelUtil<>(TaskDetailCountVO.class);
        util.exportExcel(response, res.getResult(), "按公司汇总后的检测数据");
    }

    /**
     * 按路段汇总
     */
//    @PreAuthorize("@ss.hasPermi('highway:listByCompany:list')")
    @GetMapping("/listByRoad")
    public TableDataInfo listByRoad(TaskGroupQueryDTO queryDTO) {
        TaskDetailCountRes res = scTaskDetailService.listByRoad(queryDTO);
        return getDataTable(res.getResult(), res.getTotal());
    }

}
