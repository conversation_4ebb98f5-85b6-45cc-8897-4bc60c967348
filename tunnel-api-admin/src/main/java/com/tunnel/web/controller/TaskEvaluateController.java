package com.tunnel.web.controller;

import com.tunnel.WebSocketServer;
import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.TaskEvaluate;
import com.tunnel.service.TaskEvaluateService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.HashMap;

/**
 * 检测任务评定Controller
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@RestController
@RequestMapping("/highway/evaluate")
public class TaskEvaluateController extends BaseController {
    @Autowired
    private TaskEvaluateService taskEvaluateService;

    /**
     * 查询检测任务评定列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TaskEvaluate taskEvaluate) {
        startPage();
        List<TaskEvaluate> list = taskEvaluateService.selectTaskEvaluateList(taskEvaluate);
        return getDataTable(list);
    }

    /**
     * 导出检测任务评定列表
     */
    @PreAuthorize("@ss.hasPermi('highway:evaluate:export')")
    @Log(title = "检测任务评定", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskEvaluate taskEvaluate) {
        List<TaskEvaluate> list = taskEvaluateService.selectTaskEvaluateList(taskEvaluate);
        ExcelUtil<TaskEvaluate> util = new ExcelUtil<>(TaskEvaluate.class);
        util.exportExcel(response, list, "检测任务评定数据");
    }

    /**
     * 导出检测任务评定列表
     */
    @PostMapping("/exportWord")
    public void exportWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);
        taskEvaluateService.exportWord(response, taskEvaluate);
    }

    /**
     * 导出检测任务评定列表
     */
    @PostMapping("/exportPicWord")
    public void exportPicWord(HttpServletResponse response, TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException {
        String sourceDirPath = "/tmp/taskEvaluate/" + System.currentTimeMillis() + "/";
        taskEvaluate.setSourcePath(sourceDirPath);
        taskEvaluate.setStatus(null);
        taskEvaluate.setScore(null);
        taskEvaluateService.exportPicWord(response, taskEvaluate);
    }


    /**
     * 导出文件压缩包
     */
    @PostMapping("/exportZip")
    public void exportZip(HttpServletResponse response, @RequestBody TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException, ExecutionException, InterruptedException {
        taskEvaluateService.exportZip(response, taskEvaluate);
    }

    /**
     * 导出文件压缩包-全部照片sheet
     */
    @PostMapping("/exportSheetPicZip")
    public void exportSheetPicZip(HttpServletResponse response, @RequestBody TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException, ExecutionException, InterruptedException {
        taskEvaluateService.exportSheetPicZip(response, taskEvaluate);
    }

    /**
     * 导出文件压缩包-全部照片sheet--服务区
     */
    @PostMapping("/exportSheetServiceAreaPicZip")
    public void exportSheetServiceAreaPicZip(HttpServletResponse response, @RequestBody TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException, ExecutionException, InterruptedException {
        taskEvaluateService.exportSheetServiceAreaPicZip(response, taskEvaluate);
    }

    /**
     * 导出照片压缩包
     */
    @PostMapping("/exportPicZip")
    public void exportPicZip(HttpServletResponse response, @RequestBody TaskEvaluate taskEvaluate) throws IOException, InvalidFormatException, ExecutionException, InterruptedException {
        taskEvaluate.setUserId(getUserId());
        taskEvaluateService.exportPicZip(response, taskEvaluate);
    }

    /**
     * 获取检测任务评定详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskEvaluateService.selectTaskEvaluateById(id));
    }

    /**
     * 新增检测任务评定
     */
    @Log(title = "检测任务评定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskEvaluate taskEvaluate) {
        return toAjax(taskEvaluateService.insertTaskEvaluate(taskEvaluate));
    }

    @PostMapping(value = "/handleAddAll")
    public AjaxResult handleAddAll(@RequestBody TaskEvaluate taskEvaluate) {
        taskEvaluateService.handleAddAll(taskEvaluate);
        return AjaxResult.success();
    }


    /**
     * 修改检测任务评定
     */
    @Log(title = "检测任务评定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskEvaluate taskEvaluate) {
        return toAjax(taskEvaluateService.updateTaskEvaluate(taskEvaluate));
    }

    /**
     * 删除检测任务评定
     */
    @Log(title = "检测任务评定", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskEvaluateService.deleteTaskEvaluateByIds(ids));
    }

    /**
     * 查看评分结果
     */
    @GetMapping(value = "/getScoreInfo/{id}")
    public AjaxResult getScoreInfo(@PathVariable("id") Long id) {
        List<List<CheckEnum>> result = taskEvaluateService.getScoreInfo(id);
        return AjaxResult.success(result);
    }

    /**
     * 获取下载进度状态
     */
    @GetMapping("/getDownloadProgress")
    public AjaxResult getDownloadProgress() {
        // 创建包含用户ID的进度信息
        Map<String, Object> progressInfo = new HashMap<>();
        progressInfo.put("userId", getUserId());
        return AjaxResult.success(progressInfo);
    }


}
