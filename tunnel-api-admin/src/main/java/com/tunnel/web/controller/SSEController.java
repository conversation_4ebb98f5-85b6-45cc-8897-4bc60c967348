package com.tunnel.web.controller;

import cn.hutool.core.util.IdUtil;
import com.tunnel.SseClient;
import com.tunnel.common.annotation.Anonymous;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;

/**
 * 任务列表Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/highway/sse")
public class SSEController {

    @Resource
    private SseClient sseClient;

//    @CrossOrigin
    @GetMapping("/createSse")
    public SseEmitter createConnect(String userId) {
        return sseClient.createSse(userId);
    }

//    @CrossOrigin
    @GetMapping("/sendMsg")
    @ResponseBody
    public String sseChat(String userId) {
        for (int i = 0; i < 10; i++) {
            sseClient.sendMessage(userId,IdUtil.fastUUID());
        }
        return "ok";
    }

    /**
     * 关闭连接
     */
    @CrossOrigin
    @GetMapping("/closeSse")
    public void closeConnect(String uid ){
        sseClient.closeSse(uid);
    }
}
