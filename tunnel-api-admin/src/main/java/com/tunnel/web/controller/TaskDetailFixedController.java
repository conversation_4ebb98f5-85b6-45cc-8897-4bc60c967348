package com.tunnel.web.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.TaskDetailFixed;
import com.tunnel.service.TaskDetailFixedService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 整改信息 控制器
 */
@RestController
@RequestMapping("/highway/taskDetailFixed")
public class TaskDetailFixedController extends BaseController {
    
    @Resource
    private TaskDetailFixedService taskDetailFixedService;

    /**
     * 查询整改信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TaskDetailFixed taskDetailFixed) {
        startPage();
        List<TaskDetailFixed> list = taskDetailFixedService.selectTaskDetailFixedList(taskDetailFixed);
        return getDataTable(list);
    }

    /**
     * 获取整改信息详细信息
     */
    @GetMapping("/{detailId}")
    public AjaxResult getInfo(@PathVariable Long detailId) {
        return success(taskDetailFixedService.selectTaskDetailFixedByDetailId(detailId));
    }


    /**
     * 新增整改信息
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TaskDetailFixed taskDetailFixed) {
        return toAjax(taskDetailFixedService.insertTaskDetailFixed(taskDetailFixed));
    }

    /**
     * 修改整改信息
     */
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody TaskDetailFixed taskDetailFixed) {
        return toAjax(taskDetailFixedService.updateTaskDetailFixed(taskDetailFixed));
    }

    /**
     * 删除整改信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids) {
        return toAjax(taskDetailFixedService.deleteTaskDetailFixedByIds(ids));
    }
}