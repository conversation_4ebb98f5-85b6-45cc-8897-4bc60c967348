package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.domain.Road;
import com.tunnel.service.RoadService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 基础数据Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/highway/road")
public class RoadController extends BaseController {
    @Autowired
    private RoadService scRoadService;

    /**
     * 查询基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('highway:road:list')")
    @GetMapping("/list")
    public TableDataInfo list(Road road) {
        startPage();
        List<Road> list = scRoadService.selectScRoadList(road);
        return getDataTable(list);
    }

    /**
     * 查询基础数据列表-all
     */
    @GetMapping("/listAll")
    public List<Road> listAll() {
        List<Road> list = scRoadService.selectScRoadList(new Road());
        return list;
    }

    @GetMapping("/listAllCompany")
    public List<Road> listAllCompany() {
        List<Road> list = scRoadService.listAllCompany(new Road());
        return list;
    }

    /**
     * 导出基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('highway:road:export')")
    @Log(title = "基础数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Road road) {
        List<Road> list = scRoadService.selectScRoadList(road);
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        util.exportExcel(response, list, "基础数据数据");
    }

    /**
     * 获取基础数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('highway:road:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(scRoadService.selectScRoadById(id));
    }

    /**
     * 新增基础数据
     */
    @PreAuthorize("@ss.hasPermi('highway:road:add')")
    @Log(title = "基础数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Road road) {
        return toAjax(scRoadService.insertScRoad(road));
    }

    /**
     * 修改基础数据
     */
    @PreAuthorize("@ss.hasPermi('highway:road:edit')")
    @Log(title = "基础数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Road road) {
        return toAjax(scRoadService.updateScRoad(road));
    }

    /**
     * 删除基础数据
     */
    @PreAuthorize("@ss.hasPermi('highway:road:remove')")
    @Log(title = "基础数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scRoadService.deleteScRoadByIds(ids));
    }


    @PreAuthorize("@ss.hasPermi('highway:road:add')")
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/batchAdd", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult doUploadFile(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(scRoadService.batchAdd(file));
    }
}
