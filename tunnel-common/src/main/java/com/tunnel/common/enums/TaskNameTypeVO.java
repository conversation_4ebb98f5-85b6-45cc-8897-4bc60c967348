package com.tunnel.common.enums;

public enum TaskNameTypeVO {
    WEEKLY_CHECK_1("1", "周度一检"),
    WEEKLY_CHECK_2("2", "周度二检"),
    WEEKLY_CHECK_3("3", "周度三检"),
    WEEKLY_CHECK_4("4", "周度四检"),
    MONTHLY_CHECK("5", "月度检查"),
    SPECIAL_SURVEY("6", "专项普查"),
    QUARTERLY_CHECK("7", "季度检查");

    private final String code;
    private final String name;

    TaskNameTypeVO(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 通过日期字符串获取对应的检查名称
     *
     * @param date 日期格式为 "2025-04-1" 或 "2025-04"
     * @return 匹配的检查名称，匹配不到返回空字符串 ""
     */
    /**
     * 通过日期字符串获取对应的检查名称
     *
     * @param date 日期格式必须是 "2025-04-1" 形式
     * @return 匹配的检查名称，匹配不到返回空字符串 ""
     */
    public static String getInspectionName(String date) {
        // 确保格式是 "yyyy-MM-d"，如果缺少最后的 "-d"，直接返回空
        if (date == null || !date.matches("\\d{4}-\\d{2}-\\d")) {
            return "";
        }
        // 获取最后一位数字作为 code
        String lastChar = date.substring(date.length() - 1);
        for (TaskNameTypeVO type : values()) {
            if (type.getCode().equals(lastChar)) {
                return type.getName();
            }
        }
        return "";
    }
}