package com.tunnel.common.utils;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * <AUTHOR>
 */
@Slf4j
public class PinyinUtils {
    /**
     * 将字符串转换成拼音（小写，无音标），并过滤特殊符号
     */
    public static String toPinyin(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        StringBuilder pinyin = new StringBuilder();
        char[] chars = text.toCharArray();
        for (char c : chars) {
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]")) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]);
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error("汉字转拼音失败：{}", e.getMessage());
                }
            } else if (Character.isLetterOrDigit(c)) {
                // 保留字母和数字
                pinyin.append(c);
            } else if (c == '_' || c == '-') {
                // 允许下划线和连字符
                pinyin.append(c);
            }
        }
        return pinyin.toString();
    }
}