package com.tunnel.common.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.UUID;

@Slf4j
public class FileUtil {
    //windows系统临时文件存储路径
    private static String WINDOWS_TMP_PATH="E:\\tmp\\upload";

    //linux系统临时文件存储路径
    private static String LINUX_TMP_PATH="/tmp/files/";

    private FileUtil() {

    }
    public static String saveExcelFile(MultipartFile file) {
        //生成本地缓存路径
        String fileNameType = file.getOriginalFilename().split("\\.")[1];
        String localPath = getPath("excel", fileNameType, UUID.randomUUID().toString());
        File localFile = new File(localPath);
        try {
            file.transferTo(localFile);
        } catch (Exception e) {
            log.error("MultipartFile无法缓存到File,path:" + localPath, e);
        }
        return localPath;
    }

    public static String getPath(String dty, String suffix, String name) {
        //生成本地缓存路径
        String systemType=System.getProperty("os.name");
        String tmpFile = "";
        if(systemType.toLowerCase().startsWith("win")){
            tmpFile=WINDOWS_TMP_PATH;
        }else {
            tmpFile=LINUX_TMP_PATH;
        }
        if (StringUtils.isBlank(tmpFile)) {
            tmpFile = File.separator + "tmp" + File.separator;
        }
        tmpFile = tmpFile + File.separator + dty + File.separator;
        checkFilePath(tmpFile);
        String path = tmpFile + name + "." + suffix;
        return path;

    }


    /**
     * 判断文件夹是否存在
     * @param localFilePath
     */
    public static void checkFilePath(String localFilePath){
        File filePath = new File(localFilePath);
        if (!filePath.exists()) {
            //　mkdirs(): 创建多层目录
            filePath.mkdirs();
        }
    }
}
